{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        // {
        //     "name": "TransportMatchProvider",
        //     "request": "launch",
        //     "type": "dart"
        // },
        // {
        //     "name": "TransportMatchProvider (profile mode)",
        //     "request": "launch",
        //     "type": "dart",
        //     "flutterMode": "profile"
        // },
        // {
        //     "name": "TransportMatchProvider (release mode)",
        //     "request": "launch",
        //     "type": "dart",
        //     "flutterMode": "release"
        // },
        // {
        //     "name": "flutter-kronos_0.1.0+1",
        //     "cwd": "packages/flutter-kronos_0.1.0+1",
        //     "request": "launch",
        //     "type": "dart"
        // },
        // {
        //     "name": "flutter-kronos_0.1.0+1 (profile mode)",
        //     "cwd": "packages/flutter-kronos_0.1.0+1",
        //     "request": "launch",
        //     "type": "dart",
        //     "flutterMode": "profile"
        // },
        // {
        //     "name": "flutter-kronos_0.1.0+1 (release mode)",
        //     "cwd": "packages/flutter-kronos_0.1.0+1",
        //     "request": "launch",
        //     "type": "dart",
        //     "flutterMode": "release"
        // },
        // {
        //     "name": "pkg_dio",
        //     "cwd": "packages/pkg_dio",
        //     "request": "launch",
        //     "type": "dart"
        // },
        // {
        //     "name": "pkg_dio (profile mode)",
        //     "cwd": "packages/pkg_dio",
        //     "request": "launch",
        //     "type": "dart",
        //     "flutterMode": "profile"
        // },
        // {
        //     "name": "pkg_dio (release mode)",
        //     "cwd": "packages/pkg_dio",
        //     "request": "launch",
        //     "type": "dart",
        //     "flutterMode": "release"
        // },
        {
            "name": "TransportMatch staging",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug",
            "program": "lib/main_stg.dart",
        },
        {
            "name": "TransportMatchProvider local",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug",
            "program": "lib/main_local.dart",
        },
        {
            "name": "TransportMatchProvider",
            "request": "launch",
            "type": "dart",
            "flutterMode": "debug",
            "program": "lib/main.dart",
        },
        {
            "name": "TransportMatchProvider (release)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "program": "lib/main.dart",
        }
    ]
}