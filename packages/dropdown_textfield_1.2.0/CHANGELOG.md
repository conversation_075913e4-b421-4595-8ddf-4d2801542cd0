# Changelog

## 1.2.0
- 'WidgetStateProperty' not found - fixed

## 1.1.0
- Deprecated bug fixes
- README updated
- Search text customization
- RTL support added
- Animated controller dispose fix added

## 1.0.7
- Fixed clear validation error (#14)
- Autovalidate option added

## 1.0.6
- Fixed keyboard failed to show (#10)

## 1.0.5
- Option added to customize clear and dropdown icon properties
- `checkBoxProperty` added, now you can customize the default property of multiple checkbox style
- Fixed "InitialValue in multiSelection" bug (#9)

## 1.0.4
- Input decoration added for search textfield
- `singleController` and `multiController` renamed to `controller`

## 1.0.3
- Fixed animated GIF not displaying in pub page

## 1.0.1
- Fixed `keyboardSubscription` bug

## 1.0.0
- Added option to customize multiple dropdown okay button:
  - Color
  - Text and textStyle
- Added option to customize padding and text style of dropdown list tile
- Fixed setState bug on onChange function
- Added outside click to hide dropdown if textfield is hidden
- Updated to new version of Flutter (Flutter 3.0.0)

## 0.0.8
- Fixed single dropdown controller text clear function

## 0.0.7
- Added attribute to add space between textfield and list widget

## 0.0.6
- Added controller for dropdown

## 0.0.5
- Fixed state change issue

## 0.0.4
- Added attribute to hide clear suffix icon button from textfield
- Changed class name to `DropDownTextField`

## 0.0.3
- Bug fix

## 0.0.2
- Fixed bug ([#1](https://github.com/srtraj/dropdown_textfield/issues/1))

## 0.0.1
- First publication
