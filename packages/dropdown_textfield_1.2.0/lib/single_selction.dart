import 'package:flutter/material.dart';

import 'dropdown_textfield.dart';

class SingleSelection extends StatefulWidget {
  const SingleSelection(
      {Key? key,
      required this.dropDownList,
      required this.onChanged,
      required this.height,
      required this.enableSearch,
      required this.searchHeight,
      this.searchTextStyle,
      required this.searchFocusNode,
      required this.mainFocusNode,
      this.searchKeyboardType,
      required this.searchAutofocus,
      this.searchShowCursor,
      required this.mainController,
      required this.autoSort,
      required this.listTileHeight,
      this.onSearchTap,
      this.onSearchSubmit,
      this.listTextStyle,
      this.searchDecoration,
      required this.listPadding,
      this.clearIconProperty})
      : super(key: key);
  final List<DropDownValueModel> dropDownList;
  final ValueSetter onChanged;
  final double height;
  final double listTileHeight;
  final bool enableSearch;
  final double searchHeight;
  final TextStyle? searchTextStyle;
  final FocusNode searchFocusNode;
  final FocusNode mainFocusNode;
  final TextInputType? searchKeyboardType;
  final bool searchAutofocus;
  final bool? searchShowCursor;
  final TextEditingController mainController;
  final bool autoSort;
  final Function? onSearchTap;
  final Function? onSearchSubmit;
  final TextStyle? listTextStyle;
  final ListPadding listPadding;
  final InputDecoration? searchDecoration;
  final IconProperty? clearIconProperty;

  @override
  State<SingleSelection> createState() => _SingleSelectionState();
}

class _SingleSelectionState extends State<SingleSelection> {
  late List<DropDownValueModel> newDropDownList;
  late TextEditingController _searchCnt;
  late FocusScopeNode _focusScopeNode;
  late InputDecoration _inpDec;
  onItemChanged(String value) {
    setState(() {
      if (value.isEmpty) {
        newDropDownList = List.from(widget.dropDownList);
      } else {
        newDropDownList = widget.dropDownList
            .where(
                (item) => item.name.toLowerCase().contains(value.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  void initState() {
    _focusScopeNode = FocusScopeNode();
    _inpDec = widget.searchDecoration ?? InputDecoration();
    if (widget.searchAutofocus) {
      widget.searchFocusNode.requestFocus();
    }
    _focusScopeNode.requestFocus();
    newDropDownList = List.from(widget.dropDownList);
    _searchCnt = TextEditingController();
    if (widget.autoSort) {
      onItemChanged(widget.mainController.text);
      widget.mainController.addListener(() {
        if (mounted) {
          onItemChanged(widget.mainController.text);
        }
      });
    }
    super.initState();
  }

  @override
  void dispose() {
    _searchCnt.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.enableSearch)
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: TextField(
              style: widget.searchTextStyle,
              focusNode: widget.searchFocusNode,
              showCursor: widget.searchShowCursor,
              keyboardType: widget.searchKeyboardType,
              controller: _searchCnt,
              onTap: () {
                if (widget.onSearchTap != null) {
                  widget.onSearchTap!();
                }
              },
              decoration: _inpDec.copyWith(
                hintText: _inpDec.hintText ?? 'Search Here...',
                suffixIcon: GestureDetector(
                  onTap: () {
                    widget.mainFocusNode.requestFocus();
                    _searchCnt.clear();
                    onItemChanged("");
                  },
                  child: widget.searchFocusNode.hasFocus
                      ? InkWell(
                          child: Icon(
                            widget.clearIconProperty?.icon ?? Icons.close,
                            size: widget.clearIconProperty?.size,
                            color: widget.clearIconProperty?.color,
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ),
              onChanged: onItemChanged,
              onSubmitted: (val) {
                widget.mainFocusNode.requestFocus();
                if (widget.onSearchSubmit != null) {
                  widget.onSearchSubmit!();
                }
              },
            ),
          ),
        SizedBox(
          height: widget.height,
          child: Scrollbar(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: newDropDownList.length,
              itemBuilder: (BuildContext context, int index) {
                return SizedBox(
                  height: widget.listTileHeight,
                  child: Padding(
                    padding: EdgeInsets.only(
                        right: 10,
                        left: 10,
                        bottom: widget.listPadding.bottom,
                        top: widget.listPadding.top),
                    child: InkWell(
                        onTap: () {
                          widget.onChanged(newDropDownList[index]);
                        },
                        child: Text(newDropDownList[index].name,
                            style: widget.listTextStyle)),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
