import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

/// Custom app-bar
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Constructor
  const CustomAppBar({
    required this.title,
    this.leading,
    super.key,
    this.actions,
    this.centerTitle = true,
    this.canPop = true,
    this.onBackPress,
    this.horizontalPadding,
    this.verticalPadding,
    this.leadingWidth,
    this.backgroundColor,
  });

  /// app bar title
  final String title;

  /// app bar leading
  final Widget? leading;

  /// actions
  final List<Widget>? actions;

  /// center title
  final bool centerTitle;

  /// Can pop screen
  final bool canPop;

  /// Back press callback
  final VoidCallback? onBackPress;

  //Horizontal Padding
  final double? horizontalPadding;

  //Vertical Padding
  final double? verticalPadding;

  /// Leading Width
  final double? leadingWidth;

  /// Background Color
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return AppPadding.symmetric(
      horizontal: horizontalPadding ?? AppSize.appPadding,
      vertical: verticalPadding,
      child: AppBar(
        centerTitle: false,
        actions: actions,
        automaticallyImplyLeading: canPop,
        leadingWidth: leadingWidth ?? AppSize.w16,
        surfaceTintColor: Colors.transparent,
        backgroundColor: backgroundColor ?? AppColors.ffF8F9FA,
        leading: (canPop && context.canPop()) ||
                (onBackPress != null && leading == null)
            ? InkWell(
                onTap: onBackPress ??
                    () {
                      context.pop();
                    },
                child: Icon(
                  Icons.arrow_back,
                  // color: context.themeTextColors.text,
                  size: AppSize.sp20,
                ),
              )
            : (leading != null)
                ? leading
                : null,
        title: Text(
          title,
          style: context.textTheme.titleLarge
              ?.copyWith(fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
