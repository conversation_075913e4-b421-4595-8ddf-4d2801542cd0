import 'package:flutter/material.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/dashed_line.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/location_info_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';

class LocationWidget extends StatelessWidget {
  const LocationWidget({
    super.key,
    required this.title1,
    required this.date1,
    required this.title2,
    required this.date2,
    required this.startLatitude,
    required this.endLatitude,
    required this.startLongitude,
    required this.endLongitude,
    this.isStackIcon = false,
  });
  final String title1;
  final String date1;
  final String title2;
  final String date2;
  final String startLatitude;
  final String endLatitude;
  final String startLongitude;
  final String endLongitude;

  final bool isStackIcon;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      spacing: AppSize.w8,
      children: [
        Flexible(
          child: LocationInfo(
            title: title1,
            date: date1,
            latitude: startLatitude,
            longitude: startLongitude,
            icon: AppAssets.iconsCalender.image(
              height: AppSize.h10,
              color: AppColors.ff6C757D,
            ),
          ),
        ),
        Flexible(
          child: Stack(
            children: [
              SizedBox(
                width: AppSize.w100,
                height: AppSize.h25,
                child: FittedBox(
                  child: Row(
                    children: [
                      AppAssets.iconsLocationOrigin.image(
                        height: AppSize.h14,
                      ),
                      SizedBox(
                        width: AppSize.w72,
                        child: const DashedDivider(),
                      ),
                      AppAssets.iconsLocation.image(
                        height: AppSize.h14,
                      ),
                    ],
                  ),
                ),
              ),
              if (isStackIcon)
                Positioned(
                  right: AppSize.r5,
                  left: AppSize.r5,
                  bottom: AppSize.r5,
                  child: AppAssets.iconsCar
                      .image(height: AppSize.h18, fit: BoxFit.fitHeight),
                ),
            ],
          ),
        ),
        // Row(
        //   children: [
        //     AppAssets.iconsLocationOrigin.image(
        //       height: AppSize.h14,
        //     ),
        //     SizedBox(
        //       width: AppSize.w70,
        //       child: const DashedDivider(),
        //     ),
        //     AppAssets.iconsLocation.image(
        //       height: AppSize.h14,
        //     ),
        //   ],
        // ),
        Flexible(
          child: LocationInfo(
            title: title2,
            date: date2,
            latitude: endLatitude,
            longitude: endLongitude,
            icon: AppAssets.iconsCalender.image(
              height: AppSize.h10,
              color: AppColors.ff6C757D,
            ),
          ),
        ),
      ],
    );
  }
}
