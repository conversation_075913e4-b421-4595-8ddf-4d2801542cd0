import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

class AppDropdown extends StatelessWidget {
  const AppDropdown({
    required this.items,
    this.title,
    this.titleStyle,
    required this.controller,
    this.autoValidateMode = AutovalidateMode.onUserInteraction,
    this.validator,
    this.prefixIcon,
    this.hintText,
    this.shadow,
    this.labelText,
    this.inputFormatters,
    this.readOnly = false,
    this.keyboardType,
    this.subTitle,
    super.key,
    this.textAction,
    this.suffix,
    this.obscureText = false,
    this.suffixIcon,
    this.onChanged,
    this.inputBorder,
    this.fillColor = Colors.white,
    // this.focusNode,
    this.fontSize,
    this.contentHeight,
    this.borderRadius,
    this.style,
    this.contentWidth,
    this.hintStyle,
    this.borderSide,
    this.textAlignVertical,
    this.prefix,
    this.isDense,
    this.prefixIconConstraints,
    this.autofocus,
    this.suffixIconConstraints,
    this.dropDownItemCount,
    this.floatingLabelColor,
    this.autofillHints,
    this.textAlign = TextAlign.start,
    this.textColor,
    this.enableSearch = true,
  });
  final dynamic controller;
  final AutovalidateMode? autoValidateMode;
  final String? Function(String?)? validator;
  final String? title;
  final TextStyle? titleStyle;
  final void Function(dynamic)? onChanged;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final List<DropDownValueModel> items;
  final Widget? suffix;
  final BoxShadow? shadow;
  final String? hintText;
  final String? labelText;
  final Color? floatingLabelColor;
  final bool readOnly;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textAction;
  final List<TextInputFormatter>? inputFormatters;
  final InputBorder? inputBorder;
  // final FocusNode? focusNode;
  final double? fontSize;
  final double? contentHeight;
  final double? dropDownItemCount;
  final double? contentWidth;
  final double? borderRadius;
  final Color? fillColor;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final BorderSide? borderSide;
  final TextAlignVertical? textAlignVertical;
  final Widget? prefix;
  final bool? isDense;
  final BoxConstraints? prefixIconConstraints;
  final String? subTitle;
  final bool? autofocus;
  final BoxConstraints? suffixIconConstraints;
  final Iterable<String>? autofillHints;
  final TextAlign textAlign;
  final Color? textColor;
  final bool enableSearch;

  // final FocusNode _focusNode = FocusNode();
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Row(
            children: [
              Text(
                '  ${title!}',
                style: titleStyle ??
                    context.textTheme.titleMedium!.copyWith(
                      overflow: TextOverflow.ellipsis,
                      // color: AppColors.ff343A40,
                      fontWeight: FontWeight.w500,
                      fontSize: AppSize.sp14,
                      color: AppColors.black,
                    ),
              ),
            ],
          ),
        if (title != null) Gap(AppSize.h4),
        // ValueListenableBuilder(
        //     valueListenable: _isFocused,
        //     builder: (context, isFocused, child){
        //       return
        if (controller is SingleValueDropDownController)
          DropDownTextField(
            controller: controller,
            enableSearch: enableSearch,
            validator: validator,
            textFieldDecoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: contentWidth ?? AppSize.w12,
                vertical: contentHeight ?? AppSize.h10,
              ),
              errorMaxLines: 2,
              counterText: '',
              prefixIconConstraints: prefixIconConstraints,
              suffixIconConstraints: suffixIconConstraints,
              isDense: isDense ?? false,
              border: inputBorder ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      borderRadius ?? AppSize.r10,
                    ),
                    borderSide: borderSide ?? BorderSide.none,
                  ),
              prefixIcon: prefixIcon == null
                  ? null
                  : AppPadding(
                      left: AppSize.w14,
                      right: AppSize.w8,
                      child: prefixIcon,
                    ),
              prefix: prefix == null
                  ? null
                  : AppPadding(
                      left: AppSize.w14,
                      right: AppSize.w8,
                      child: prefix,
                    ),
              suffix: suffix == null
                  ? null
                  : AppPadding(
                      right: AppSize.w14,
                      left: AppSize.w8,
                      child: suffix,
                    ),
              suffixIcon: suffixIcon == null
                  ? null
                  : AppPadding(
                      right: AppSize.w14,
                      left: AppSize.w8,
                      child: suffixIcon,
                    ),
              hintText: hintText,
              labelText: labelText,
              floatingLabelStyle: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: AppColors.ffADB5BD,
                fontSize: fontSize ?? AppSize.sp14,
              ),
              labelStyle: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: AppColors.ffADB5BD,
                fontSize: fontSize ?? AppSize.sp14,
              ),
              hintStyle: hintStyle ??
                  context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppColors.ffADB5BD,
                    fontSize: fontSize ?? AppSize.sp14,
                  ),
              errorStyle: TextStyle(
                color: AppColors.errorColor,
                fontSize: fontSize ?? AppSize.sp12,
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w2,
                      color: AppColors.errorColor,
                    ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w1,
                      color: AppColors.ffDEE2E6,
                    ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      color: context.theme.primaryColor,
                      width: AppSize.w2,
                    ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w2,
                      color: AppColors.errorColor,
                    ),
              ),
              filled: fillColor != null,
              fillColor: fillColor ?? AppColors.ffF8F9FA,
            ),
            searchDecoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: contentWidth ?? AppSize.w12,
                vertical: contentHeight ?? AppSize.h10,
              ),
              border: inputBorder ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      borderRadius ?? AppSize.r10,
                    ),
                    borderSide: borderSide ?? BorderSide.none,
                  ),
              hintText: 'Search',
              labelStyle: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: AppColors.ffADB5BD,
                fontSize: fontSize ?? AppSize.sp14,
              ),
              hintStyle: hintStyle ??
                  context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppColors.ffADB5BD,
                    fontSize: fontSize ?? AppSize.sp14,
                  ),
              errorStyle: TextStyle(
                color: AppColors.errorColor,
                fontSize: fontSize ?? AppSize.sp12,
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w2,
                      color: AppColors.errorColor,
                    ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w1,
                      color: context.theme.primaryColor,
                    ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      color: context.theme.primaryColor,
                      width: AppSize.w2,
                    ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w2,
                      color: AppColors.errorColor,
                    ),
              ),
              filled: fillColor != null,
              fillColor: fillColor ?? AppColors.ffF8F9FA,
            ),
            dropDownList: items,
            onChanged: onChanged,
            autovalidateMode: autoValidateMode,
            keyboardType: keyboardType,
            textStyle: style ?? context.textTheme.headlineSmall,
            // textFieldFocusNode: widget.focusNode ?? _focusNode,
            listTextStyle:
                style ?? context.textTheme.headlineSmall?.copyWith(height: 2),
            listPadding: ListPadding(
              top: AppSize.sp6,
              bottom: AppSize.sp6,
            ),
            // searchAutofocus: false,
            searchTextStyle: context.textTheme.headlineSmall?.copyWith(
              // color: context.themeTextColors.text,
              fontSize: AppSize.sp14,
            ),
          ),

        if (controller is MultiValueDropDownController)
          DropDownTextField.multiSelection(
            controller: controller,
            submitButtonColor: context.theme.primaryColor,
            submitButtonTextStyle: context.textTheme.headlineMedium
                ?.copyWith(color: AppColors.white),
            validator: validator,
            textFieldDecoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: contentWidth ?? AppSize.w12,
                vertical: contentHeight ?? AppSize.h10,
              ),
              errorMaxLines: 2,
              counterText: '',
              prefixIconConstraints: prefixIconConstraints,
              suffixIconConstraints: suffixIconConstraints,
              isDense: isDense ?? false,
              border: inputBorder ??
                  OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      borderRadius ?? AppSize.r10,
                    ),
                    borderSide: borderSide ?? BorderSide.none,
                  ),
              prefixIcon: prefixIcon == null
                  ? null
                  : AppPadding(
                      left: AppSize.w14,
                      right: AppSize.w8,
                      child: prefixIcon,
                    ),
              prefix: prefix == null
                  ? null
                  : AppPadding(
                      left: AppSize.w14,
                      right: AppSize.w8,
                      child: prefix,
                    ),
              suffix: suffix == null
                  ? null
                  : AppPadding(
                      right: AppSize.w14,
                      left: AppSize.w8,
                      child: suffix,
                    ),
              suffixIcon: suffixIcon == null
                  ? null
                  : AppPadding(
                      right: AppSize.w14,
                      left: AppSize.w8,
                      child: suffixIcon,
                    ),
              hintText: hintText,
              labelText: labelText,
              floatingLabelStyle: TextStyle(
                fontSize: fontSize ?? AppSize.sp14,
                // color:
                //     widget.floatingLabelColor ?? context.themeTextColors.text,
              ),
              labelStyle: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: AppColors.ffADB5BD,
                fontSize: fontSize ?? AppSize.sp14,
              ),
              hintStyle: hintStyle ??
                  context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: AppColors.ffADB5BD,
                    fontSize: fontSize ?? AppSize.sp14,
                  ),
              errorStyle: TextStyle(
                color: AppColors.errorColor,
                fontSize: fontSize ?? AppSize.sp12,
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w2,
                      color: AppColors.errorColor,
                    ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w1,
                      color: context.theme.primaryColor,
                    ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      color: context.theme.primaryColor,
                      width: AppSize.w2,
                    ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  borderRadius ?? AppSize.r6,
                ),
                borderSide: borderSide ??
                    BorderSide(
                      width: AppSize.w2,
                      color: AppColors.errorColor,
                    ),
              ),
              filled: fillColor != null,
              fillColor: fillColor ?? AppColors.ffF8F9FA,
            ),
            dropDownList: items,
            onChanged: onChanged,
            autovalidateMode: autoValidateMode,
            textStyle: style ?? context.textTheme.headlineSmall,
            // textFieldFocusNode: widget.focusNode ?? _focusNode,
            listTextStyle:
                style ?? context.textTheme.headlineSmall?.copyWith(height: 2),
            listPadding: ListPadding(
              top: AppSize.sp6,
              bottom: AppSize.sp6,
            ),
          ),
        // ;

        // },
        // ),
        if (subTitle != null) Gap(AppSize.h3) else const SizedBox(),
        if (subTitle != null)
          AppPadding(
            left: AppSize.w10,
            child: Text(
              subTitle!,
              style: titleStyle ??
                  context.textTheme.titleMedium!.copyWith(
                    fontSize: AppSize.sp11,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                  ),
            ),
          ),
      ],
    );
  }
}
