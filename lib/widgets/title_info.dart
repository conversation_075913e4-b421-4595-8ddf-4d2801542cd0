import 'package:flutter/cupertino.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// Title info
class TitleInfoWidget extends StatelessWidget {
  /// Constructor
  const TitleInfoWidget({
    required this.title,
    required this.subTitle,
    this.subTitleColor,
    super.key,
    this.titleColor,
    this.subTitleFontWeight,
    this.titleFontWeight,
    this.titleFontSize,
    this.subTitleFontSize,
    this.spacing,
  });

  ///  title
  final String title;

  ///  title
  final Color? titleColor;

  ///  subTitle
  final String subTitle;

  ///  subTitle
  final Color? subTitleColor;

  ///  subTitle FontWeight
  final FontWeight? subTitleFontWeight;

  ///  subTitle FontWeight
  final FontWeight? titleFontWeight;

  ///  Title FontWeight
  final double? titleFontSize;

  ///  subTitle FontWeight
  final double? subTitleFontSize;

  ///  spacing between title and subTitle
  final double? spacing;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: spacing ?? 0,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: titleFontSize ?? AppSize.sp12,
            color: titleColor ?? AppColors.ffADB5BD,
            fontWeight: titleFontWeight ?? FontWeight.normal,
          ),
        ),
        Text(
          subTitle,
          style: TextStyle(
            fontSize: subTitleFontSize ?? AppSize.sp16,
            fontWeight: subTitleFontWeight ?? FontWeight.normal,
            color: subTitleColor ?? AppColors.black,
          ),
        ),
      ],
    );
  }
}
