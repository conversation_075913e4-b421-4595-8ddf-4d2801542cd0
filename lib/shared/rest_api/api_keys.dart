// ignore_for_file: public_member_api_docs

import 'package:flutter/foundation.dart' show immutable;

/// This class contains all keys name used to store values locally
@immutable
final class ApiKeys {
  const ApiKeys._();

  /// user parameters
  static const email = 'email';
  static const password = 'password';

  ///FCM token
  static const registrationId = 'registration_id';
  static const firstName = 'first_name';
  static const deviceId = 'device_id';

  /// ENUM
  static const deviceType = 'device_type';
  static const role = 'role';
  static const verificationCode = 'verification_code';

  /// ENUM
  static const resetType = 'reset_type';
  static const oldPassword = 'old_password';
  static const refresh = 'refresh';
  static const companyName = 'company_name';
  static const commercialName = 'commercial_name';
  static const taxId = 'tax_id';
  static const webPage = 'web_page';

  /// Equipments parameters
  static const name = 'name';

  /// ENUM
  static const equipmentType = 'equipment_type';
  static const version = 'version';
  static const slot = 'slot';
  static const plateNumber = 'plate_number';
  static const economicNumber = 'economic_number';
  static const insurancePolicyNumber = 'insurance_policy_number';
  static const images = 'images';
  static const limit = 'limit';
  static const offset = 'offset';
  static const brandId = 'brand_id';
  static const winch = 'winch';
  static const awsImageKeys = 'aws_image_keys';
  static const existingImagesId = 'existing_images_id';
  static const validity = 'validity';

  /// trip api
  static const latitude = 'latitude';
  static const longitude = 'longitude';
  static const start = 'start';
  static const end = 'end';
  static const equipment = 'equipment';
  static const startStopLocation = 'start_stop_location';
  static const endStopLocation = 'end_stop_location';
  static const driver = 'driver';
  static const spotAvail = 'spot_available_for_reservation';
  static const tripStartDate = 'trip_start_date';
  static const tripEndDate = 'trip_end_date';
  static const allowIntermediate = 'allow_intermediate_pickup';
  static const costPerKm = 'cost_per_kilometer';
  static const intermediatePoint = 'intermediate_pick_up_point';
  static const stopLocation = 'stop_location';
  static const estimatedArrival = 'estimated_arrival_date';
  static const stopLocationIndex = 'stop_location_index';
  static const distance = 'distance';
  static const totalTripDistance = 'total_trip_distance';
  static const lastSegmentDistance = 'last_segment_distance';
  static const saveRoute = 'save_route';
  static const startDate = 'start_date';
  static const endDate = 'end_date';
  static const customPoints = 'custom_points';
  static const street = 'street';
  // static const neighborhood = 'neighborhood';
  // static const city = 'city';
  // static const state = 'state';
  // static const country = 'country';
  static const postalCode = 'postal_code';
  // static const countryCode = 'country_code';

  /// Customer Support
  static const description = 'description';
  static const awaImageKeys = 'aws_image_keys';
}
