import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/env/env.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';

import 'package:transportmatch_provider/utils/logger.dart';

/// Refresh token API class
class RefreshTokenApi {
  /// Refresh token API call cancellation variable
  static CancelToken? refreshCancelToken;

  /// Refresh token API call
  static Future<void> refreshToken({
    required VoidCallback callback,
  }) async {
    try {
      refreshCancelToken?.cancel();
      refreshCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.refresh: Injector.instance<AppDB>().refreshToken,
      };
      (AppEnv().baseUrl + EndPoints.refreshToken).logFatal;
      data.logD;
      final apiRes = await Dio().post<Map<String, dynamic>>(
        AppEnv().baseUrl + EndPoints.refreshToken,
        data: data,
        cancelToken: refreshCancelToken,
      );
      apiRes.data.logFatal;
      apiRes.statusCode.logFatal;
      if (apiRes.statusCode?.clamp(200, 299) == apiRes.statusCode) {
        // ignore: lines_longer_than_80_chars, avoid_dynamic_calls
        Injector.instance<AppDB>().token =
            (apiRes.data?['data']['access'] ?? '').toString();
        // ignore: avoid_dynamic_calls
        Injector.instance<AppDB>().refreshToken =
            (apiRes.data?['data']['refresh'] ?? '').toString();
        callback.call();
      } else {
        await Injector.instance<AppDB>().logoutUser().whenComplete(() {
          // Navigate to login screen
          AppNavigationService.pushAndRemoveAllPreviousRoute(
            rootNavKey.currentContext!,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
        });
      }
    } catch (e) {
      '$e'.logTime;
      await Injector.instance<AppDB>().logoutUser().whenComplete(() {
        // Navigate to login screen
        AppNavigationService.pushAndRemoveAllPreviousRoute(
          rootNavKey.currentContext!,
          AppRoutes.authBase,
          isBaseRoute: true,
        );
      });
    }
  }
}
