import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/models/equipments_list_model.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/models/equipment_brand_list_model.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/models/equipment_data_list_model.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';

/// Equipments related API methods class
final class EquipmentsRepository {
  /// User account repository constructor
  const EquipmentsRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  ///Api call to get all Equipments
  Future<ApiResult<EquipmentDataListModel>> getEquipmentsByBrand(
    ApiRequest request,
  ) {
    return DioRequest<EquipmentDataListModel>(
      dio: dio,
      path: request.path!,
      listJsonMapper: EquipmentDataListModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  ///Api call to create Equipment
  Future<ApiResult<Map<String, dynamic>>> createEquipment(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call to get Equipments Brands
  Future<ApiResult<EquipmentBrandsListModel>> getEquipmentsBrands(
    ApiRequest request,
  ) {
    return DioRequest<EquipmentBrandsListModel>(
      dio: dio,
      path: request.path!,
      listJsonMapper: EquipmentBrandsListModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  ///Api call to update Equipment
  Future<ApiResult<Map<String, dynamic>>> updateEquipment(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  ///Api call to delete Equipment
  Future<ApiResult<Map<String, dynamic>>> deleteEquipment(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).delete();
  }

  ///Api call to get Equipment
  Future<ApiResult<EquipmentListDataModel>> getEquipments(
    ApiRequest request,
  ) {
    return DioRequest<EquipmentListDataModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: EquipmentListDataModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
