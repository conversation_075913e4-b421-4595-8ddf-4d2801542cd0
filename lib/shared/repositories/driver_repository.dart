import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/driver_list_page_for_provider/models/driver_list_model.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';

/// Equipments related API methods class
final class DriverRepository {
  /// User account repository constructor
  const DriverRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  ///Api call to get all drivers
  Future<ApiResult<DriverListModel>> getDrivers(
    ApiRequest request,
  ) {
    return DioRequest<DriverListModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: DriverListModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  ///Api call to create driver
  Future<ApiResult<Map<String, dynamic>>> createDriver(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call to create driver
  Future<ApiResult<Map<String, dynamic>>> deleteDriver(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).delete();
  }
}
