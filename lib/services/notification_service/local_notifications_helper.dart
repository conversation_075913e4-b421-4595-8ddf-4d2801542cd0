// import 'dart:convert';

// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:transportmatch_provider/services/notification_service/notification_helper.dart';

// /// Location Notification service helper class
// class LocalNotificationHelper {
//   LocalNotificationHelper._();

//   /// Location Notification service singleton
//   static final localNotificationHelper = LocalNotificationHelper._();

//   /// Location Notification service flutter local notification plugin
//   static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();

//   /// Location Notification service android notification channel
//   static const AndroidNotificationChannel channel = AndroidNotificationChannel(
//     'default_channel',
//     'Default Channel',
//     importance: Importance.high,
//   );

//   /// Location Notification service android initialization settings
//   static const AndroidInitializationSettings initializationSettingsAndroid =
//       AndroidInitializationSettings('@mipmap/ic_launcher');

//   /// Location Notification service ios initialization settings
//   static const DarwinInitializationSettings initializationSettingsDarwin =
//       DarwinInitializationSettings();

//   /// Location Notification service android notification details
//   static AndroidNotificationDetails androidNotificationDetails =
//       AndroidNotificationDetails(
//     channel.id,
//     channel.name,
//     visibility: NotificationVisibility.public,
//     importance: Importance.high,
//     enableLights: true,
//   );

//   /// Location Notification service ios notification details
//   static DarwinNotificationDetails darwinNotificationDetails =
//       const DarwinNotificationDetails(presentSound: true);

//   /// Location Notification service initialization entry point
//   Future<void> initialize() async {
//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.requestNotificationsPermission();

//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             IOSFlutterLocalNotificationsPlugin>()
//         ?.requestPermissions(
//           alert: true,
//           badge: true,
//           sound: true,
//         );

//     await flutterLocalNotificationsPlugin.initialize(
//       const InitializationSettings(
//         android: initializationSettingsAndroid,
//         iOS: initializationSettingsDarwin,
//       ),
//       onDidReceiveNotificationResponse: (details) {
//         if (details.payload != '') {
//           NotificationHelper.notificationOnTapHandler(
//             localData: details,
//             isLocal: true,
//           );
//         }
//       },
//     );

//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(channel);
//   }

//   /// Location Notification service show notification method to show notification
//   Future<void> showNotification(RemoteMessage remoteMessage) async {
//     await flutterLocalNotificationsPlugin.show(
//       remoteMessage.notification.hashCode,
//       remoteMessage.notification?.title,
//       remoteMessage.notification?.body,
//       NotificationDetails(
//         android: androidNotificationDetails,
//         iOS: darwinNotificationDetails,
//       ),
//       payload:
//           remoteMessage.data.isNotEmpty ? jsonEncode(remoteMessage.data) : null,
//     );
//   }
// }
