/// Message types for chat
enum MessageType {
  /// Text message
  text,

  /// Image message
  image,

  /// Video message
  video,

  /// Audio message
  audio,

  /// File message
  file,
}

/// Extension for MessageType
extension MessageTypeExtension on MessageType {
  /// Convert MessageType to string
  String get value {
    switch (this) {
      case MessageType.text:
        return 'TEXT';
      case MessageType.image:
        return 'IMAGE';
      case MessageType.video:
        return 'VIDEO';
      case MessageType.audio:
        return 'AUDIO';
      case MessageType.file:
        return 'FILE';
    }
  }
}
