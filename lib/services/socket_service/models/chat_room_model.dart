import 'package:flutter/foundation.dart' show immutable;

/// Chat room model
@immutable
class ChatRoom {
  /// Constructor
  const ChatRoom({
    required this.id,
    required this.bookingDetail,
    required this.chatRoomId,
    required this.isActive,
    required this.participants,
  });

  /// Factory constructor to create a ChatRoom from JSON
  factory ChatRoom.fromJson(Map<String, dynamic> json) {
    return ChatRoom(
      id: json['id'] as int,
      bookingDetail: json['booking_detail'] as int,
      chatRoomId: json['chat_room_id'] as String,
      isActive: json['is_active'] as bool,
      participants: (json['participants'] as List<dynamic>)
          .map((e) => Participant.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Room ID
  final int id;

  /// Booking detail ID
  final int bookingDetail;

  /// Chat room ID
  final String chatRoomId;

  /// Is active
  final bool isActive;

  /// Participants
  final List<Participant> participants;

  /// Convert ChatRoom to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'booking_detail': bookingDetail,
      'chat_room_id': chatRoomId,
      'is_active': isActive,
      'participants': participants.map((e) => e.toJson()).toList(),
    };
  }
}

/// Participant model
@immutable
class Participant {
  /// Constructor
  const Participant({
    required this.id,
    required this.userId,
    required this.isOnline,
    this.isActiveInRoom = false,
    required this.joinedAt,
  });

  /// Factory constructor to create a Participant from JSON
  factory Participant.fromJson(Map<String, dynamic> json) {
    return Participant(
      id: json['id'] as int,
      userId: json['user'] as int,
      isOnline: json['is_online'] as bool,
      isActiveInRoom: json['is_active_in_room'] as bool? ?? false,
      joinedAt: DateTime.parse(json['joined_at'] as String),
    );
  }

  /// Participant ID
  final int id;

  /// User ID
  final int userId;

  /// Is online
  final bool isOnline;

  /// Is active in room
  final bool isActiveInRoom;

  /// Joined at
  final DateTime joinedAt;

  /// Convert Participant to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': userId,
      'is_online': isOnline,
      'is_active_in_room': isActiveInRoom,
      'joined_at': joinedAt.toIso8601String(),
    };
  }
}

// /// Socket message model for chat messages
// @immutable
// class SocketMessage {
//   /// Constructor
//   const SocketMessage({
//     required this.id,
//     required this.chatRoom,
//     required this.sender,
//     required this.messageType,
//     this.message,
//     this.fileUrl,
//     required this.createdAt,
//     this.isRead = false,
//   });

//   /// Factory constructor to create a SocketMessage from JSON
//   factory SocketMessage.fromJson(Map<String, dynamic> json) {
//     return SocketMessage(
//       id: json['id'] as int,
//       chatRoom: json['chat_room'] as int,
//       sender: json['sender'] as int,
//       messageType:
//           MessageTypeExtension.fromString(json['message_type'] as String),
//       message: json['message'] as String?,
//       fileUrl: json['file_url'] as String?,
//       createdAt: DateTime.parse(json['created_at'] as String),
//       isRead: json['is_read'] as bool? ?? false,
//     );
//   }

//   /// Message ID
//   final int id;

//   /// Chat room ID
//   final int chatRoom;

//   /// Sender ID
//   final int sender;

//   /// Message type
//   final MessageType messageType;

//   /// Message content (optional for non-text messages)
//   final String? message;

//   /// File URL (for non-text messages)
//   final String? fileUrl;

//   /// Created at
//   final DateTime createdAt;

//   /// Is read
//   final bool isRead;

//   /// Convert SocketMessage to JSON
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'chat_room': chatRoom,
//       'sender': sender,
//       'message_type': messageType.value,
//       if (message != null) 'message': message,
//       if (fileUrl != null) 'file_url': fileUrl,
//       'created_at': createdAt.toIso8601String(),
//       'is_read': isRead,
//     };
//   }
// }
