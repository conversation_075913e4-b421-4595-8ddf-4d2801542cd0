import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/env/env.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/models/chat_messages_model.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/services/socket_service/enum/message_type_enum.dart';
import 'package:transportmatch_provider/services/socket_service/models/chat_room_model.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Socket service for handling real-time communication
class SocketService {
  /// Factory constructor to return the singleton instance
  factory SocketService() => _instance;

  /// Private constructor for singleton pattern
  SocketService._();

  /// Singleton instance
  static final SocketService _instance = SocketService._();

  /// Socket.io client instance
  io.Socket? _socket;

  /// Flag to check if socket is connected
  bool get isConnected => _socket?.connected ?? false;

  /// Flag to check if service is closed
  // bool _isClosed = false;

  /// Current active chat room
  ChatRoom? _currentChatRoom;

  /// Get current chat room
  ChatRoom? get currentChatRoom => _currentChatRoom;

  /// Stream controllers for different event types
  StreamController<Message> _messageController =
      StreamController<Message>.broadcast();

  /// Stream for new messages
  Stream<Message> get onMessage => _messageController.stream;

  /// Stream controller for connection status
  StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();

  /// Stream for connection status
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  /// Stream controller for chat room connection status
  StreamController<ChatRoom?> _chatRoomController =
      StreamController<ChatRoom?>.broadcast();

  /// Stream for chat room connection status
  Stream<ChatRoom?> get onChatRoomConnection => _chatRoomController.stream;

  void _initializeController() {
    _messageController = StreamController<Message>.broadcast();
    _connectionStatusController = StreamController<bool>.broadcast();
    _chatRoomController = StreamController<ChatRoom?>.broadcast();
  }

  /// Initialize socket connection
  Future<void> initialize() async {
    // if (_isClosed) return;

    try {
      final token = Injector.instance<AppDB>().token;

      if (token.isEmpty) {
        'Socket initialization failed: No authentication token available'.logE;
        return;
      }
      _initializeController();
      // Close existing socket if any
      await disconnect();

      // Initialize socket with options
      _socket = io.io(
        AppEnv().socketUrl,
        io.OptionBuilder()
            .setTransports(['websocket'])
            .disableAutoConnect()
            .enableForceNew()
            .setAuth(
              {
                'token': token,
                'device_id': Injector.instance<AppProvider>()
                    .deviceId, // Add device ID to auth
              },
            )
            // .setExtraHeaders({
            //   'token': token,
            //   'device_id': Injector.instance<AppProvider>()
            //       .deviceId, // Add device ID to headers
            // })
            .build(),
      );

      // Setup event listeners
      _setupEventListeners();

      // Connect to socket server
      await connect();

      'Socket initialized successfully'.logI;
    } catch (e) {
      'Socket initialization error: $e'.logE;
    }
  }

  /// Setup socket event listeners
  void _setupEventListeners() {
    if (_socket == null
        // || _isClosed
        ) {
      return;
    }

    // Connection events
    _socket!.onConnect((_) {
      'Socket connected'.logI;
      _connectionStatusController.add(true);
    });

    _socket!.onDisconnect((_) {
      'Socket disconnected'.logI;
      _connectionStatusController.add(false);
      _currentChatRoom = null;
    });

    _socket!.onConnectError((error) {
      'Socket connection error: $error'.logE;
      _connectionStatusController.add(false);
    });

    _socket!.onError((error) {
      'Socket error: $error'.logE;
    });

    _socket!.onAny((event, data) {
      if (kDebugMode) {
        print(
          '\x1B[6m\x1B[33mSocket event: $event\n\x1B[6m\x1B[32mData: $data\x1B[0m',
        );
      }
    });

    // Chat events
    _socket!.on('chat_connection_status', (data) {
      try {
        if (data is Map<String, dynamic>) {
          final status = data['status'] as bool;
          if (status && data.containsKey('chat_room')) {
            final chatRoom = ChatRoom.fromJson(
              data['chat_room'] as Map<String, dynamic>,
            );
            _currentChatRoom = chatRoom;
            _chatRoomController.add(chatRoom);
            'Chat room connected: ${chatRoom.id}'.logI;
          } else {
            _chatRoomController.add(null);
            'Failed to connect to chat room'.logE;
          }
        }
      } catch (e) {
        'Error parsing chat connection status: $e'.logE;
        _chatRoomController.add(null);
      }
    });

    _socket!.on('receive_message', (data) {
      try {
        if (data is Map<String, dynamic>) {
          final message = Message.fromJson(
            data,
          );
          _messageController.add(message);
          'Message received: ${message.id}'.logI;
        }
      } catch (e) {
        'Error parsing message: $e'.logE;
      }
    });

    _socket!.on('chat_room_screen_inactive_status', (data) {
      try {
        if (data is Map<String, dynamic>) {
          final status = data['status'] as bool;
          final message = data['message'] as String;
          'Chat room inactive status: $status, Message: $message'.logI;
        }
      } catch (e) {
        'Error parsing chat room inactive status: $e'.logE;
      }
    });
  }

  /// Connect to socket server
  Future<void> connect() async {
    if (_socket == null
        // || _isClosed
        ) {
      return;
    }

    try {
      if (!isConnected) {
        _socket!.connect();
      }
    } catch (e) {
      'Socket connection error: $e'.logE;
    }
  }

  /// Disconnect from socket server
  Future<void> disconnect() async {
    if (_socket == null) return;

    try {
      if (isConnected) {
        _socket!.disconnect();
      }
    } catch (e) {
      'Socket disconnection error: $e'.logE;
    }
  }

  /// Get chat connection
  Future<ChatRoom?> getChatConnection({
    required int receiverId,
    required int bookingDetailId,
  }) async {
    if (!isConnected
        //  || _isClosed
        ) {
      return null;
    }

    try {
      final deviceId = Injector.instance.get<AppProvider>().deviceId;
      final connectionData = {
        'receiver': receiverId,
        'booking_detail': bookingDetailId,
        'device_id': deviceId,
      };

      _socket!.emit('get_chat_connection', json.encode(connectionData));
      'Requested chat connection with receiver: $receiverId, booking: $bookingDetailId'
          .logI;

      // Wait for response via the stream
      final completer = Completer<ChatRoom?>();

      // Set up a subscription to get the response
      late StreamSubscription<ChatRoom?> subscription;
      subscription = onChatRoomConnection.listen((chatRoom) {
        if (!completer.isCompleted) {
          completer.complete(chatRoom);
          subscription.cancel();
        }
      });

      // Add a timeout
      Timer(const Duration(seconds: 10), () {
        if (!completer.isCompleted) {
          'Chat connection request timed out'.logE;
          completer.complete(null);
          subscription.cancel();
        }
      });

      return completer.future;
    } catch (e) {
      'Error getting chat connection: $e'.logE;
      return null;
    }
  }

  /// Mark chat room as inactive
  Future<bool> markChatRoomInactive(int chatRoomId) async {
    if (!isConnected
        // || _isClosed
        ) {
      return false;
    }

    try {
      _socket!.emit(
        'chat_room_screen_inactive',
        json.encode({
          'chat_room_id': chatRoomId,
          'device_id': Injector.instance<AppProvider>()
              .deviceId, // Add device ID to payload
        }),
      );
      'Marked chat room as inactive: $chatRoomId'.logI;
      return true;
    } catch (e) {
      'Error marking chat room as inactive: $e'.logE;
      return false;
    }
  }

  /// Active or In-active chat when app is in background
  Future<bool> markActiveOrInActiveChatForAppInBackground({
    required int chatRoomId,
    required bool isInBackground,
  }) async {
    if (!isConnected
        // || _isClosed
        ) {
      return false;
    }

    try {
      _socket!.emit(
        'active_or_inactive_chat_room_screen_in_background',
        json.encode({
          'chat_room_id': chatRoomId,
          'device_id': Injector.instance<AppProvider>().deviceId,
          'is_in_background': isInBackground,
        }),
      );
      'Marked Active Or InActive Chat For App In Background: $chatRoomId :: InBackground $isInBackground'
          .logI;
      return true;
    } catch (e) {
      'Marked Active Or InActive Chat For App In Background: $e'.logE;
      return false;
    }
  }

  /// Send a message
  Future<bool> sendMessage({
    required int chatRoomId,
    required String message,
    String? fileUrl,
    MessageType messageType = MessageType.text,
  }) async {
    if (!isConnected
        // || _isClosed
        ) {
      return false;
    }

    try {
      final messageData = <String, dynamic>{
        'chat_room': chatRoomId,
        'message_type': messageType.value,
      };

      // Add either message or file_url based on message type
      if (messageType == MessageType.text) {
        messageData['message'] = message;
      } else if (fileUrl.isNotEmptyAndNotNull) {
        messageData['file_url'] = fileUrl;
      } else {
        'Cannot send message: Missing required data for message type: ${messageType.value}'
            .logE;
        return false;
      }

      _socket!.emit('send_message', json.encode(messageData));
      'Message sent to chat room: $chatRoomId'.logI;
      return true;
    } catch (e) {
      'Error sending message: $e'.logE;
      return false;
    }
  }

  /// Handle app lifecycle changes
  void handleAppLifecycleState(AppLifecycleState state, int chatRoomId) {
    // if (_isClosed) return;

    try {
      log('============>STATE: >${state.name}');
      switch (state) {
        case AppLifecycleState.resumed:
          // App is in foreground
          markActiveOrInActiveChatForAppInBackground(
            chatRoomId: chatRoomId,
            isInBackground: false,
          );

        case AppLifecycleState.inactive:
        // App is inactive
        case AppLifecycleState.paused:
        // App is in background
        // Keep socket connected for background messages
        case AppLifecycleState.detached:
          // App is detached
          // disconnect();
          markActiveOrInActiveChatForAppInBackground(
            chatRoomId: chatRoomId,
            isInBackground: state == AppLifecycleState.paused,
          );

        case AppLifecycleState.hidden:
        // App is hidden (newer Flutter versions)
      }
    } catch (e) {
      'Error handling app lifecycle state: $e'.logE;
    }
  }

  /// Dispose resources
  void dispose() {
    // if (_isClosed) return;

    // _isClosed = true;

    try {
      disconnect();
      _socket?.dispose();
      _socket = null;

      _messageController.close();
      _connectionStatusController.close();
      _chatRoomController.close();

      'Socket service disposed'.logI;
    } catch (e) {
      'Error disposing socket service: $e'.logE;
    }
  }

  /// Convert string to MessageType
  static MessageType fromString(String value) {
    switch (value.toUpperCase()) {
      case 'TEXT':
        return MessageType.text;
      case 'IMAGE':
        return MessageType.image;
      case 'VIDEO':
        return MessageType.video;
      case 'AUDIO':
        return MessageType.audio;
      case 'FILE':
        return MessageType.file;
      default:
        return MessageType.text;
    }
  }
}
