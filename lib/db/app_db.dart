import 'dart:async';

import 'package:eraser/eraser.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/models/signup_and_signin_model.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// to store local data
class AppDB {
  AppDB._(this._box);
  static const _appDbBox = '_appDbBox';
  final Box<dynamic> _box;

  /// to get instance
  static Future<AppDB> getInstance() async {
    try {
      final box = await Hive.openBox<dynamic>(_appDbBox);
      return AppDB._(box);
    } catch (e) {
      final appDir = await getApplicationDocumentsDirectory();
      if (appDir.existsSync()) {
        appDir.deleteSync(recursive: true);
      }
      final box = await Hive.openBox<dynamic>(_appDbBox);
      return AppDB._(box);
    }
  }

  /// save value
  T getValue<T>(String key, {T? defaultValue}) =>
      _box.get(key, defaultValue: defaultValue) as T;

  /// save value
  Future<void> setValue<T>(String key, T value) => _box.put(key, value);

  /// to get user token
  String get token => getValue('token', defaultValue: '');

  ///to set user token
  set token(String update) => setValue('token', update);

  /// to get refresh token
  String get refreshToken => getValue('refreshToken', defaultValue: '');

  ///to set refresh token
  set refreshToken(String update) => setValue('refreshToken', update);

  ///Removes all user data except
  Future<void> logoutUser() async {
    try {
      await _box.clear();
      unawaited(Eraser.clearAllAppNotifications());
    } catch (e) {
      e.logFatal;
    }
  }

  /// to set internet status
  set internetStatus(String status) => setValue('internetStatus', status);

  /// to get internet status
  String get internetStatus =>
      getValue('internetStatus', defaultValue: 'connected');

  /// to check internet connection status is connected or not
  bool get isInternetConnected {
    return internetStatus == 'connected';
  }

  /// get language preference
  String get languageCode => getValue('languageCode', defaultValue: 'en');

  /// set language preference
  set languageCode(String update) => setValue('languageCode', update);

  /// get user information
  SignUpAndSignInModel? get userModel => getValue<dynamic>('userModel') != null
      ? SignUpAndSignInModel.fromJson(
          Map<String, dynamic>.from(getValue('userModel')),
        )
      : null;

  /// store user information
  set userModel(SignUpAndSignInModel? update) =>
      setValue('userModel', update?.toJson());

  /// get provider's information
  User? get providerInfoModel => getValue<dynamic>('providerInfoModel') != null
      ? User.fromJson(
          Map<String, dynamic>.from(getValue('providerInfoModel')),
        )
      : null;

  /// store user information
  set providerInfoModel(User? update) =>
      setValue('providerInfoModel', update?.toJson());
  // /// get remember me email
  // String? get email => getValue('email');

  // /// set remember me email
  // set email(String? update) => setValue('email', update);

  // /// get remember me password
  // String? get password => getValue('password');

  // /// set remember me password
  // set password(String? update) => setValue('password', update);
}
