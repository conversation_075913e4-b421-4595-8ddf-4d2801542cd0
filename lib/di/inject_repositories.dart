import 'package:flutter/foundation.dart' show immutable;
import 'package:get_it/get_it.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/repositories/driver_repository.dart';
import 'package:transportmatch_provider/shared/repositories/equipments_repository.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';

/// Use Case injection
@immutable
class RepositoryInjector {
  /// Construct
  RepositoryInjector(this.instance) {
    _init();
  }

  /// GetIt instance
  final GetIt instance;

  /// TODO : Remove the instanceName parameter when production server is in use
  void _init() {
    Injector.instance.isReady<AppDB>().then(
      (value) {
        instance.registerSingleton(AppProvider());
      },
    );
    Injector.instance.registerFactory(
      () => AccountRepository(dio: instance(instanceName: 'open')),
    );
    Injector.instance.registerFactory(
      () => DriverRepository(dio: instance(instanceName: 'open')),
    );
    Injector.instance.registerFactory(
      () => EquipmentsRepository(dio: instance(instanceName: 'open')),
    );
    Injector.instance.registerFactory(
      () => TripRepository(dio: instance(instanceName: 'open')),
    );
  }
}
