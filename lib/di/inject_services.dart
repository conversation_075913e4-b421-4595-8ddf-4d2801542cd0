import 'package:flutter/foundation.dart' show immutable;
import 'package:get_it/get_it.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/services/socket_service/socket_service.dart';

/// Services injector
@immutable
class ServicesInjector {
  /// Constructor
  ServicesInjector(this.instance) {
    _init();
  }

  /// GetIt instance
  final GetIt instance;

  void _init() {
    instance.registerSingletonAsync(AppDB.getInstance);
    instance.registerSingleton<SocketService>(SocketService());
  }
}
