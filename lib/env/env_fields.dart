import 'package:transportmatch_provider/env/env_types_enum.dart';

/// Both DebugEnv and ReleaseEnv must implement all these values
abstract class EnvFields {
  /// field name for BaseUrl
  abstract final String baseUrl;

  /// field name for Android google map key
  abstract final String googleMapAndroidKey;

  /// field name for iOS google map key
  abstract final String googleMapIosKey;

  /// field name for Socket URL
  abstract final String socketUrl;
}

/// Global variable for setting environment
late EnvTypes currentEnv;
