import 'package:transportmatch_provider/env/env.dart';

/// App environment model class
abstract class AppEnv implements EnvFields {
  /// constructor
  factory AppEnv() => _instance;

  static final AppEnv _instance = envInstance;

  /// instance getter
  static AppEnv envInstance = switch (currentEnv) {
    EnvTypes.staging => EnvStaging(),
    EnvTypes.production => EnvProd(),
    EnvTypes.local => EnvLocal(),
  };
}
