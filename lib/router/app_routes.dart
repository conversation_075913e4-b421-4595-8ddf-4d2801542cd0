/// App Routes constants
///
/// Naming convention:
/// - Use camelCase for route names
/// - For screen routes, use the format: moduleNameScreenName
/// - Path should be kebab-case version of the route name
///
/// Example:
/// - Route name: authLoginScreen
/// - Path: /auth/login-screen
/// - Widget: LoginScreen
class AppRoutes {
  // <---------------------------------------------- Initial routes ---------------------------------------------->
  static const String initialPath = '/';
  static const String initial = 'initial';

  // Auth module paths
  // <---------------------------------------------- Auth Base and Auth paths and screens ---------------------------------------------->
  // Auth routes
  static const String authBase = '/auth';
  static const String authSignupPath = 'signup';
  static const String authSignupProviderInfoPath = 'signup-provider-info';
  static const String authResetPasswordPath = 'reset-password';
  static const String authForgotPasswordPath = 'forgot-password';
  static const String authCheckOtpPath = 'check-otp';
  static const String authSignupSuccessPath = 'signup-success';
  static const String authAddStripeWebviewPath = 'add-stripe-webview';

  static const String authSignupScreen = 'signup-screen';
  static const String authSignupProviderInfoScreen =
      'signup-provider-info-screen';
  static const String authResetPasswordScreen = 'reset-password-screen';
  static const String authForgotPasswordScreen = 'forgot-password-screen';
  static const String authCheckOtpScreen = 'check-otp-screen';
  static const String authSignupSuccessScreen = 'signup-success-screen';
  static const String authAddStripeWebview = 'add-stripe-webview';

  // <---------------------------------------------- Home paths and screens ---------------------------------------------->
  // Home routes
  static const String homeBase = '/home';
  static const String homeLiveTripsPath = '/home/<USER>';
  static const String homeNewRoutePath = '/home/<USER>';
  static const String homeSearchAddressPath = '/home/<USER>';

  static const String homeLiveTripsScreen = 'live-trips-screen';
  static const String homeNewRouteScreen = 'new-route-screen';
  static const String homeSearchAddressPage = 'search-address-page';

  // <---------------------------------------------- Trips paths and screens ---------------------------------------------->
  // Trips routes
  static const String tripsBase = '/trips';
  static const String tripsAllShipmentsPath = '/trips/all-shipments';
  static const String tripsRouterPath = '/trips/routes';
  static const String tripsChatPath = '/trips/chat';
  static const String tripsBookingDetailPath = '/trips/booking-detail';
  static const String tripsChecklistPath = '/trips/checklist';
  static const String tripsAddChecklistPath = 'add-checklist';
  static const String tripsReportProblemPath = '/trips/report-problem';
  static const String tripsCarInfoPath = '/trips/car-info';
  static const String tripsAcceptedTripPath = '/trips/accepted-trip';
  static const String tripsExclusiveTripPath = '/trips/exclusive-trip';
  static const String tripsSendOfferPath = '/trips/send-offer';
  static const String tripsWaitingRequestedPath = '/trips/waiting-requested';
  static const String tripsExclusiveRequestedPath =
      '/trips/exclusive-requested';
  static const String tripsOfferPriceScreenPath = '/trips/offer-price-screen';

  static const String tripsAllShipmentsScreen = 'all-shipments-screen';
  static const String tripsRouterScreen = 'routes-screen';
  static const String tripsChatScreen = 'chat-screen';
  static const String tripsBookingDetailScreen = 'booking-detail-screen';
  static const String tripsChecklistScreen = 'checklist-screen';
  static const String tripsAddChecklistScreen = 'add-checklist-screen';
  static const String tripsReportProblemScreen = 'report-problem-screen';
  static const String tripsCarInfoPage = 'car-info-page';
  static const String tripsAcceptedTripScreen = 'accepted-trip-screen';
  static const String tripsExclusiveTripScreen = 'exclusive-trip-screen';
  static const String tripsSendOfferScreen = 'send-offer-screen';
  static const String tripsWaitingRequestedScreen = 'waiting-requested-screen';
  static const String tripsExclusiveRequestedScreen =
      'exclusive-requested-screen';
  static const String tripsOfferPriceScreen = 'offer-price-screen';

  // <---------------------------------------------- Notification paths and screens ---------------------------------------------->
  // Notification routes
  static const String notificationBase = '/notifications';

  // <---------------------------------------------- Profile paths and screens ---------------------------------------------->
  // Profile routes
  static const String profileBase = '/profile';
  static const String profileEditPath = '/profile/edit';
  static const String profileSavedRoutePath = '/profile/saved-route';
  static const String profileSetNewPasswordPath = '/profile/set-new-password';
  static const String profileCustomerSupportPath = '/profile/customer-support';
  static const String profileDriverListPath = '/profile/driver-list';
  static const String profileDriverAddPath = '/profile/driver-add';

  static const String profileEditScreen = 'edit-profile-screen';
  static const String profileSavedRouteScreen = 'saved-route-screen';
  static const String profileSetNewPasswordScreen = 'set-new-password-screen';
  static const String profileCustomerSupportScreen = 'customer-support-screen';
  static const String profileDriverListScreen = 'driver-list-screen';
  static const String profileDriverAddScreen = 'add-driver-screen';

  // <---------------------------------------------- Driver User paths and screens ---------------------------------------------->
  // Driver routes
  static const String driverBase = '/driver';
  static const String driverProfilePath = '/driver/profile';
  static const String driverProfileScreen = 'driver-profile-screen';
  static const String driverChecklistListPath = '/driver/checklist-list';
  static const String driverChecklistListScreen = 'driver-checklist-list-screen';
  static const String driverNotificationPath = '/driver/notification';
  static const String driverNotificationScreen = 'driver-notification-screen';

  // <---------------------------------------------- Equipment paths and screens ---------------------------------------------->
  // Equipment module paths
  static const String equipmentBase = '/equipment';
  // static const String equipmentListPath = 'equipment-list-screen';
  static const String equipmentAddUpdatePath =
      '/equipment/equipment-add-screen';

  // Equipment module route names
  // static const String equipmentListScreen = 'equipmentListScreen';
  static const String equipmentAddUpdateScreen = 'equipmentAddUpdateScreen';

  // <---------------------------------------------- Common paths and screens  ---------------------------------------------->
  static const String commonBase = '/common';
  static const String commonWebviewPath = 'webview';
  static const String commonWebviewScreen = 'webview-screen';
}
