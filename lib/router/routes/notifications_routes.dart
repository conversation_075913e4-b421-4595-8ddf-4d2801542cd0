import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/modules/notifications_module/notifications_page/notifications_page.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class NotificationsRoutes {
  static StatefulShellBranch buildNotificationsBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.notificationBase,
          builder: (context, state) => const NotificationsPage(),
        ),
      ],
    );
  }
}
