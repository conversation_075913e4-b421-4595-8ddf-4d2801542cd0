import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/home_page.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/create_trip_page.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/create_trip_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/search_address_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/search_address_page.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/live_trips_page.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class HomeRoutes {
  static StatefulShellBranch buildHomeBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.homeBase,
          builder: (context, state) => const HomePage(),
        ),
      ],
    );
  }

  static List<GoRoute> buildNestedHomeRoutes() {
    return [
      GoRoute(
        path: AppRoutes.homeLiveTripsPath,
        name: AppRoutes.homeLiveTripsScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const LiveTripsPage(),
      ),
      GoRoute(
        path: AppRoutes.homeNewRoutePath,
        name: AppRoutes.homeNewRouteScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as CreateTripParams;
          return CreateTripPage(
            newRouteParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.homeSearchAddressPath,
        name: AppRoutes.homeSearchAddressPage,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as SearchAddressParams;
          return AddressSearchPage(
            searchAddressParams: params,
          );
        },
      ),
    ];
  }
}
