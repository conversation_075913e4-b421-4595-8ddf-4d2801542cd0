import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/presentation/common_pages/car_info_page/car_info_page.dart';
import 'package:transportmatch_provider/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/accepted_trip_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/all_shipments_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/booking_detail_page/booking_detail_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/booking_detail_page/models/booking_detail_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/chat_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/models/chat_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/provider/chat_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/route_pages/model/router_request_param.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/route_pages/route_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/add_checklist_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/models/add_checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/checklist_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/models/checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/report_problem_page/models/report_problem_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/report_problem_page/report_problem_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/exclusive_requested_trip_offer_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/models/send_offer_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/pages/offer_price_page/models/offer_price_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/pages/offer_price_page/offer_price_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/send_offer_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/waiting_list_page/pages/waiting_requested_page/waiting_requested_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/trips_page.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class TripsRoutes {
  static StatefulShellBranch buildTripsBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.tripsBase,
          builder: (context, state) => const TripsPage(),
        ),
      ],
    );
  }

  static List<GoRoute> buildNestedTripsRoutes() {
    return [
      GoRoute(
        path: AppRoutes.tripsAllShipmentsPath,
        name: AppRoutes.tripsAllShipmentsScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as AllShipmentsParams;
          return AllShipmentsPage(
            allShipmentsParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsRouterPath,
        name: AppRoutes.tripsRouterScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as RouterRequestParam;
          return RoutePage(
            routerRequestParam: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsChatPath,
        name: AppRoutes.tripsChatScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ChatParams;
          return ChangeNotifierProvider(
            create: (context) => ChatProvider(
              receiverId: params.receiverId,
              bookingDetailId: params.bookingDetailId,
              chatType: params.chatType,
              customerChatRoomParameter: params.customerChatRoomParameter,
              updateChatModel: params.updateChatModel,
            ),
            child: ChatPage(
              chatParams: params,
            ),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsBookingDetailPath,
        name: AppRoutes.tripsBookingDetailScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as BookingDetailParams;
          return BookingDetailPage(
            bookingDetailParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsChecklistPath,
        name: AppRoutes.tripsChecklistScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ChecklistParams;
          return CheckListPage(
            checklistParams: params,
          );
        },
        routes: [
          GoRoute(
            path: AppRoutes.tripsAddChecklistPath,
            name: AppRoutes.tripsAddChecklistScreen,
            parentNavigatorKey: rootNavKey,
            builder: (context, state) {
              final params = state.extra! as AddChecklistParams;
              return AddChecklistPage(
                addChecklistParams: params,
              );
            },
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.tripsReportProblemPath,
        name: AppRoutes.tripsReportProblemScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ReportProblemParams;
          return ReportProblemPage(
            reportProblemParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsCarInfoPath,
        name: AppRoutes.tripsCarInfoPage,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as CarInfoParams;
          return CarInfoPage(
            carInfoParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsAcceptedTripPath,
        name: AppRoutes.tripsAcceptedTripScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const AcceptedTripPage(),
      ),
      GoRoute(
        path: AppRoutes.tripsExclusiveTripPath,
        name: AppRoutes.tripsExclusiveTripScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const Scaffold(
          body:
              Center(child: Text('Exclusive Trip Screen - Under Construction')),
        ),
      ),
      GoRoute(
        path: AppRoutes.tripsSendOfferPath,
        name: AppRoutes.tripsSendOfferScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as SendOfferParams;
          return SendOfferPage(
            sendOfferParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsWaitingRequestedPath,
        name: AppRoutes.tripsWaitingRequestedScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const WaitingRequestedPage(),
      ),
      GoRoute(
        path: AppRoutes.tripsExclusiveRequestedPath,
        name: AppRoutes.tripsExclusiveRequestedScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ExclusiveRequestedParams;
          return ExclusiveRequestedTripOfferPage(
            exclusiveRequestedParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsOfferPriceScreenPath,
        name: AppRoutes.tripsOfferPriceScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as OfferPriceParams;
          return OfferPricePage(
            offerPriceParams: params,
          );
        },
      ),
    ];
  }
}
