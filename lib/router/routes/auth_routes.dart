import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/add_stripe_web_view_page/add_stripe_webview_page.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/add_stripe_web_view_page/models/add_stripe_webview_params.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/check_otp_page.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/models/check_otp_params.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/forgot_password_page/forgot_password_page.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/login_page/login_page.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/reset_password_page/reset_password_page.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/signup_page.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/signup_provider_info_page/signup_provider_info_page.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/signup_successfully_page/models/signup_success_params.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/signup_successfully_page/signup_successfully_page.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class AuthRoutes {
  static ShellRoute buildAuthShellRoute() {
    return ShellRoute(
      builder: (context, state, child) => Scaffold(body: child),
      routes: [
        GoRoute(
          path: AppRoutes.authBase,
          builder: (context, state) => const LoginPage(),
          routes: [
            GoRoute(
              path: AppRoutes.authSignupPath,
              name: AppRoutes.authSignupScreen,
              builder: (context, state) => const SignupPage(),
            ),
            GoRoute(
              path: AppRoutes.authSignupProviderInfoPath,
              name: AppRoutes.authSignupProviderInfoScreen,
              builder: (context, state) => const SignupProviderInfoPage(),
            ),
            GoRoute(
              path: AppRoutes.authResetPasswordPath,
              name: AppRoutes.authResetPasswordScreen,
              builder: (context, state) {
                return const ResetPasswordPage();
              },
            ),
            GoRoute(
              path: AppRoutes.authForgotPasswordPath,
              name: AppRoutes.authForgotPasswordScreen,
              builder: (context, state) => const ForgotPasswordPage(),
            ),
            GoRoute(
              path: AppRoutes.authCheckOtpPath,
              name: AppRoutes.authCheckOtpScreen,
              builder: (context, state) {
                final params = state.extra! as CheckOtpParams;
                return CheckOtpPage(
                  checkOtpParams: params,
                );
              },
            ),
            GoRoute(
              path: AppRoutes.authSignupSuccessPath,
              name: AppRoutes.authSignupSuccessScreen,
              builder: (context, state) {
                final params = state.extra! as SignupSuccessParams;
                return SignupSuccessFullyPage(
                  signupSuccessParams: params,
                );
              },
            ),
            GoRoute(
              path: AppRoutes.authAddStripeWebviewPath,
              name: AppRoutes.authAddStripeWebview,
              builder: (context, state) {
                final params = state.extra! as AddStripeWebviewParams;
                return AddStripeWebViewPage(
                  addStripeWebviewParams:
                      params,
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}
