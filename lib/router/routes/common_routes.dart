import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/common_pages/car_info_page/models/webview_params.dart';
import 'package:transportmatch_provider/presentation/common_pages/webview_page/webview_page.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class CommonRoutes {
  static List<GoRoute> buildCommonRoutes() {
    return [
      GoRoute(
        path: '${AppRoutes.commonBase}/${AppRoutes.commonWebviewPath}',
        name: AppRoutes.commonWebviewScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as WebviewParams;
          return WebViewPage(
            url: params.url,
            finalUrl: params.finalUrl,
          );
        },
      ),
    ];
  }
}
