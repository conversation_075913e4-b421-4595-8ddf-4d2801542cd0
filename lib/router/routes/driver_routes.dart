import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/driver_checklist_page.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/driver_page.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/driver_profile_page/driver_profile_page.dart';
import 'package:transportmatch_provider/presentation/modules/notifications_module/notifications_page/notifications_page.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class DriverRoutes {
  static List<GoRoute> buildDriverRoutes() {
    return [
      GoRoute(
        path: AppRoutes.driverBase,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const DriverPage(),
        routes: [
          GoRoute(
            path: AppRoutes.driverProfilePath,
            name: AppRoutes.driverProfileScreen,
            parentNavigatorKey: rootNav<PERSON>ey,
            builder: (context, state) => const DriverProfilePage(),
          ),
          GoRoute(
            path: AppRoutes.driverNotificationPath,
            name: AppRoutes.driverNotificationScreen,
            parentNavigatorKey: rootNavKey,
            builder: (context, state) => const NotificationsPage(),
          ),
          GoRoute(
            path: AppRoutes.driverChecklistListPath,
            name: AppRoutes.driverChecklistListScreen,
            parentNavigatorKey: rootNavKey,
            builder: (context, state) => const DriverChecklistPage(),
          ),
        ],
      ),
    ];
  }
}
