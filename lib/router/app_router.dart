// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/presentation/initial_page.dart';
import 'package:transportmatch_provider/presentation/modules/bottom_bar_module/bottom_bar_page.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/driver_page.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/router/routes/auth_routes.dart';
import 'package:transportmatch_provider/router/routes/common_routes.dart';
import 'package:transportmatch_provider/router/routes/driver_routes.dart';
import 'package:transportmatch_provider/router/routes/equipment_routes.dart';
import 'package:transportmatch_provider/router/routes/home_routes.dart';
import 'package:transportmatch_provider/router/routes/notifications_routes.dart';
import 'package:transportmatch_provider/router/routes/profile_routes.dart';
import 'package:transportmatch_provider/router/routes/trips_routes.dart';
import 'package:transportmatch_provider/utils/enums.dart';

/// App Router class to manage all the routes in the application
class AppRouter {
  static final GoRouter router = GoRouter(
    navigatorKey: rootNavKey,
    initialLocation: AppRoutes.initialPath,
    // debugLogDiagnostics: true,
    routes: [
      // Initial route
      GoRoute(
        path: AppRoutes.initialPath,
        name: AppRoutes.initial,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const InitialPage(),
      ),

      // Auth routes (no bottom bar)
      AuthRoutes.buildAuthShellRoute(),

      // Common routes (no bottom bar)
      ...CommonRoutes.buildCommonRoutes(),

      // Driver routes (no bottom bar)
      ...DriverRoutes.buildDriverRoutes(),

      // Nested full-screen routes (no bottom bar)
      ...HomeRoutes.buildNestedHomeRoutes(),
      ...TripsRoutes.buildNestedTripsRoutes(),
      ...EquipmentRoutes.buildNestedEquipmentRoutes(),
      ...ProfileRoutes.buildNestedProfileRoutes(),

      // Bottom bar shell route for top-level screens
      StatefulShellRoute.indexedStack(
        parentNavigatorKey: rootNavKey,
        builder: (context, state, navigationShell) {
          final isProvider =
              Injector.instance<AppDB>().userModel?.user?.role?.toLowerCase() ==
                  UserType.Provider.name.toLowerCase();
          return isProvider
              ? BottomBarPage(navigationShell: navigationShell)
              : const DriverPage();
        },
        branches: [
          HomeRoutes.buildHomeBranch(),
          TripsRoutes.buildTripsBranch(),
          EquipmentRoutes.buildEquipmentBranch(),
          NotificationsRoutes.buildNotificationsBranch(),
          ProfileRoutes.buildProfileBranch(),
        ],
      ),
    ],
    redirect: (context, state) => null,
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('Error: ${state.error}'),
        ),
      ),
    ),
  );
}
//   /// GoRouter configuration
//   static final GoRouter router = GoRouter(
//     navigatorKey: rootNavKey,
//     initialLocation: AppRoutes.initialPath,
//     debugLogDiagnostics: true,
//     routes: [
//       // Initial route
//       GoRoute(
//         path: AppRoutes.initialPath,
//         name: AppRoutes.initial,
//         builder: (context, state) => const InitialScreen(),
//       ),

//       // Auth module routes
//       ShellRoute(
//         builder: (context, state, child) => child,
//         routes: [
//           GoRoute(
//             path: AppRoutes.authBase,
//             builder: (context, state) => const LoginScreen(),
//             routes: [
//               // GoRoute(
//               //   path: AppRoutes.authLoginPath,
//               //   name: AppRoutes.authLoginScreen,
//               //   builder: (context, state) => const LoginScreen(),
//               // ),
//               GoRoute(
//                 path: AppRoutes.authSignupPath,
//                 name: AppRoutes.authSignupScreen,
//                 builder: (context, state) => const SignupScreen(),
//               ),
//               GoRoute(
//                 path: AppRoutes.authSignupProviderInfoPath,
//                 name: AppRoutes.authSignupProviderInfoScreen,
//                 builder: (context, state) => const SignupProviderInfoScreen(),
//               ),
//               GoRoute(
//                 path: AppRoutes.authResetPasswordPath,
//                 name: AppRoutes.authResetPasswordScreen,
//                 builder: (context, state) {
//                   final email = state.extra as String;
//                   return ResetPasswordScreen(email: email ?? '');
//                 },
//               ),
//               GoRoute(
//                 path: AppRoutes.authForgotPasswordPath,
//                 name: AppRoutes.authForgotPasswordScreen,
//                 builder: (context, state) => const ForgotPasswordScreen(),
//               ),
//               GoRoute(
//                 path: AppRoutes.authCheckOtpPath,
//                 name: AppRoutes.authCheckOtpScreen,
//                 builder: (context, state) {
//                   final params = state.extra as Map<String, dynamic>? ?? {};
//                   return CheckOtpScreen(
//                     email: params['email'] as String? ?? '',
//                     isFromForgotPassword:
//                         params['isFromForgotPassword'] as bool? ?? false,
//                     isFromSignup: params['isFromSignup'] as bool? ?? false,
//                   );
//                 },
//               ),
//               GoRoute(
//                 path: AppRoutes.authSignupSuccessPath,
//                 name: AppRoutes.authSignupSuccessScreen,
//                 builder: (context, state) {
//                   final isFromLogin = state.extra as bool;
//                   return SignupSuccessFullyScreen(
//                     isFromLogin: isFromLogin ?? false,
//                   );
//                 },
//               ),
//               GoRoute(
//                 path: AppRoutes.authAddStripeWebviewPath,
//                 name: AppRoutes.authAddStripeWebview,
//                 builder: (context, state) {
//                   final url = state.extra as String;
//                   return AddStripeWebView(url: url ?? '');
//                 },
//               ),
//             ],
//           ),
//         ],
//       ),

//       //Common Trips Routes :
//       GoRoute(
//         path: AppRoutes.tripsAllShipmentsPath,
//         name: AppRoutes.tripsAllShipmentsScreen,
//         builder: (context, state) {
//           final tripId = state.extra as int;
//           return AllShipmentsScreen(tripId: tripId);
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsChatPath,
//         name: AppRoutes.tripsChatScreen,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};

//           final receiverId = params['receiverId'] as int? ?? 0;
//           final bookingDetailId = params['bookingDetailId'] as int? ?? 0;
//           final chatType = params['chatType'] as ChatType? ?? ChatType.customer;
//           final chatRoomParam =
//               params['customerChatRoomParameter'] as CustomerChatRoom;
//           final updateChatModel = params['updateChatModel'] as void Function({
//             int? chatRoomId,
//             bool? isActive,
//           });
//           final title = params['title'] as String? ?? 'Customer';

//           return ChangeNotifierProvider(
//             create: (context) => ChatProvider(
//               receiverId: receiverId,
//               bookingDetailId: bookingDetailId,
//               chatType: chatType,
//               customerChatRoomParameter: chatRoomParam,
//               updateChatModel: updateChatModel,
//             ),
//             child: ChatScreen(
//               title: title,
//               customerChatRoomParameter: chatRoomParam,
//             ),
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsBookingDetailPath,
//         name: AppRoutes.tripsBookingDetailScreen,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return BookingDetail(
//             booking: params['booking'] as BookingModel,
//             tripDetailsModel: params['tripDetailsModel'] as TripDetailsModel,
//           );
//         },
//       ),

//       GoRoute(
//         path: AppRoutes.tripsChecklistPath,
//         name: AppRoutes.tripsChecklistScreen,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return ChecklistScreen(
//             carInfo: params['carInfo'] as BookedCar? ?? BookedCar(),
//             clientName: params['clientName'] as String? ?? '',
//             booking: params['booking'] as BookingModel? ?? BookingModel(),
//             tripDetailsModel: params['tripDetailsModel'] as TripDetailsModel? ??
//                 TripDetailsModel(),
//           );
//         },
//         routes: [
//           GoRoute(
//             path: AppRoutes.tripsAddChecklistPath,
//             name: AppRoutes.tripsAddChecklistScreen,
//             builder: (context, state) {
//               final params = state.extra as Map<String, dynamic>? ?? {};
//               return AddChecklistScreen(
//                 carInfo: params['carInfo'] as BookedCar?,
//                 isFromNotification:
//                     params['isFromNotification'] as bool? ?? false,
//                 checkListId: params['checkListId'] as int?,
//                 clientName: params['clientName'] as String? ?? '',
//                 checkListProvider:
//                     params['checkListProvider'] as CheckListProvider,
//                 isExclusive: params['isExclusive'] as bool? ?? false,
//                 isAdd: params['isAdd'] as bool? ?? false,
//               );
//             },
//           ),
//         ],
//       ),

//       GoRoute(
//         path: AppRoutes.tripsReportProblemPath,
//         name: AppRoutes.tripsReportProblemScreen,
//         builder: (context, state) {
//           final tripId = state.extra as int;
//           return ReportProblemScreen(
//             tripId: tripId?.toString() ?? '',
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsCarInfoPath,
//         name: AppRoutes.tripsCarInfoPage,
//         builder: (context, state) {
//           final carDetail = state.extra as CarDetail;
//           return CarInfoPage(carDetail: carDetail ?? CarDetail());
//         },
//       ),

//       // Main app routes with StatefulShellRoute for bottom navigation
//       StatefulShellRoute.indexedStack(
//         builder: (context, state, navigationShell) {
//           // Check if user is provider or driver
//           final isProvider =
//               Injector.instance<AppDB>().userModel?.user?.role?.toLowerCase() ==
//                   UserType.Provider.name.toLowerCase();

//           // If user is a driver, redirect to driver screen
//           if (!isProvider) {
//             return const DriverScreen();
//           }

//           // Otherwise, show the bottom bar with navigation shell
//           return BottomBarScreen(navigationShell: navigationShell);
//         },
//         branches: [
//           // Home branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.homeBase,
//                 builder: (context, state) => const HomeScreen(),
//                 routes: [
//                   GoRoute(
//                     path: AppRoutes.homeLiveTripsPath,
//                     name: AppRoutes.homeLiveTripsScreen,
//                     builder: (context, state) => const LiveTripsScreen(),
//                   ),
//                   GoRoute(
//                     path: AppRoutes.homeNewRoutePath,
//                     name: AppRoutes.homeNewRouteScreen,
//                     builder: (context, state) {
//                       final params = state.extra as Map<String, dynamic>? ?? {};
//                       final isSetSavedRoute =
//                           params['isSetSavedRoute'] as bool? ?? false;
//                       final data = params['data'] as SavedRoute;
//                       return NewRouteScreen(
//                         isSetSavedRoute: isSetSavedRoute,
//                         data: data,
//                       );
//                     },
//                   ),
//                   GoRoute(
//                     path: AppRoutes.homeSearchAddressPath,
//                     name: AppRoutes.homeSearchAddressPage,
//                     builder: (context, state) {
//                       final addressSearch = state.extra as AddressSearch;
//                       return AddressSearch(
//                         createTripProvider: addressSearch?.createTripProvider ??
//                             CreateTripProvider(),
//                         title: addressSearch?.title ?? '',
//                         isDrop: addressSearch?.isDrop ?? false,
//                         onTap: addressSearch?.onTap ?? (address) {},
//                       );
//                     },
//                   ),
//                 ],
//               ),
//             ],
//           ),

//           // Trips branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.tripsBase,
//                 builder: (context, state) => const TripsScreen(),
//                 routes: [
//                   GoRoute(
//                     path: AppRoutes.tripsAcceptedTripPath,
//                     name: AppRoutes.tripsAcceptedTripScreen,
//                     builder: (context, state) => const AcceptedTripScreen(),
//                   ),
//                   GoRoute(
//                     path: AppRoutes.tripsExclusiveTripPath,
//                     name: AppRoutes.tripsExclusiveTripScreen,
//                     builder: (context, state) {
//                       return const Scaffold(
//                         body: Center(
//                           child: Text(
//                             'Exclusive Trip Screen - Under Construction',
//                           ),
//                         ),
//                       );
//                     },
//                   ),
//                   GoRoute(
//                     path: AppRoutes.tripsSendOfferPath,
//                     name: AppRoutes.tripsSendOfferScreen,
//                     builder: (context, state) {
//                       final requestedTripProvider =
//                           state.extra as RequestedTripProvider;
//                       return SendOfferScreen(
//                         requestedTripProvider ?? RequestedTripProvider(),
//                       );
//                     },
//                   ),
//                   GoRoute(
//                     path: AppRoutes.tripsWaitingRequestedPath,
//                     name: AppRoutes.tripsWaitingRequestedScreen,
//                     builder: (context, state) => const WaitingRequestedScreen(),
//                   ),
//                   GoRoute(
//                     path: AppRoutes.tripsExclusiveRequestedPath,
//                     name: AppRoutes.tripsExclusiveRequestedScreen,
//                     builder: (context, state) {
//                       final params = state.extra as Map<String, dynamic>? ?? {};
//                       return ExclusiveRequestedScreen(
//                         exclusiveBooking:
//                             params['exclusiveBooking'] as ExclusiveTrip,
//                         requestedTripProvider: params['requestedTripProvider']
//                             as RequestedTripProvider,
//                         tripDataProvider:
//                             params['tripDataProvider'] as TripDataProvider,
//                       );
//                     },
//                   ),
//                 ],
//               ),
//             ],
//           ),

//           // Equipment branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.equipmentBase,
//                 builder: (context, state) => const EquipmentScreen(),
//                 routes: [
//                   GoRoute(
//                     path: AppRoutes.equipmentAddUpdatePath,
//                     name: AppRoutes.equipmentAddUpdateScreen,
//                     builder: (context, state) {
//                       final equipmentData = state.extra as EquipmentDataModel;
//                       return AddAndUpdateEquipmentScreen(
//                         equipmentData: equipmentData,
//                       );
//                     },
//                   ),
//                 ],
//               ),
//             ],
//           ),

//           // Notification branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.notificationBase,
//                 builder: (context, state) => const NotificationsScreen(),
//               ),
//             ],
//           ),

//           // Profile branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.profileBase,
//                 builder: (context, state) => const ProfileScreen(),
//                 routes: [
//                   GoRoute(
//                     path: AppRoutes.profileEditPath,
//                     name: AppRoutes.profileEditScreen,
//                     builder: (context, state) => const EditProfileScreen(),
//                   ),
//                   GoRoute(
//                     path: AppRoutes.profileSavedRoutePath,
//                     name: AppRoutes.profileSavedRouteScreen,
//                     builder: (context, state) => const SavedRouteScreen(),
//                   ),
//                   GoRoute(
//                     path: AppRoutes.profileSetNewPasswordPath,
//                     name: AppRoutes.profileSetNewPasswordScreen,
//                     builder: (context, state) {
//                       final email = state.extra as String;
//                       return SetNewPasswordScreen(email: email ?? '');
//                     },
//                   ),
//                   GoRoute(
//                     path: AppRoutes.profileCustomerSupportPath,
//                     name: AppRoutes.profileCustomerSupportScreen,
//                     builder: (context, state) => const CustomerSupportScreen(),
//                   ),
//                   GoRoute(
//                     path: AppRoutes.profileDriverListPath,
//                     name: AppRoutes.profileDriverListScreen,
//                     builder: (context, state) =>
//                         const DriverListScreenForProvider(),
//                   ),
//                   GoRoute(
//                     path: AppRoutes.profileDriverAddPath,
//                     name: AppRoutes.profileDriverAddScreen,
//                     builder: (context, state) => const AddDriverScreen(),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ],
//       ),

//       // Driver module routes (outside of bottom navigation)
//       GoRoute(
//         path: AppRoutes.driverBase,
//         builder: (context, state) => const DriverScreen(),
//         routes: [
//           GoRoute(
//             path: AppRoutes.driverProfilePath,
//             name: AppRoutes.driverProfileScreen,
//             builder: (context, state) => const DriverProfileScreen(),
//           ),
//         ],
//       ),

//       // Common components (outside of main shell)
//       GoRoute(
//         path: '${AppRoutes.commonBase}/${AppRoutes.commonWebviewPath}',
//         name: AppRoutes.commonWebviewScreen,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return WebView(
//             url: params['url'] as String? ?? '',
//             finalUrl: params['finalUrl'] as String?,
//           );
//         },
//       ),
//     ],
//     redirect: (context, state) {
//       // Add any global redirects here if needed
//       return null;
//     },
//     errorBuilder: (context, state) => Scaffold(
//       body: Center(
//         child: Text('Error: ${state.error}'),
//       ),
//     ),
//   );
// }
