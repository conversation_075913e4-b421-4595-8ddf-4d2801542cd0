import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({
    super.key,
    required this.url,
    this.finalUrl,
  });
  final String url;
  final String? finalUrl;

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  WebViewController? controller;
  final ValueNotifier<bool> _isShowLoader = ValueNotifier(true);

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              _isShowLoader.value = true;
            },
            onPageFinished: (String url) {
              _isShowLoader.value = false;
            },
          ),
        )
        ..loadRequest(Uri.parse(widget.url));
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ValueListenableBuilder(
        valueListenable: _isShowLoader,
        builder: (context, isLoading, _) {
          return AppLoader(
            isShowLoader: isLoading,
            child: SafeArea(
              child: controller != null
                  ? WebViewWidget(controller: controller!)
                  : Center(
                      child: Lottie.asset(
                        AppAssets.animationLoaderAppLoader.path,
                        height: AppSize.h100,
                      ),
                    ),
            ),
          );
        },
      ),
    );
  }
}
