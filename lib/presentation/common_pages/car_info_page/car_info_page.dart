import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_image.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:widget_zoom/widget_zoom.dart';

class CarInfoPage extends StatelessWidget {
  const CarInfoPage({super.key, required this.carInfoParams});
  final CarInfoParams carInfoParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.ffF8F9FA,
      appBar: CustomAppBar(title: context.l10n.vehicles_info),
      body: AppPadding.symmetric(
        horizontal: AppSize.appPadding,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSize.w16,
            vertical: AppSize.h16,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
            color: AppColors.white,
          ),
          child: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              spacing: AppSize.h16,
              children: [
                Text(
                  context.l10n.vehicles_info,
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: AppSize.sp20,
                  ),
                ),
                _buildInfoRow(
                  title: context.l10n.vehicleBrand,
                  value: carInfoParams.carDetail.brand ??
                      carInfoParams.carDetail.car?.brand ??
                      '',
                ),
                _buildInfoRow(
                  title: context.l10n.vehicleModel,
                  value: carInfoParams.carDetail.model ??
                      carInfoParams.carDetail.car?.model ??
                      '',
                ),
                _buildInfoRow(
                  title: context.l10n.vehicleYear,
                  value: carInfoParams.carDetail.year ??
                      carInfoParams.carDetail.car?.year ??
                      '',
                ),
                if (carInfoParams.carDetail.size != null)
                  _buildInfoRow(
                    title: context.l10n.vehicleSize,
                    value: switch (carInfoParams.carDetail.size) {
                      1 => context.l10n.small.capitalized,
                      1.5 => context.l10n.medium.capitalized,
                      _ => context.l10n.pickup.capitalized,
                    },
                  ),
                _buildInfoRow(
                  title: context.l10n.vehicleSerialNumeric.capitalized,
                  value: carInfoParams.carDetail.serialNumber ?? '',
                ),
                if (carInfoParams.carDetail.carDescription?.isNotEmpty ?? false)
                  _buildInfoRow(
                    title: context.l10n.vehicleDescription.capitalized,
                    value: carInfoParams.carDetail.carDescription ?? '',
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      context.l10n.isWinchRequired,
                      style: context.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: AppSize.sp14,
                        color: AppColors.black,
                      ),
                    ),
                    CircleAvatar(
                      radius: 10,
                      backgroundColor:
                          (carInfoParams.carDetail.isWinchRequired ?? false)
                              ? Colors.green
                              : AppColors.red,
                      child: Icon(
                        (carInfoParams.carDetail.isWinchRequired ?? false)
                            ? Icons.check
                            : Icons.close,
                        size: 13,
                        color: AppColors.white,
                      ),
                    ),
                  ],
                ),
                if (carInfoParams.carDetail.images?.isNotEmpty ?? false)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.l10n.vehicleImages,
                        style: context.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          fontSize: AppSize.sp16,
                          color: AppColors.black,
                        ),
                      ),
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.only(top: AppSize.h10),
                        itemCount: carInfoParams.carDetail.images?.length ?? 0,
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          mainAxisSpacing: 10,
                          crossAxisSpacing: 10,
                        ),
                        itemBuilder: (context, index) {
                          final image =
                              carInfoParams.carDetail.images?[index].imageUrl;
                          return ClipRRect(
                            borderRadius: BorderRadius.circular(AppSize.r5),
                            child: ColoredBox(
                              color: AppColors.primaryColorLight.withValues(
                                alpha: 0.4,
                              ),
                              child: WidgetZoom(
                                heroAnimationTag: image ?? '',
                                zoomWidget: AppImage.network(
                                  image ?? '',
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required String title,
    required String value,
  }) {
    return AppTextFormField(
      title: title,
      controller: TextEditingController(text: value),
      readOnly: true,
      maxLine: 5,
    );
  }
}
