// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/models/signup_and_signin_model.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class AppProvider extends ChangeNotifier {
  AppProvider() {
    _networkConnectionChecker();
    AppCommonFunctions.getDeviceId().then(
      (value) => deviceId = value,
    );
    // NotificationHelper.initializeNotification();
  }

  // /// FCM token
  // String? fcmToken;

  // /// Device Id
  String? deviceId;

  bool isClosed = false;

  /// Locale
  Locale locale = Locale(Injector.instance<AppDB>().languageCode);

  /// Internet connection status subscription stream
  StreamSubscription<List<ConnectivityResult>>? _streamSubscriptionInternet;

  /// Cancel token for get user data
  CancelToken? getUserDataCancelToken;

  /// Loader
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);

  void _networkConnectionChecker() {
    try {
      _streamSubscriptionInternet?.cancel();
      _streamSubscriptionInternet =
          Connectivity().onConnectivityChanged.listen((status) {
        try {
          'onConnectivityChanged : $status'.logD;
          Injector.instance.isReady<AppDB>().then((value) {
            try {
              Injector.instance<AppDB>().internetStatus =
                  status.contains(ConnectivityResult.mobile) ||
                          status.contains(ConnectivityResult.wifi) ||
                          status.contains(ConnectivityResult.ethernet) ||
                          status.contains(ConnectivityResult.vpn) ||
                          status.contains(ConnectivityResult.other)
                      ? 'connected'
                      : 'disconnected';
            } catch (e) {
              '==>> _networkConnectionChecker inner error $e'.logE;
            }
          });
        } catch (e) {
          '==>> _networkConnectionChecker listener error $e'.logE;
        }
      });
    } catch (e) {
      '==>> _networkConnectionChecker error $e'.logE;
    }
  }

  void changeLocale(Locale locale) {
    if (isClosed) return;
    try {
      this.locale = locale;
      Injector.instance<AppDB>().languageCode = locale.languageCode;
      notifyListeners();
    } catch (e) {
      '==>> changeLocale error $e'.logE;
      e.toString().logE;
    }
  }

  Future<bool?> getUserData({required BuildContext context}) async {
    if (isClosed) return false;
    final db = Injector.instance<AppDB>();
    final userDetailData = db.userModel?.user?.userDetailData;
    bool? isDone = false;
    try {
      if (db.token.isNotEmptyAndNotNull &&
          (db.userModel != null &&
              db.userModel!.user != null &&
              (userDetailData != null ||
                  db.userModel?.user?.role?.toLowerCase() !=
                      UserType.Provider.name.toLowerCase()))) {
        getUserDataCancelToken?.cancel();
        getUserDataCancelToken = CancelToken();
        final request = ApiRequest(
          path: '${EndPoints.getUserInfo}${db.userModel?.user?.id}/',
          cancelToken: getUserDataCancelToken,
        );
        final res =
            await Injector.instance<AccountRepository>().getUserInfo(request);
        await res.when(
          success: (data) async {
            if (isClosed || (getUserDataCancelToken?.isCancelled ?? true)) {
              return;
            }
            final db = Injector.instance<AppDB>();
            final userDetailData = db.userModel?.user?.userDetailData;
            db.userModel = SignUpAndSignInModel(
              user: data.user,
              access: db.userModel?.access,
              refresh: db.userModel?.refresh,
            );
            if (db.token.isNotEmptyAndNotNull &&
                (db.userModel != null &&
                    db.userModel?.user != null &&
                    (userDetailData != null ||
                        db.userModel?.user?.role?.toLowerCase() !=
                            UserType.Provider.name.toLowerCase()))) {
              isDone = true;
              // if (userDetailData.commercialName.isNotEmptyAndNotNull &&
              //     userDetailData.companyName.isNotEmptyAndNotNull &&
              //     userDetailData.taxId.isNotEmptyAndNotNull) {
              // } else {
              //   isDone = null;
              // }
            } else {
              isDone = false;
            }
          },
          error: (exception) async {
            if (isClosed || (getUserDataCancelToken?.isCancelled ?? true)) {
              return;
            }
            // isShowLoader.value = false;
            '========================= $exception'.logE;
            exception.message.showErrorAlert();
            isDone = false;
          },
        );
      } else {
        isDone = false;
      }
    } catch (e) {
      if (isClosed || (getUserDataCancelToken?.isCancelled ?? true)) {
        return false;
      }
      // isShowLoader.value = false;
      e.toString().logE;
      isDone = false;
    }
    return isDone;
  }

  @override
  void dispose() {
    isClosed = true;
    _streamSubscriptionInternet?.cancel();
    getUserDataCancelToken?.cancel();
    isShowLoader.dispose();
    deviceId = null;
    super.dispose();
  }
}
