import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/common_pages/webview_page/webview_page.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class BottomBarProvider extends ChangeNotifier {
  BottomBarProvider(this.context) {
    final userData = Injector.instance<AppDB>().userModel;
    if (userData?.user?.role?.toLowerCase() ==
        UserType.Provider.name.toLowerCase()) {
      if ((userData?.user?.userDetailData?.isProfileCompleted ?? false) &&
          !(userData?.user?.userDetailData?.isAccountActivated ?? false)) {
        activateStripeAccountAPICall(context);
      }
      getUnreadNotificationCount();
    }
  }
  final BuildContext context;

  bool isClosed = false;
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// Api call activate stripe account
  CancelToken? activateStripeAccountCancelToken;
  Future<void> activateStripeAccountAPICall(BuildContext context) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      activateStripeAccountCancelToken?.cancel();
      activateStripeAccountCancelToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.activateStripeAccount,
        cancelToken: activateStripeAccountCancelToken,
      );
      final res = await Injector.instance<AccountRepository>()
          .activateStripeAccount(request);
      res.when(
        success: (data) {
          if (isClosed ||
              (activateStripeAccountCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          if ((data['onboarding_url']['url'] as String?).isNotEmptyAndNotNull) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WebViewPage(
                  url: data['onboarding_url']['url'] as String,
                ),
              ),
            );
          }
        },
        error: (exception) {
          if (isClosed ||
              (activateStripeAccountCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (activateStripeAccountCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  int unreadNotificationCount = 0;

  /// To reset unread notification count
  Future<void> resetUnreadNotificationCount() async {
    if (isClosed) return;
    try {
      unawaited(notificationMarkReaded());
      notify();
    } catch (e) {
      if (isClosed) return;
      e.toString().logE;
    }
  }

  CancelToken? getUnreadNotificationCountCancelToken;
  Future<void> getUnreadNotificationCount() async {
    if (isClosed) return;
    try {
      getUnreadNotificationCountCancelToken?.cancel();
      getUnreadNotificationCountCancelToken = CancelToken();
      final response = await Injector.instance<AccountRepository>()
          .getUnreadNotificationCount(
        ApiRequest(
          path: EndPoints.unreadNotificationCount,
          cancelToken: getUnreadNotificationCountCancelToken,
        ),
      );
      response.when(
        success: (data) {
          if (isClosed ||
              (getUnreadNotificationCountCancelToken?.isCancelled ?? true)) {
            return;
          }
          unreadNotificationCount =
              int.tryParse(data['unread'].toString()) ?? 0;
          notify();
        },
        error: (e) {
          if (isClosed ||
              (getUnreadNotificationCountCancelToken?.isCancelled ?? true)) {
            return;
          }
          e.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed ||
          (getUnreadNotificationCountCancelToken?.isCancelled ?? true)) {
        return;
      }
      'Exception: $e'.logE;
    }
  }

  CancelToken? notificationMarkReadedCancelToken;
  Future<void> notificationMarkReaded() async {
    if (isClosed) return;
    try {
      notificationMarkReadedCancelToken?.cancel();
      notificationMarkReadedCancelToken = CancelToken();
      final response =
          await Injector.instance<AccountRepository>().notificationMarkReaded(
        ApiRequest(
          path: EndPoints.notificationMarkReaded,
          cancelToken: notificationMarkReadedCancelToken,
        ),
      );
      response.when(
        success: (data) {
          if (isClosed ||
              (notificationMarkReadedCancelToken?.isCancelled ?? true)) {
            return;
          }
          unreadNotificationCount = 0;
          notify();
        },
        error: (e) {
          if (isClosed ||
              (notificationMarkReadedCancelToken?.isCancelled ?? true)) {
            return;
          }
          e.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed ||
          (notificationMarkReadedCancelToken?.isCancelled ?? true)) {
        return;
      }
      'Exception: $e'.logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    activateStripeAccountCancelToken?.cancel();
    getUnreadNotificationCountCancelToken?.cancel();
    notificationMarkReadedCancelToken?.cancel();
    isShowLoader.dispose();
    super.dispose();
  }
}
