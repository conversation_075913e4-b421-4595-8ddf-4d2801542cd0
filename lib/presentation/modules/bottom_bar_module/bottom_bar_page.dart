// ignore_for_file: public_member_api_docs

import 'package:firebase_notifications_handler/firebase_notifications_handler.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/presentation/modules/bottom_bar_module/provider/bottom_bar_provider.dart';
import 'package:transportmatch_provider/services/notification_service/notification_helper.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

class BottomBarPage extends StatelessWidget {
  const BottomBarPage({
    super.key,
    required this.navigationShell,
  });

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context) {
    return FirebaseNotificationsHandler(
      onTap: (details) =>
          NotificationHelper.notificationOnTapHandler(details.firebaseMessage),
      shouldHandleNotification: (p0) => true,
      child: ChangeNotifierProvider(
        create: (_) => BottomBarProvider(context),
        lazy: false,
        builder: (context, child) {
          return Scaffold(
            backgroundColor: AppColors.pageBGColor,
            body: navigationShell,
            bottomNavigationBar: ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppSize.r14),
                topRight: Radius.circular(AppSize.r14),
              ),
              child: BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                backgroundColor: AppColors.white,
                showSelectedLabels: false,
                showUnselectedLabels: false,
                selectedLabelStyle: const TextStyle(fontSize: 0),
                unselectedFontSize: 0,
                selectedFontSize: 0,
                unselectedLabelStyle: const TextStyle(fontSize: 0),
                currentIndex: navigationShell.currentIndex,
                onTap: (index) async {
                  // Handle notification badge reset
                  if (index == 3) {
                    await Provider.of<BottomBarProvider>(
                      context,
                      listen: false,
                    ).resetUnreadNotificationCount();
                  }

                  // Navigate using the shell
                  navigationShell.goBranch(
                    index,
                    // This is a state-preserving navigation
                    initialLocation: index == navigationShell.currentIndex,
                  );
                },
                items: <BottomNavigationBarItem>[
                  BottomNavigationBarItem(
                    icon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsHome.image(
                        scale: 3.6,
                        color: AppColors.ffADB5BD,
                      ),
                    ),
                    activeIcon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsHomeFill.image(
                        scale: 3.6,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    label: '',
                  ),
                  BottomNavigationBarItem(
                    icon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsTrip.image(
                        scale: 3.6,
                        color: AppColors.ffADB5BD,
                      ),
                    ),
                    activeIcon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsTripFill.image(
                        scale: 3.6,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    label: '',
                  ),
                  BottomNavigationBarItem(
                    icon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsEquipment.image(
                        scale: 3.6,
                        color: AppColors.ffADB5BD,
                      ),
                    ),
                    activeIcon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsEquipmentFill.image(
                        scale: 3.6,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    label: '',
                  ),
                  BottomNavigationBarItem(
                    icon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: Consumer<BottomBarProvider>(
                        builder: (context, bottomBarProvider, child) {
                          return Stack(
                            children: [
                              AppAssets.iconsNotification.image(
                                scale: 3.6,
                                color: AppColors.ffADB5BD,
                              ),
                              if (bottomBarProvider.unreadNotificationCount > 0)
                                Transform.translate(
                                  offset: const Offset(10, -10),
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: const BoxDecoration(
                                      color: AppColors.primaryColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Text(
                                      bottomBarProvider.unreadNotificationCount
                                          .toString(),
                                      style: const TextStyle(
                                        color: AppColors.white,
                                        fontSize: 10,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          );
                        },
                      ),
                    ),
                    activeIcon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsNotificationFill.image(
                        scale: 3.6,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    label: '',
                  ),
                  BottomNavigationBarItem(
                    icon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsProfile.image(
                        scale: 3.6,
                        color: AppColors.ffADB5BD,
                      ),
                    ),
                    activeIcon: AppPadding.symmetric(
                      vertical: AppSize.h8,
                      child: AppAssets.iconsProfileFill.image(
                        scale: 3.6,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    label: '',
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
