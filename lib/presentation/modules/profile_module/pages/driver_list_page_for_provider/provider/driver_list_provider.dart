// ignore_for_file: public_member_api_docs

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/driver_list_page_for_provider/models/driver_list_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/driver_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Driver Provider
class DriverListProvider extends ChangeNotifier {
  DriverListProvider() {
    getDriversApiCall(isWantShowLoader: true);
    // Remove ScrollController listener as we'll use SmartRefresher instead
  }
  bool isClosed = false;
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? getDriversCancelToken;
  CancelToken? deleteDriverCancelToken;

  // Add RefreshController
  final refreshController = RefreshController();

  DriverListModel? _driverListModel;
  DriverListModel? get driverListModel => _driverListModel;

  // Add getter for next URL to use with SmartRefresher
  String? get nextUrl => _driverListModel?.next;

  // DriverData? selectedDriver;
  final SingleValueDropDownController selectedDriver =
      SingleValueDropDownController();

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      e.toString().logE;
    }
  }

  Future<void> refreshDriverListData({bool isShowLoader = false}) async {
    if (isClosed) return;
    try {
      _driverListModel = null;
      await getDriversApiCall(isWantShowLoader: isShowLoader);
    } catch (e) {
      if (isClosed) return;
      e.toString().logE;
    }
  }

  Future<void> getDriversApiCall({
    String? nextUrl,
    bool isWantShowLoader = false,
    bool isPagination = false,
  }) async {
    if (isClosed) return;
    try {
      getDriversCancelToken?.cancel();
      getDriversCancelToken = CancelToken();
      final request = ApiRequest(
        path:isPagination
              ?  nextUrl ?? EndPoints.getDrivers 
              : EndPoints.getDrivers,
        cancelToken: getDriversCancelToken,
      );
      if (!isPagination && isWantShowLoader) {
        isShowLoader.value = true;
      }
      final res =
          await Injector.instance<DriverRepository>().getDrivers(request);
      if (!isPagination && isWantShowLoader) {
        isShowLoader.value = false;
      }
      await res.when(
        success: (data) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          if (_driverListModel != null && isPagination) {
            _driverListModel!.previous = data.previous;
            _driverListModel!.next = data.next;
            _driverListModel!.count = data.count;
            for (final element in data.results) {
              if (!(_driverListModel?.results
                      .any((val) => val.id == element.id) ??
                  false)) {
                _driverListModel?.results.add(element);
              }
            }
          } else {
            _driverListModel = data;
          }
          notify();
        },
        error: (exception) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      e.logE;
      // e.toString().showErrorAlert();
    }
  }

  Future<void> deleteDriverApiCall({
    required BuildContext context,
    required int id,
    required int index,
  }) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      deleteDriverCancelToken?.cancel();
      deleteDriverCancelToken = CancelToken();
      final request = ApiRequest(
        path: '${EndPoints.deleteDriver}/$id/delete/',
        cancelToken: deleteDriverCancelToken,
      );
      final res =
          await Injector.instance<DriverRepository>().deleteDriver(request);
      await res.when(
        success: (data) async {
          if (isClosed || (deleteDriverCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          driverListModel?.results.removeAt(index);
          notify();
          rootNavKey.currentContext!.l10n.driverDeletedSuccessfully
              .showSuccessAlert();
        },
        error: (exception) async {
          if (isClosed || (deleteDriverCancelToken?.isCancelled ?? true)) {
            return;
          }

          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (deleteDriverCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    getDriversCancelToken?.cancel();
    deleteDriverCancelToken?.cancel();
    selectedDriver.dispose();
    isShowLoader.dispose();
    refreshController.dispose(); // Dispose RefreshController
    _driverListModel = null;
    super.dispose();
  }
}
