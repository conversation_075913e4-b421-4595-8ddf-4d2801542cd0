// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/driver_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Add Driver Provider
class AddDriverProvider extends ChangeNotifier {
  bool isClosed = false;
  // TextEditingController userIdController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final GlobalKey<FormState> formKeyAddDriver = GlobalKey<FormState>();

  final ValueNotifier<bool> isPasswordShow = ValueNotifier(false);
  final ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? addDriverCancelToken;

  Future<void> addDriverApiCall({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      addDriverCancelToken?.cancel();
      addDriverCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.firstName: nameController.text.trim(),
        ApiKeys.email: emailController.text.trim(),
        ApiKeys.password: passwordController.text,
      };
      final request = ApiRequest(
        path: EndPoints.createDriver,
        data: data,
        cancelToken: addDriverCancelToken,
      );
      final res =
          await Injector.instance<DriverRepository>().createDriver(request);
      await res.when(
        success: (data) async {
          if (isClosed || (addDriverCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          AppNavigationService.pop(context, true);
          context.l10n.driverCreatedSuccessfully.showSuccessAlert();
        },
        error: (exception) async {
          if (isClosed || (addDriverCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (addDriverCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    addDriverCancelToken?.cancel();
    isShowLoader.dispose();
    isPasswordShow.dispose();
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }
}
