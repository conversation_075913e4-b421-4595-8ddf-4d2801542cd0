// ignore_for_file: public_member_api_docs

class DriverListModel {
  DriverListModel({
    required this.count,
    required this.next,
    required this.previous,
    required this.results,
  });

  factory DriverListModel.fromJson(Map<String, dynamic> json) {
    return DriverListModel(
      count: json['count'] as int? ?? -1,
      next: json['next'] as String? ?? '',
      previous: json['previous'] as String? ?? '',
      results: (json['results'] as List<dynamic>? ?? [])
          .map((result) => DriverData.fromJson(result as Map<String, dynamic>))
          .toList(),
    );
  }
  int count;
  String next;
  String previous;
  final List<DriverData> results;

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      'results': results.map((result) => result.toJson()).toList(),
    };
  }
}

class DriverData {
  DriverData({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.dateJoined,
    required this.lastLogin,
  });

  factory DriverData.fromJson(Map<String, dynamic> json) {
    return DriverData(
      id: json['id'] as int? ?? 0,
      firstName: json['first_name'] as String? ?? '',
      lastName: json['last_name'] as String? ?? '',
      email: json['email'] as String? ?? '',
      dateJoined: json['date_joined'] as String? ?? '',
      lastLogin: json['last_login'] as String? ?? '',
    );
  }
  final int id;
  final String firstName;
  final String lastName;
  final String email;
  final String dateJoined;
  final String lastLogin;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'date_joined': dateJoined,
      'last_login': lastLogin,
    };
  }
}
