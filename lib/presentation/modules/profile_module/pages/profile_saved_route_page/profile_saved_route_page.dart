// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:readmore/readmore.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/create_trip_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/provider/live_trips_provider.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/profile_saved_route_page/provider/saved_route_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:transportmatch_provider/widgets/location_widget.dart';

class ProfileSavedRoutePage extends StatelessWidget {
  const ProfileSavedRoutePage({super.key});

  @override
  Widget build(BuildContext context) {
  
    return ChangeNotifierProvider(
      create: (context) => SavedRouterProvider(),
      child: ChangeNotifierProvider(
        create: (context) => LiveTripsProvider(),
        child: Consumer<SavedRouterProvider>(
          builder: (context, savedRouteProvider, child) {
            return Scaffold(
              backgroundColor: AppColors.pageBGColor,
              appBar: CustomAppBar(
                title: context.l10n.savedRoute,
                actions: [
                  IconButton(
                    onPressed: () => AppNavigationService.pushNamed(
                      context,
                      AppRoutes.homeNewRouteScreen,
                      extra: CreateTripParams(
                        isSetSavedRoute: true,
                      ),
                    ),
                    icon: Icon(
                      Icons.add,
                      color: AppColors.primaryColor,
                      size: AppSize.sp24,
                    ),
                  ),
                ],
              ),
              body: ListenableBuilder(
                listenable: savedRouteProvider.isShowLoader,
                builder: (context, child) {
                  return AppLoader(
                    isShowLoader: savedRouteProvider.isShowLoader.value,
                    child: child!,
                  );
                },
                child: SmartRefresher(
                  controller: savedRouteProvider.refreshController,
                  onRefresh: () {
                    savedRouteProvider.getSavedTripList().whenComplete(
                      savedRouteProvider.refreshController.refreshCompleted,
                    );
                  },
                  child: savedRouteProvider.savedRouteList.value.isEmpty &&
                          !savedRouteProvider.isShowLoader.value
                      ? Center(
                          child: Text(context.l10n.noRoutesSavedYet),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: savedRouteProvider.savedRouteList.value.length,
                          padding: EdgeInsets.all(
                            AppSize.appPadding,
                          ),
                          itemBuilder: (context, index) {
                            final data = savedRouteProvider.savedRouteList.value[index];
                            return Container(
                              margin: EdgeInsets.only(
                                bottom: AppSize.sp16,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                  AppSize.r6,
                                ),
                                color: AppColors.white,
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(
                                  AppSize.sp16,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  spacing: AppSize.h20,
                                  children: [
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: GestureDetector(
                                        onTap: () => savedRouteProvider.deleteSaveRoute(
                                          data.id?.toString() ?? '',
                                        ),
                                        child: AppAssets.iconsDelete
                                            .image(height: AppSize.h20),
                                      ),
                                    ),
                                    LocationWidget(
                                      title1:
                                          data.startStopLocation?.fullAddress ??
                                              '',
                                      startLatitude:
                                          data.startStopLocation?.latitude ??
                                              '',
                                      startLongitude:
                                          data.startStopLocation?.longitude ??
                                              '',
                                      endLatitude:
                                          data.endStopLocation?.latitude ?? '',
                                      endLongitude:
                                          data.endStopLocation?.longitude ?? '',
                                      date1:
                                          data.tripStartDate?.monthDate ?? '',
                                      title2:
                                          data.endStopLocation?.fullAddress ??
                                              '',
                                      date2: data.tripEndDate?.monthDate ?? '',
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      spacing: AppSize.w4,
                                      children: [
                                        Flexible(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                l10n.originStockLocation
                                                    .upperCamelCase,
                                                style: context
                                                    .textTheme.titleMedium
                                                    ?.copyWith(
                                                  fontSize: AppSize.sp12,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors.ff6C757D,
                                                ),
                                              ),
                                              Gap(
                                                AppSize.h4,
                                              ),
                                              Text(
                                                data.startStopLocation
                                                        ?.fullAddress ??
                                                    '',
                                                style: context
                                                    .textTheme.titleMedium
                                                    ?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  color: AppColors.ff343A40,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Flexible(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                l10n.dropStockLocation
                                                    .upperCamelCase,
                                                style: context
                                                    .textTheme.titleMedium
                                                    ?.copyWith(
                                                  fontSize: AppSize.sp12,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors.ff6C757D,
                                                ),
                                              ),
                                              Gap(
                                                AppSize.h4,
                                              ),
                                              Text(
                                                data.endStopLocation
                                                        ?.fullAddress ??
                                                    '',
                                                style: context
                                                    .textTheme.titleMedium
                                                    ?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  color: AppColors.ff343A40,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          context.l10n.stops,
                                          style: context.textTheme.titleMedium
                                              ?.copyWith(
                                            fontSize: AppSize.sp12,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.ff6C757D,
                                          ),
                                        ),
                                        Gap(AppSize.h4),
                                        ReadMoreText(
                                          data.intermediatePickUpPoint
                                                  ?.map(
                                                    (e) =>
                                                        e.stopLocation
                                                            ?.fullAddress ??
                                                        '',
                                                  )
                                                  .toList()
                                                  .join(', ')
                                                  .replaceAll('.', '') ??
                                              '',
                                          style: context.textTheme.titleMedium
                                              ?.copyWith(
                                            fontSize: AppSize.sp14,
                                            color: AppColors.ff343A40,
                                            fontWeight: FontWeight.w400,
                                          ),
                                          trimLines: 3,
                                          trimMode: TrimMode.Line,
                                          trimCollapsedText:
                                              context.l10n.readMore,
                                          trimExpandedText:
                                              '  ${context.l10n.readLess}',
                                          moreStyle: context
                                              .textTheme.titleMedium
                                              ?.copyWith(
                                            fontSize: AppSize.sp14,
                                            color: AppColors.primaryColor,
                                            fontWeight: FontWeight.w400,
                                          ),
                                          lessStyle: context
                                              .textTheme.titleMedium
                                              ?.copyWith(
                                            fontSize: AppSize.sp14,
                                            color: AppColors.primaryColor,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
