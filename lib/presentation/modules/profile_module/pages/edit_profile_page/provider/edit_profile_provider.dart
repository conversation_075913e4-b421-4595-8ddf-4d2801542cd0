// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';

/// EditProfileProvider
class EditProfileProvider extends ChangeNotifier {
  bool isClosed = false;
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? updateProfileToken;

  final nameController = TextEditingController(
    text: Injector.instance<AppDB>().userModel?.user?.firstName,
  );
  final emailController = TextEditingController(
    text: Injector.instance<AppDB>().userModel?.user?.email,
  );
  final passwordController = TextEditingController(text: '********');
  final companyTextController = TextEditingController(
    text: Injector.instance<AppDB>()
            .userModel
            ?.user
            ?.userDetailData
            ?.companyName ??
        '',
  );
  final commercialTextController = TextEditingController(
    text: Injector.instance<AppDB>()
            .userModel
            ?.user
            ?.userDetailData
            ?.commercialName ??
        '',
  );
  final taxIdTextController = TextEditingController(
    text:
        Injector.instance<AppDB>().userModel?.user?.userDetailData?.taxId ?? '',
  );
  final webPageTextController = TextEditingController(
    text: Injector.instance<AppDB>().userModel?.user?.userDetailData?.webPage ??
        '',
  );
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  /// update user data
  Future<void> updateUserData({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      updateProfileToken?.cancel();
      updateProfileToken = CancelToken();
      final data = {ApiKeys.firstName: nameController.text.trim()};
      final request = ApiRequest(
        path:
            '${EndPoints.getUserInfo}${Injector.instance<AppDB>().userModel?.user?.id}/',
        data: data,
        cancelToken: updateProfileToken,
      );
      final res =
          await Injector.instance<AccountRepository>().updateUserInfo(request);
      await res.when(
        success: (data) async {
          if (isClosed || (updateProfileToken?.isCancelled ?? true)) {
            return;
          }
          Injector.instance<AppDB>().userModel = data;
          isShowLoader.value = false;
          AppNavigationService.pop(context, data.user);
        },
        error: (exception) async {
          if (isClosed || (updateProfileToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (updateProfileToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      // e.toString().showErrorAlert();
    }
  }

  @override
  void dispose() {
    isClosed = true;
    updateProfileToken?.cancel();
    isShowLoader.dispose();
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    companyTextController.dispose();
    commercialTextController.dispose();
    taxIdTextController.dispose();
    webPageTextController.dispose();
    super.dispose();
  }
}
