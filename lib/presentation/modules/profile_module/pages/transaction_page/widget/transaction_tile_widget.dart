// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

class TransactionTileWidget extends StatelessWidget {
  const TransactionTileWidget({
    this.fromName,
    this.time,
    this.price,
    this.isCompleted,
    super.key,
  });
  final String? fromName;
  final String? time;
  final String? price;
  final bool? isCompleted;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Container(
      padding: EdgeInsets.all(
        AppSize.w16,
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r6),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(
              AppSize.h12,
            ),
            decoration: BoxDecoration(
              color: (isCompleted ?? true)
                  ? AppColors.ff17BD8D.withValues(alpha: 0.1)
                  : AppColors.ffEF4770.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSize.r4),
            ),
            child: Icon(
              (isCompleted ?? true)
                  ? Icons.arrow_downward_rounded
                  : Icons.error_outline,
              color: (isCompleted ?? true)
                  ? AppColors.ff17BD8D
                  : AppColors.ffEF4770,
            ),
          ),
          Expanded(
            child: AppPadding.symmetric(
              horizontal: AppSize.w8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Text(
                    '${l10n.from} ${fromName ?? ''}',
                    maxLines: 2,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: AppSize.sp16,
                      color: AppColors.ff343A40,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Gap(AppSize.h2),
                  Text(
                    time ?? '00:00 AM',
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: AppSize.sp12,
                      color: AppColors.ffADB5BD,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Text(
                price?.smartFormat() ?? r'$00.00',
                maxLines: 2,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: AppSize.sp16,
                  color: AppColors.ff343A40,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Gap(AppSize.h2),
              Text(
                (isCompleted ?? true) ? l10n.completed : l10n.unsuccessful,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: AppSize.sp12,
                  color: (isCompleted ?? true)
                      ? AppColors.successColor
                      : AppColors.errorColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
