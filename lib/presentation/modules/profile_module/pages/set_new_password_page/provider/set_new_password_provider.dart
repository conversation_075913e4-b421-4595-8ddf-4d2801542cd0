// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class SetNewPasswordProvider extends ChangeNotifier {
  bool isClosed = false;
  final TextEditingController oldPasswordController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> formKeyResetPassword = GlobalKey<FormState>();

  final ValueNotifier<bool> isOldPasswordShow = ValueNotifier(false);
  final ValueNotifier<bool> isPasswordShow = ValueNotifier(false);
  final ValueNotifier<bool> isConfirmPasswordShow = ValueNotifier(false);
  final ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? resetPasswordCancelToken;

  Future<void> resetPasswordAPIcall({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      resetPasswordCancelToken?.cancel();
      resetPasswordCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.resetType: 'PROFILE_RESET_PASSWORD',
        ApiKeys.password: passwordController.text,
        ApiKeys.oldPassword: oldPasswordController.text,
      };
      final request = ApiRequest(
        path: EndPoints.resetPassword,
        data: data,
        cancelToken: resetPasswordCancelToken,
      );
      final res =
          await Injector.instance<AccountRepository>().restPassword(request);
      await res.when(
        success: (data) async {
          if (isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          AppNavigationService.pop(context);
          rootNavKey.currentContext!.l10n.passwordChangedSuccessfully
              .showSuccessAlert();
        },
        error: (exception) async {
          if (isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    resetPasswordCancelToken?.cancel();
    isOldPasswordShow.dispose();
    isPasswordShow.dispose();
    isConfirmPasswordShow.dispose();
    isShowLoader.dispose();
    oldPasswordController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }
}
