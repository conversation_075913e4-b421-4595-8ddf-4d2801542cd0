// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/common_pages/car_info_page/models/webview_params.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/models/signup_and_signin_model.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class ProfileProvider extends ChangeNotifier {
  ProfileProvider() {
    getUserData(context: rootNavKey.currentContext!);
  }

  bool isClosed = false;
  User? user = Injector.instance<AppDB>().userModel?.user;

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  CancelToken? logoutToken;
  CancelToken? deleteAccountToken;
  CancelToken? getToken;
  CancelToken? stripeConnectAccountLoginUrlToken;
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);

  /// logout account
  Future<void> logout() async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      logoutToken?.cancel();
      logoutToken = CancelToken();
      final deviceId = Injector.instance<AppProvider>().deviceId;
      Map<String, dynamic> data;
      data = {
        ApiKeys.deviceId: deviceId,
        'refresh_token': Injector.instance<AppDB>().refreshToken,
      };
      final request = ApiRequest(
        path: EndPoints.logout,
        data: data,
        cancelToken: logoutToken,
      );
      final res = await Injector.instance<AccountRepository>().logout(request);
      await res.when(
        success: (data) async {
          if (isClosed || (logoutToken?.isCancelled ?? true)) return;
          await Injector.instance<AppDB>().logoutUser();
          isShowLoader.value = false;
          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            rootNavKey.currentContext!,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
        },
        error: (exception) async {
          if (isClosed || (logoutToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          await Injector.instance<AppDB>().logoutUser();
          isShowLoader.value = false;
          // Replace all instances of:
          // await AppNavigationService.pushAndRemoveAllScreen(
          //   context,
          //   const LoginScreen(),
          // );

          // With:
          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            rootNavKey.currentContext!,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (logoutToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  /// get user data
  Future<void> getUserData({required BuildContext context}) async {
    if (isClosed) return;
    try {
      getToken?.cancel();
      getToken = CancelToken();
      final request = ApiRequest(
        path:
            '${EndPoints.getUserInfo}${Injector.instance<AppDB>().userModel?.user?.id}/',
        cancelToken: getToken,
      );
      final res =
          await Injector.instance<AccountRepository>().updateUserInfo(request);
      await res.when(
        success: (data) async {
          if (isClosed || (getToken?.isCancelled ?? true)) return;
          Injector.instance<AppDB>().userModel = data;
          isShowLoader.value = false;
        },
        error: (exception) async {
          if (isClosed || (getToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  /// delete account
  Future<void> deleteAccount() async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      deleteAccountToken?.cancel();
      deleteAccountToken = CancelToken();
      final request = ApiRequest(
        path:
            '${EndPoints.deleteAccount}${Injector.instance<AppDB>().userModel?.user?.id}/delete/',
        cancelToken: deleteAccountToken,
      );
      final res =
          await Injector.instance<AccountRepository>().deleteAccount(request);
      await res.when(
        success: (data) async {
          if (isClosed || (deleteAccountToken?.isCancelled ?? true)) return;
          await Injector.instance<AppDB>().logoutUser();
          isShowLoader.value = false;
          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            rootNavKey.currentContext!,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
        },
        error: (exception) async {
          if (isClosed || (deleteAccountToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (deleteAccountToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  Future<void> stripeConnectAccountLoginUrl(BuildContext context) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      stripeConnectAccountLoginUrlToken?.cancel();
      stripeConnectAccountLoginUrlToken = CancelToken();

      final request = ApiRequest(
        path: EndPoints.stripeConnectAccountLoginUrl,
        cancelToken: stripeConnectAccountLoginUrlToken,
      );

      final res = await Injector.instance<AccountRepository>()
          .stripeConnectAccountLoginUrl(request);
      res.when(
        success: (data) {
          if (isClosed ||
              (stripeConnectAccountLoginUrlToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          final url = data['stripe_login_link']['url'] as String?;
          if (url.isNotEmptyAndNotNull) {
            AppNavigationService.pushNamed(
              context,
              AppRoutes.commonWebviewScreen,
              extra: WebviewParams(
                url: url ?? '',
                finalUrl:
                    'https://staging.d171pkw491jul9.amplifyapp.com/providers/',
              ),
            );

            // Navigator.push(
            //   context,
            //   MaterialPageRoute(
            //     builder: (context) => WebView(url: url ?? ''),
            //   ),
            // );
          }
        },
        error: (exception) {
          if (isClosed ||
              (stripeConnectAccountLoginUrlToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed ||
          (stripeConnectAccountLoginUrlToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    logoutToken?.cancel();
    deleteAccountToken?.cancel();
    getToken?.cancel();
    stripeConnectAccountLoginUrlToken?.cancel();
    isShowLoader.dispose();
    super.dispose();
  }
}
