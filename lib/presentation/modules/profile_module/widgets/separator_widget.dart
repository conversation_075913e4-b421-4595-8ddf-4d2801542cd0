// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// Separator
class SeparatorWidget extends StatelessWidget {
  /// Separator
  const SeparatorWidget({required this.children, super.key, this.space});

  final List<Widget?> children;
  final double? space;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const ClampingScrollPhysics(),
      itemCount: children.length,
      itemBuilder: (context, index) {
        return children[index];
      },
      separatorBuilder: (context, index) => Gap(space ?? AppSize.h16),
    );
  }
}
