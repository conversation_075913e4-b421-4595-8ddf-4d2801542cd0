// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// CustomTileWidget
class CustomTileWidget extends StatelessWidget {
  /// CustomTileWidget
  const CustomTileWidget({
    this.name,
    super.key,
    this.onTap,
    this.customWidget,
    this.suffix,
    this.fontColor,
  });

  final String? name;
  final VoidCallback? onTap;
  final Widget? customWidget;
  final Widget? suffix;
  final Color? fontColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(AppSize.h16),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Expanded(
              child: customWidget ??
                  (name.isNotEmptyAndNotNull
                      ? Text(
                          name ?? '',
                          style: context.textTheme.bodyMedium?.copyWith(
                            fontSize: AppSize.sp16,
                            fontWeight: FontWeight.w400,
                            color: fontColor,
                          ),
                        )
                      : const SizedBox.shrink()),
            ),
            Gap(AppSize.h8),
            suffix ?? Icon(Icons.arrow_forward, size: AppSize.h20),
          ],
        ),
      ),
    );
  }
}
