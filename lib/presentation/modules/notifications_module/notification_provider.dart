import 'dart:convert' show jsonDecode;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/notifications_module/notifications_page/models/notification_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/models/add_checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/pages/offer_price_page/models/offer_price_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class NotificationProvider extends ChangeNotifier {
  NotificationProvider() {
    getNotification(isWantShowLoader: true);
  }

  bool isClosed = false;
  List<NotificationModel> notificationList = [];

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  bool isShowLoader = false;
  CancelToken? cancelToken;
  String? nextUrl;
  Future<void> getNotification({
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;
    cancelToken?.cancel();
    cancelToken = CancelToken();
    if (isWantShowLoader) {
      isShowLoader = true;
      notify();
    }
    try {
      final response =
          await Injector.instance<AccountRepository>().getNotification(
        ApiRequest(
          path: isPagination
              ? nextUrl ?? EndPoints.notifications
              : EndPoints.notifications,
          cancelToken: cancelToken,
        ),
      );
      if (isClosed) return;
      if (isWantShowLoader) {
        isShowLoader = false;
        notify();
      }
      response.when(
        success: (data) {
          if (isClosed || (cancelToken?.isCancelled ?? true)) {
            return;
          }
          nextUrl = data.next;
          if (!isPagination) notificationList.clear();
          final dummyList = notificationList + (data.results ?? []);
          notificationList = dummyList;
          notify();
        },
        error: (error) {
          if (isClosed || (cancelToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed) return;
      if (isWantShowLoader) {
        isShowLoader = false;
        notify();
      }
      if (cancelToken?.isCancelled ?? true) return;
      if (kDebugMode) '==>> $e <<=='.logE;
    }
  }

  Future<void> onNotificationTap({
    required Map<String, dynamic> notificationData,
    required BuildContext context,
    required String notificationType,
  }) async {
    final data = notificationData;
    '==>>> type: $data'.logE;
    switch (notificationType) {
      case AppStrings.checklist:
        await AppNavigationService.pushNamed(
          rootNavKey.currentContext!,
          AppRoutes.tripsAddChecklistScreen,
          extra: AddChecklistParams(
            checkListProvider: CheckListProvider(
              carId: (data[AppStrings.bookedCarId] as String?) ?? '',
            ),
            checkListId: int.tryParse(
              (data[AppStrings.relatedObjectId] as String?) ?? '',
            ),
            isFromNotification: true,
          ),
          // ChangeNotifierProvider(
          //   create: (context) => CheckListProvider(
          //     carId: (data[AppStrings.bookedCarId] as String?) ?? '',
          //   ),
          //   builder: (context, child) {
          //     return AddChecklistScreen(
          //       checkListProvider: Provider.of<CheckListProvider>(context),
          //       checkListId: int.tryParse(
          //         (data[AppStrings.relatedObjectId] as String?) ?? '',
          //       ),
          //     );
          //   },
          // ),
        );
      // final chatRoom = ChatRoom.fromJson(data);
      // Navigator.pushNamed(
      //   AppNavigator.navigatorKey.currentContext!,
      //   AppRoutes.chatScreen,
      //   arguments: chatRoom,
      // );
      case AppStrings.bookingDetailStr ||
            AppStrings.tripStr ||
            AppStrings.bookingStr:
        final booking = data[AppStrings.relatedObjectData] != null
            ? jsonDecode((data[AppStrings.relatedObjectData] as String?) ?? '')
            : null;
        if (booking is Map<String, dynamic> &&
            data[AppStrings.notToRedirect] == null) {
          '==>>> notification: $data \n ${data[AppStrings.bookingType]}'.logE;

          /// if notification is for exclusive trip, then show exclusive trip screen
          if (booking[AppStrings.bookingStatus] ==
              NotificationType.EXCLUSIVE.name) {
            if (booking[AppStrings.bookingType] == AppStrings.sentOfferStr) {
              await AppNavigationService.pushNamed(
                rootNavKey.currentContext!,
                AppRoutes.tripsOfferPriceScreen,
                extra: OfferPriceParams(
                  tripId: int.tryParse(
                    (data[AppStrings.relatedObjectId] as String?) ?? '',
                  ),
                ),
                // OfferPriceScreen(
                //   tripId: int.tryParse(
                //     (data[AppStrings.relatedObjectId] as String?) ?? '',
                //   ),
                // ),
              );
            } else {
              await AppNavigationService.pushNamed(
                rootNavKey.currentContext!,
                AppRoutes.tripsExclusiveRequestedScreen,
                extra: ExclusiveRequestedParams(
                  exclusiveBooking: ExclusiveTrip.fromJson(booking),
                  requestedTripProvider: RequestedTripProvider(),
                  tripDataProvider: TripDataProvider(),
                ),
                // MultiProvider(
                //   providers: [
                //     ChangeNotifierProvider(
                //       create: (context) => RequestedTripProvider(),
                //     ),
                //     ChangeNotifierProvider(
                //       create: (context) => TripDataProvider(),
                //     ),
                //   ],
                //   builder: (context, child) => ExclusiveRequestedScreen(
                //     exclusiveBooking: null,
                //     tripId: booking[AppStrings.bookingId] as int?,
                //     requestedTripProvider:
                //         Provider.of<RequestedTripProvider>(context),
                //     tripDataProvider: Provider.of<TripDataProvider>(context),
                //   ),
                //   // child: ExclusiveRequestedScreen(
                //   //   exclusiveBooking: null,
                //   //   tripId: booking[AppStrings.bookingId] as int?,
                //   //   requestedTripProvider: widget.requestedTripProvider,
                //   //   tripDataProvider: widget.tripDataProvider,
                //   // ),
                // ),
              );
            }

            /// if notification is for sent offer, then show offer price screen
          } else {
            await AppNavigationService.pushNamed(
              rootNavKey.currentContext!,
              AppRoutes.tripsAllShipmentsScreen,
              extra: AllShipmentsParams(
                tripId: booking[AppStrings.tripId] as int?,
                bookingId: booking[AppStrings.bookingId] as int?,
              ),
              // AllShipmentsScreen(
              //   tripId: booking[AppStrings.tripId] as int?,
              //   bookingId: booking[AppStrings.bookingId] as int?,
              // ),
            );
          }
        } else {
          await AppNavigationService.pushNamed(
            rootNavKey.currentContext!,
            AppRoutes.tripsAllShipmentsScreen,
            extra: AllShipmentsParams(
              tripId: int.tryParse(
                (data[AppStrings.relatedObjectId] as String?) ?? '',
              ),
            ),
            // {
            //   'tripId': int.tryParse(
            //     (data[AppStrings.relatedObjectId] as String?) ?? '',
            //   ),
            //   // 'reportId': int.tryParse(
            //   //   (data[AppStrings.reportId] as String?) ?? '',
            //   // ),
            // },
            // AllShipmentsScreen(
            //   tripId: int.tryParse(
            //     (data[AppStrings.relatedObjectId] as String?) ?? '',
            //   ),
            //   // reportId: int.tryParse(
            //   //   (data[AppStrings.reportId] as String?) ?? '',
            //   // ),
            // ),
          );
        }
      // related_object_data
    }
  }

  @override
  void dispose() {
    isClosed = true;
    cancelToken?.cancel();
    notificationList.clear();
    super.dispose();
  }
}
