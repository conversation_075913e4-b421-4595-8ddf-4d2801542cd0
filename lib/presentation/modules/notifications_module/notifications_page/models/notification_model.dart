import 'dart:convert';

class NotificationData {
  NotificationData({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory NotificationData.fromJson(Map<String, dynamic> json) =>
      NotificationData(
        count: json['count'] as int?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<NotificationModel>.from(
                (json['results'] as List?)?.map(
                      (x) =>
                          NotificationModel.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
      );
  final int? count;
  final String? next;
  final String? previous;
  final List<NotificationModel>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results == null
            ? []
            : List<dynamic>.from(results!.map((x) => x.toJson())),
      };
}

class NotificationModel {
  NotificationModel({
    this.id,
    this.user,
    this.title,
    this.description,
    this.notificationType,
    this.relatedObjectData,
    this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      NotificationModel(
        id: json['id'] as int?,
        user: json['user'] as int?,
        title: json['title'] as String?,
        description: json['description'] as String?,
        notificationType: json['notification_type'] as String?,
        relatedObjectData: json['related_object_data'] != null
            ? json['related_object_data'] is String
                ? jsonDecode(json['related_object_data'] as String)
                    as Map<String, dynamic>
                : json['related_object_data'] as Map<String, dynamic>
            : null,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
      );
  final int? id;
  final int? user;
  final String? title;
  final String? description;
  final String? notificationType;
  final Map<String, dynamic>? relatedObjectData;
  final DateTime? createdAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'user': user,
        'title': title,
        'description': description,
        'notification_type': notificationType,
        'related_object_data': relatedObjectData,
        'created_at': createdAt?.toIso8601String(),
      };
}
