import 'package:flutter/material.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// Notifications Widgets
class NotificationsWidgets extends StatelessWidget {
  /// Constructor
  const NotificationsWidgets({
    super.key,
    required this.icon,
    required this.time,
    required this.textNormal,
    required this.textBold,
  });

  /// for icon widget
  final Widget icon;

  /// for time
  final String time;

  /// for textSpan normal
  final String textNormal;

  /// for textSpan Bold
  final String textBold;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSize.h10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DecoratedBox(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(AppSize.r10),
            ),
            child: Padding(
              padding: EdgeInsets.all(AppSize.r10),
              child: icon,
            ),
          ),
          Flexible(
            fit: FlexFit.tight,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    textBold,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: AppSize.h2),
                    child: Text(
                      textNormal,
                      style: TextStyle(
                        fontSize: AppSize.sp12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      time,
                      style: TextStyle(
                        fontSize: AppSize.sp14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
