class DriverDropDownModel {
  DriverDropDownModel({
    required this.id,
    required this.firstName,
    required this.lastName,
  });

  factory DriverDropDownModel.fromJson(Map<String, dynamic> json) {
    return DriverDropDownModel(
      id: json['id'] as int?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
    );
  }

  final int? id;
  final String? firstName;
  final String? lastName;

  Map<String, dynamic> toJson() => {
        'id': id,
        'first_name': firstName,
        'last_name': lastName,
      };
}
