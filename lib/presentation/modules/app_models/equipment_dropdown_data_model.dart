class EquipmentDropDownModel {
  EquipmentDropDownModel({
    required this.id,
    required this.name,
    required this.provider,
    required this.plateNumber,
    required this.slot,
    required this.economicNumber,
    required this.insurancePolicyNumber,
    required this.winch,
  });

  factory EquipmentDropDownModel.fromJson(Map<String, dynamic> json) {
    return EquipmentDropDownModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      provider: json['provider'] as int?,
      plateNumber: json['plate_number'] as String?,
      slot: json['slot'] as int?,
      economicNumber: json['economic_number'] as String?,
      insurancePolicyNumber: json['insurance_policy_number'] as String?,
      winch: json['winch'] as bool?,
    );
  }

  final int? id;
  final String? name;
  final int? provider;
  final String? plateNumber;
  final int? slot;
  final String? economicNumber;
  final String? insurancePolicyNumber;
  final bool? winch;

  EquipmentDropDownModel copyWith({
    int? id,
    String? name,
    int? provider,
    String? plateNumber,
    int? slot,
    String? economicNumber,
    String? insurancePolicyNumber,
    bool? winch,
  }) {
    return EquipmentDropDownModel(
      id: id ?? this.id,
      name: name ?? this.name,
      provider: provider ?? this.provider,
      plateNumber: plateNumber ?? this.plateNumber,
      slot: slot ?? this.slot,
      economicNumber: economicNumber ?? this.economicNumber,
      insurancePolicyNumber:
          insurancePolicyNumber ?? this.insurancePolicyNumber,
      winch: winch ?? this.winch,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'provider': provider,
        'plate_number': plateNumber,
        'slot': slot,
        'economic_number': economicNumber,
        'insurance_policy_number': insurancePolicyNumber,
        'winch': winch,
      };
}
