// ignore_for_file: public_member_api_docs
import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/widgets/marqee_widget.dart';

class EquipmentTypeRadioWidget extends StatelessWidget {
  const EquipmentTypeRadioWidget({
    required this.groupValue,
    required this.value,
    this.equipmentTypeName,
    this.onChanged,
    this.equipmentImage,
    super.key,
  });

  final EquipmentType groupValue;
  final EquipmentType value;
  final void Function(EquipmentType?)? onChanged;
  final Widget? equipmentImage;
  final String? equipmentTypeName;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: GestureDetector(
        onTap: () {
          if (onChanged != null) {
            onChanged?.call(value);
          }
        },
        child: Column(
          children: [
            Row(
              children: [
                Radio<EquipmentType>(
                  value: value,
                  groupValue: groupValue,
                  onChanged: onChanged,
                  fillColor: WidgetStateProperty.resolveWith((states) {
                    // active
                    if (states.contains(WidgetState.selected)) {
                      return AppColors.primaryColor;
                    }
                    // inactive
                    return AppColors.ffDEE2E6;
                  }),
                ),
                SizedBox(
                  height: AppSize.h30,
                  width: AppSize.h30,
                  child: equipmentImage,
                ),
              ],
            ),
            MarqueeWidget(
              child: Text(
                equipmentTypeName ?? '',
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: AppSize.sp12,
                  color: AppColors.black,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
