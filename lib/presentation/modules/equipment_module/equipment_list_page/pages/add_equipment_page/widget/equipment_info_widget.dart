// ignore_for_file: public_member_api_docs
import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/provider/add_and_update_equipment_provider.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/widget/equipment_type_radio_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_dropdown.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

class EquipmentInfoWidget extends StatelessWidget {
  const EquipmentInfoWidget({
    required this.addEquipmentProvider,
    super.key,
  });

  final AddAndUpdateEquipmentProvider addEquipmentProvider;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w24,
        vertical: AppSize.h16,
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r6),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.equipmentInfo,
            style: context.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
              fontSize: AppSize.sp20,
            ),
          ),
          Gap(AppSize.h20),

          //* Equipment Name
          AppTextFormField(
            title: l10n.equipmentName,
            hintText: l10n.enterEquipmentName,
            fillColor: AppColors.ffF8F9FA,
            controller: addEquipmentProvider.equipmentNameController,
            validator: (p0) => equipmentNameValidator().call(p0),
          ),
          Gap(AppSize.h20),
          //* Equipment Type
          Text(
            '  ${l10n.equipmentType}',
            style: context.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: AppSize.sp14,
              color: AppColors.black,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              //* Car Hauler
              EquipmentTypeRadioWidget(
                groupValue: addEquipmentProvider.equipmentType.value,
                value: EquipmentType.CAR_HAULER,
                onChanged: (value) {
                  addEquipmentProvider.equipmentType.value =
                      value ?? EquipmentType.CAR_HAULER;
                  addEquipmentProvider.updateEquipmentType(
                    equipmentTypeIndex: 0,
                  );
                },
                equipmentImage: AppAssets.iconsCarHaulerTruck.image(),
                equipmentTypeName: l10n.carHauler,
              ),

              //* Flat Bed
              EquipmentTypeRadioWidget(
                groupValue: addEquipmentProvider.equipmentType.value,
                value: EquipmentType.FLAT_BED,
                onChanged: (value) {
                  addEquipmentProvider.equipmentType.value =
                      value ?? EquipmentType.CAR_HAULER;
                  addEquipmentProvider.updateEquipmentType(
                    equipmentTypeIndex: 1,
                  );
                },
                equipmentImage: AppAssets.iconsFlatBedTruck.image(),
                equipmentTypeName: l10n.flatBed,
              ),

              //* Tow Truck
              EquipmentTypeRadioWidget(
                groupValue: addEquipmentProvider.equipmentType.value,
                value: EquipmentType.TOW_TRUCK,
                onChanged: (value) {
                  addEquipmentProvider.equipmentType.value =
                      value ?? EquipmentType.CAR_HAULER;
                  addEquipmentProvider.updateEquipmentType(
                    equipmentTypeIndex: 2,
                  );
                },
                equipmentImage: AppAssets.iconsTowTruck.image(),
                equipmentTypeName: l10n.towTruck,
              ),
            ],
          ),
          Gap(AppSize.h20),
          //* Equipment Brand
          AppDropdown(
            title: l10n.equipmentBrand,
            controller: addEquipmentProvider.selectedEquipmentBrand,
            // selectedItem: addEquipmentProvider.selectedEquipmentBrand.value,
            hintText: l10n.chooseVehicleBrand,
            fillColor: AppColors.ffF8F9FA,
            validator: equipmentBrandValidator().call,
            // onChanged: (value) {
            //   addEquipmentProvider.selectedEquipmentBrand.value = value;
            // },
            items: List.generate(
              addEquipmentProvider.equipmentBrandList.length,
              (index) {
                return DropDownValueModel(
                  value: addEquipmentProvider.equipmentBrandList[index].id
                      .toString(),
                  name: addEquipmentProvider.equipmentBrandList[index].name,
                );
              },
            ),
          ),
          Gap(AppSize.h20),
          //* Equipment Year

          AppDropdown(
            title: l10n.equipmentYear,
            controller: addEquipmentProvider.selectedEquipmentYear,
            // selectedItem: addEquipmentProvider.selectedEquipmentYear.value,
            hintText: l10n.chooseVehicleYear,
            fillColor: AppColors.ffF8F9FA,
            validator: equipmentYearValidator().call,
            // onChanged: (value) {
            //   addEquipmentProvider.selectedEquipmentYear.value = value;
            // },
            items: List.generate(
              addEquipmentProvider.equipmentYearList.length,
              (index) {
                return DropDownValueModel(
                  value: addEquipmentProvider.equipmentYearList[index].id
                      .toString(),
                  name: addEquipmentProvider.equipmentYearList[index].year
                      .toString(),
                );
              },
            ),
          ),
          Gap(AppSize.h20),
          //* Equipment Version

          AppDropdown(
            title: l10n.equipmentVersion,
            hintText: l10n.chooseVehicleVersion,
            // selectedItem: addEquipmentProvider.selectedEquipmentVersion.value,
            controller: addEquipmentProvider.selectedEquipmentVersion,
            fillColor: AppColors.ffF8F9FA,
            validator: equipmentVersionValidator().call,
            // onChanged: (value) {
            //   addEquipmentProvider.selectedEquipmentVersion.value = value;
            // },
            items: List.generate(
              addEquipmentProvider.equipmentVersionList.length,
              (index) {
                return DropDownValueModel(
                  value: addEquipmentProvider.equipmentVersionList[index].id
                      .toString(),
                  name:
                      addEquipmentProvider.equipmentVersionList[index].version,
                );
              },
            ),
          ),
          Gap(AppSize.h20),

          //* Slot
          AppTextFormField(
            title: l10n.enterOfSlots,
            fillColor: AppColors.ffF8F9FA,
            controller: addEquipmentProvider.slotController,
            validator:  equipmentSlotValidator().call,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            maxTextLength: 2,
            keyboardType: TextInputType.number,
          ),
        ],
      ),
    );
  }
}
