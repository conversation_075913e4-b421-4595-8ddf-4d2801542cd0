// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/provider/add_and_update_equipment_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

class CapacityLoadWidget extends StatelessWidget {
  const CapacityLoadWidget({
    required this.addEquipmentProvider,
    super.key,
  });

  final AddAndUpdateEquipmentProvider addEquipmentProvider;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ValueListenableBuilder(
      valueListenable: addEquipmentProvider.isInoperableVehicleLoad,
      builder: (context, isInoperableVehicleLoad, _) {
        return AppPadding.symmetric(
          vertical: AppSize.h16,
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  addEquipmentProvider.isInoperableVehicleLoad.value =
                      !addEquipmentProvider.isInoperableVehicleLoad.value;
                },
                child: Container(
                  height: AppSize.h20,
                  width: AppSize.h20,
                  decoration: BoxDecoration(
                    color: addEquipmentProvider.isInoperableVehicleLoad.value
                        ? AppColors.primaryColor
                        : AppColors.transparent,
                    borderRadius: BorderRadius.circular(AppSize.r4),
                    border: Border.all(
                      width: AppSize.w2,
                      color: addEquipmentProvider.isInoperableVehicleLoad.value
                          ? AppColors.transparent
                          : AppColors.ffDEE2E6,
                    ),
                  ),
                  child: addEquipmentProvider.isInoperableVehicleLoad.value
                      ? FittedBox(
                          child: Icon(
                            Icons.done,
                            color: AppColors.white,
                            size: AppSize.sp18,
                          ),
                        )
                      : const SizedBox(),
                ),
              ),
              Gap(AppSize.w8),
              Flexible(
                child: Text(
                  l10n.capacityToLoadInoperableWinch,
                  maxLines: 2,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: AppSize.sp14,
                    color: AppColors.black,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
