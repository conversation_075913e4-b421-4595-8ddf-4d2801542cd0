// ignore_for_file: public_member_api_docs, invalid_use_of_protected_member

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/provider/add_and_update_equipment_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_image.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

class EquipmentPhotoUploadWidget extends StatelessWidget {
  const EquipmentPhotoUploadWidget({
    required this.addEquipmentProvider,
    super.key,
  });

  final AddAndUpdateEquipmentProvider addEquipmentProvider;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '  ${l10n.photosOptional}',
          style: context.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: AppSize.sp14,
            color: AppColors.black,
          ),
        ),
        Gap(AppSize.h4),
        ValueListenableBuilder(
          valueListenable: addEquipmentProvider.imagesIdsValue,
          builder: (context, imagesIdsList, _) {
            return ValueListenableBuilder(
              valueListenable: addEquipmentProvider.selectedImages,
              builder: (context, imagesList, _) {
                return imagesList.isEmpty && imagesIdsList.isEmpty
                    ? GestureDetector(
                        onTap: () {
                          addEquipmentProvider.chooseImages(
                            context: context,
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.all(AppSize.sp30),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.ffF5F0FF,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.add_photo_alternate_outlined,
                                size: AppSize.sp48,
                                color: const Color(
                                  0xFF6A1B9A,
                                ), // Purple color for the icon
                              ),
                              Gap(AppSize.h8),
                              Text(
                                l10n.uploadPicturesTitle,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(
                                    0xFF6A1B9A,
                                  ), // Purple text color
                                ),
                              ),
                              Gap(AppSize.h4),
                              Text(
                                l10n.uploadPicturesDescription,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: AppSize.sp10,
                                  color: const Color(0xFF6A1B9A)
                                      .withValues(alpha: 0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : Container(
                        padding: EdgeInsets.all(AppSize.sp10),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: AppColors.ffF5F0FF,
                          borderRadius: BorderRadius.circular(AppSize.r12),
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        child: SizedBox(
                          height: AppSize.h100,
                          width: context.width,
                          child: ListView.builder(
                            itemCount: addEquipmentProvider
                                    .imagesIdsValue.value.length +
                                addEquipmentProvider
                                    .selectedImages.value.length +
                                1,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              final networkImagesCount = addEquipmentProvider
                                  .imagesIdsValue.value.length;
                              final fileImagesCount = addEquipmentProvider
                                  .selectedImages.value.length;

                              if (index ==
                                  networkImagesCount + fileImagesCount) {
                                return (networkImagesCount + fileImagesCount) ==
                                        7
                                    ? const SizedBox()
                                    : InkWell(
                                        onTap: () {
                                          addEquipmentProvider.chooseImages(
                                            context: context,
                                          );
                                        },
                                        child: SizedBox(
                                          width: AppSize.w110,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: AppColors.ffF5F0FF,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                AppSize.r12,
                                              ),
                                              border: Border.all(
                                                color: Colors.grey.shade300,
                                              ),
                                            ),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                AppSize.r6,
                                              ),
                                              child: const Icon(Icons.add),
                                            ),
                                          ),
                                        ),
                                      );
                              } else {
                                final isNetworkImage =
                                    index < networkImagesCount;
                                return Stack(
                                  children: [
                                    SizedBox(
                                      width: AppSize.w120,
                                      child: AppPadding(
                                        right: AppSize.w10,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(AppSize.r6),
                                          child: isNetworkImage
                                              ? AppImage.network(
                                                  addEquipmentProvider
                                                          .imagesIdsValue
                                                          .value[index]
                                                          .imageUrl ??
                                                      '',
                                                  fit: BoxFit.cover,
                                                  height: AppSize.h100,
                                                )
                                              : Image.file(
                                                  File(
                                                    addEquipmentProvider
                                                        .selectedImages
                                                        .value[index -
                                                            networkImagesCount]
                                                        .path,
                                                  ),
                                                  height: AppSize.h100,
                                                  fit: BoxFit.cover,
                                                ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      width: AppSize.w110,
                                      child: Align(
                                        alignment: Alignment.topRight,
                                        child: InkWell(
                                          onTap: () {
                                            if (isNetworkImage) {
                                              addEquipmentProvider
                                                  .imagesIdsValue.value
                                                  .removeAt(index);
                                            } else {
                                              addEquipmentProvider
                                                  .selectedImages.value
                                                  .removeAt(
                                                index - networkImagesCount,
                                              );
                                            }
                                            // Notify listeners for both cases
                                            addEquipmentProvider.imagesIdsValue
                                                // ignore: invalid_use_of_visible_for_testing_member
                                                .notifyListeners();
                                            addEquipmentProvider.selectedImages
                                                // ignore: invalid_use_of_visible_for_testing_member
                                                .notifyListeners();
                                          },
                                          child: Container(
                                            margin: EdgeInsets.all(AppSize.sp4),
                                            height: AppSize.sp20,
                                            width: AppSize.sp20,
                                            decoration: BoxDecoration(
                                              color: AppColors.ffF5F0FF,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                AppSize.r12,
                                              ),
                                              border: Border.all(
                                                color: Colors.grey.shade300,
                                              ),
                                            ),
                                            child: Icon(
                                              Icons.close,
                                              color: AppColors.primaryColor,
                                              size: AppSize.sp16,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }
                            },
                          ),
                        ),
                      );
              },
            );
          },
        ),
      ],
    );
  }
}
