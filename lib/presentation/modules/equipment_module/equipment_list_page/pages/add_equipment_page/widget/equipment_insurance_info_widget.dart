// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/provider/add_and_update_equipment_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

class EquipmentInsuranceInfoWidget extends StatelessWidget {
  const EquipmentInsuranceInfoWidget({
    required this.addEquipmentProvider,
    super.key,
  });

  final AddAndUpdateEquipmentProvider addEquipmentProvider;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Container(
      margin: EdgeInsets.only(
        top: AppSize.h16,
        bottom: AppSize.w24,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w24,
        vertical: AppSize.h16,
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r6),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.insuranceInfo,
            style: context.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
              fontSize: AppSize.sp20,
            ),
          ),
          Gap(AppSize.h20),

          //* Policy No.
          AppTextFormField(
            title: l10n.policyNo,
            hintText: l10n.enterInsurancePolicyNo,
            fillColor: AppColors.ffF8F9FA,
            controller: addEquipmentProvider.policyNumberController,
            validator: (p0) =>
                equipmentInsurancePolicyNumberValidator().call(p0),
          ),
        ],
      ),
    );
  }
}
