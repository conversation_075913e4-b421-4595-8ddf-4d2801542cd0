// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/models/equipments_list_model.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/provider/add_and_update_equipment_provider.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/widget/capacity_load_widget.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/widget/equipment_info_widget.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/widget/equipment_insurance_info_widget.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/widget/equipment_number_widget.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/widget/equipment_photo_upload_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class AddAndUpdateEquipmentPage extends StatelessWidget {
  const AddAndUpdateEquipmentPage({this.equipmentData, super.key});

  final EquipmentDataModel? equipmentData;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider<AddAndUpdateEquipmentProvider>(
      create: (context) =>
          AddAndUpdateEquipmentProvider(equipmentData: equipmentData),
      child: Consumer<AddAndUpdateEquipmentProvider>(
        builder: (context, addEquipmentProvider, _) {
          return Form(
            key: addEquipmentProvider.formKeyAddEquipment,
            child: Scaffold(
              backgroundColor: AppColors.ffF8F9FA,
              appBar: CustomAppBar(
                title: equipmentData != null
                    ? l10n.updateEquipment
                    : l10n.addEquipment,
              ),
              body: GestureDetector(
                onTap: AppCommonFunctions.closeKeyboard,
                child: ValueListenableBuilder(
                  valueListenable: addEquipmentProvider.isShowLoader,
                  builder: (context, loading, child) {
                    return AppLoader(
                      isShowLoader: loading,
                      child: child!,
                    );
                  },
                  child: AppPadding.symmetric(
                    horizontal: AppSize.appPadding,
                    child: ValueListenableBuilder(
                      valueListenable: addEquipmentProvider.equipmentType,
                      builder: (context, selectedEquipment, _) {
                        return SingleChildScrollView(
                          child: Column(
                            children: [
                              //* Equipment info
                              EquipmentInfoWidget(
                                addEquipmentProvider: addEquipmentProvider,
                              ),

                              //* Capacity Load
                              CapacityLoadWidget(
                                addEquipmentProvider: addEquipmentProvider,
                              ),

                              //* Equipment Number
                              EquipmentNumberWidget(
                                addEquipmentProvider: addEquipmentProvider,
                              ),

                              //* Equipment Insurance policy Info
                              EquipmentInsuranceInfoWidget(
                                addEquipmentProvider: addEquipmentProvider,
                              ),

                              //* Photo upload
                              EquipmentPhotoUploadWidget(
                                addEquipmentProvider: addEquipmentProvider,
                              ),

                              //* Save Equipment
                              AppPadding.symmetric(
                                vertical: AppSize.h30,
                                child: AppButton(
                                  text: l10n.saveEquipment,
                                  onPressed: () {
                                    if (addEquipmentProvider
                                        .formKeyAddEquipment.currentState!
                                        .validate()) {
                                      if (equipmentData != null) {
                                        addEquipmentProvider.updateEquipment(
                                          context: context,
                                        );
                                      } else {
                                        addEquipmentProvider.addEquipment(
                                          context: context,
                                        );
                                      }
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
