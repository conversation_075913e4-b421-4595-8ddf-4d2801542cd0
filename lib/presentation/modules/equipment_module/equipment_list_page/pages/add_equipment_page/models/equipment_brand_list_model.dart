// ignore_for_file: public_member_api_docs

class EquipmentBrandsListModel {
  EquipmentBrandsListModel({
    required this.brands,
  });

  factory EquipmentBrandsListModel.fromJson(List<dynamic> json) {
    return EquipmentBrandsListModel(
      brands: json
          .map((brand) => Brand.fromJson(brand as Map<String, dynamic>))
          .toList(),
    );
  }
  final List<Brand> brands;

  List<Map<String, dynamic>> toJson() {
    return brands.map((brand) => brand.toJson()).toList();
  }
}

class Brand {
  Brand({
    required this.id,
    required this.name,
  });

  factory Brand.fromJson(Map<String, dynamic> json) {
    return Brand(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
    );
  }
  final int id;
  final String name;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}
