// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/models/equipments_list_model.dart';

/// Parameters for the AddAndUpdateEquipmentScreen
class EquipmentAddUpdateParams {
  /// Constructor
  EquipmentAddUpdateParams({this.equipmentData});
  
  /// The equipment data
  final EquipmentDataModel? equipmentData;
}
