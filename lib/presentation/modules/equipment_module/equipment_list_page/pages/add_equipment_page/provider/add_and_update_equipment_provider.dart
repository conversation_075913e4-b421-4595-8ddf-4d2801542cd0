import 'dart:io';
import 'dart:typed_data';

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/models/equipments_list_model.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/models/equipment_brand_list_model.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/models/equipment_data_list_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/repositories/equipments_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Add Equipment Provider
class AddAndUpdateEquipmentProvider extends ChangeNotifier {
  AddAndUpdateEquipmentProvider({EquipmentDataModel? equipmentData}) {
    getEquipmentBrands(context: rootNavKey.currentContext!).then((v) {
      selectedEquipmentBrand.addListener(selectedEquipmentBrandListener);
      selectedEquipmentYear.addListener(selectedEquipmentYearListener);

      if (equipmentData != null) {
        tempEquipmentData = equipmentData;
        equipmentNameController.text = equipmentData.name ?? '';
        slotController.text = (equipmentData.slot ?? '').toString();
        plateNumberController.text = equipmentData.plateNumber ?? '';
        economicNumberController.text = equipmentData.economicNumber ?? '';
        validityDate.value = equipmentData.validity ?? DateTime.now();
        policyNumberController.text = equipmentData.insurancePolicyNumber ?? '';
        final brandId = equipmentBrandList.where(
          (e) => e.name == equipmentData.brand,
        );
        if (brandId.isNotEmpty) {
          // selectedEquipmentBrand.value = brandId.first.id.toString();
          selectedEquipmentBrand.setDropDown(
            DropDownValueModel(
              name: brandId.first.name,
              value: brandId.first.id.toString(),
            ),
          );
        }
        equipmentType.value = (EquipmentType.CAR_HAULER.name ==
                equipmentData.equipmentType)
            ? EquipmentType.CAR_HAULER
            : EquipmentType.FLAT_BED.name == equipmentData.equipmentType
                ? EquipmentType.FLAT_BED
                : EquipmentType.TOW_TRUCK.name == equipmentData.equipmentType
                    ? EquipmentType.TOW_TRUCK
                    : EquipmentType.CAR_HAULER;
        isInoperableVehicleLoad.value = equipmentData.winch ?? false;
        imagesIdsValue.value = [];
        imagesIdsValue.value.addAll(equipmentData.images ?? []);
        selectedEquipmentBrand.notifyListeners();
      }
    });
  }

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  EquipmentDataModel? tempEquipmentData;
  bool isClosed = false;
  final equipmentNameController = TextEditingController();
  final slotController = TextEditingController();
  final plateNumberController = TextEditingController();
  final economicNumberController = TextEditingController();
  // final validityController = TextEditingController();
  final validityDate = ValueNotifier<DateTime?>(null);
  final policyNumberController = TextEditingController();
  final formKeyAddEquipment = GlobalKey<FormState>();

  final equipmentType = ValueNotifier<EquipmentType>(EquipmentType.CAR_HAULER);
  // ValueNotifier<String> selectedEquipment = ValueNotifier('Car Hauler');
  // final selectedEquipmentBrand = ValueNotifier<String?>(null);
  // final selectedEquipmentYear = ValueNotifier<String?>(null);
  // final selectedEquipmentVersion = ValueNotifier<String?>(null);
  final SingleValueDropDownController selectedEquipmentBrand =
      SingleValueDropDownController();
  final SingleValueDropDownController selectedEquipmentYear =
      SingleValueDropDownController();
  final SingleValueDropDownController selectedEquipmentVersion =
      SingleValueDropDownController();
  final isInoperableVehicleLoad = ValueNotifier<bool>(false);

  final selectedImages = ValueNotifier<List<XFile>>([]);
  final imagesIdsValue = ValueNotifier<List<EquipmentImages>>([]);

  final equipmentBrandList = <Brand>[];
  final equipmentYearList = <Year>[];
  final equipmentVersionList = <Version>[];

  CancelToken? equipmentBrandListCancelToken;
  CancelToken? getAllTheBrandDataCancelToken;
  CancelToken? addEquipmentCancelToken;
  final isShowLoader = ValueNotifier<bool>(false);
  EquipmentDataListModel? _getAllTheEquipmentData;

  void selectedEquipmentBrandListener() {
    if (isClosed) return;
    try {
      if (selectedEquipmentBrand.dropDownValue != null &&
          selectedEquipmentBrand.dropDownValue!.value
              .toString()
              .isNotEmptyAndNotNull) {
        getEquipmentDataByBrand(context: rootNavKey.currentContext!);
      }
    } catch (e) {
      '==>> selectedEquipmentBrandListener error $e'.logE;
      e.toString().showErrorAlert();
    }
  }

  void selectedEquipmentYearListener() {
    if (isClosed) return;
    try {
      if (selectedEquipmentYear.dropDownValue != null &&
          selectedEquipmentYear.dropDownValue!.value
              .toString()
              .isNotEmptyAndNotNull) {
        if (_getAllTheEquipmentData != null) {
          selectedEquipmentVersion.clearDropDown();
          equipmentVersionList.clear();
          for (final e in _getAllTheEquipmentData!.equipmentData) {
            for (final i in e.years) {
              if (i.id.toString() ==
                  selectedEquipmentYear.dropDownValue!.value) {
                equipmentVersionList.addAll(i.versions);
                if (tempEquipmentData != null) {
                  final versionId = equipmentVersionList.where(
                    (e) => e.version == tempEquipmentData!.version,
                  );
                  if (versionId.isNotEmpty) {
                    // selectedEquipmentVersion.value =
                    //     versionId.first.id.toString();
                    selectedEquipmentVersion.setDropDown(
                      DropDownValueModel(
                        name: versionId.first.version,
                        value: versionId.first.id.toString(),
                      ),
                    );
                  }
                }
                notify();
                break;
              }
            }
          }
        }
      }
    } catch (e) {
      '==>> selectedEquipmentYearListener error $e'.logE;
      e.toString().showErrorAlert();
    }
  }

  Future<void> getEquipmentDataByBrand({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      getAllTheBrandDataCancelToken?.cancel();
      getAllTheBrandDataCancelToken = CancelToken();
      final data = {
        ApiKeys.brandId: selectedEquipmentBrand.dropDownValue!.value,
      };
      final request = ApiRequest(
        path: EndPoints.getEquipmentsForCreation,
        params: data,
        cancelToken: getAllTheBrandDataCancelToken,
      );
      final res = await Injector.instance<EquipmentsRepository>()
          .getEquipmentsByBrand(request);
      await res.when(
        success: (data) async {
          if (isClosed ||
              (getAllTheBrandDataCancelToken?.isCancelled ?? true)) {
            return;
          }
          data.logD;
          _getAllTheEquipmentData = data;

          selectedEquipmentYear.clearDropDown();
          selectedEquipmentVersion.clearDropDown();
          equipmentYearList.clear();
          equipmentVersionList.clear();
          for (final e in data.equipmentData) {
            for (final i in e.years) {
              equipmentYearList.add(i);
            }
          }
          if (tempEquipmentData != null) {
            final index = equipmentYearList.indexWhere(
              (e) => e.year.toString() == tempEquipmentData!.year,
            );
            if (index != -1) {
              // selectedEquipmentYear.value =
              //     equipmentYearList[index].id.toString();
              selectedEquipmentYear.setDropDown(
                DropDownValueModel(
                  name: equipmentYearList[index].year.toString(),
                  value: equipmentYearList[index].id.toString(),
                ),
              );
            }
          }

          isShowLoader.value = false;
          notify();
        },
        error: (exception) async {
          if (isClosed ||
              (getAllTheBrandDataCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getAllTheBrandDataCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  Future<void> getEquipmentBrands({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      equipmentBrandListCancelToken?.cancel();
      equipmentBrandListCancelToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.getEquipmentsBrands,
        cancelToken: equipmentBrandListCancelToken,
      );
      final res = await Injector.instance<EquipmentsRepository>()
          .getEquipmentsBrands(request);
      await res.when(
        success: (data) async {
          if (isClosed ||
              (equipmentBrandListCancelToken?.isCancelled ?? true)) {
            return;
          }
          equipmentBrandList.addAll(data.brands);
          isShowLoader.value = false;
          notify();
        },
        error: (exception) async {
          if (isClosed ||
              (equipmentBrandListCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (equipmentBrandListCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  Future<void> addEquipment({required BuildContext context}) async {
    if (isClosed) return;
    try {
      addEquipmentCancelToken?.cancel();
      addEquipmentCancelToken = CancelToken();
      List<String>? imgList = [];

      if (selectedImages.value.isNotEmpty) {
        imgList = await generateEmptyListImages(
          imgList: selectedImages.value.map((e) => File(e.path)).toList(),
        );
      }

      if (imgList != null) {
        // Handle fields
        final data = {
          ApiKeys.version: selectedEquipmentVersion.dropDownValue?.value ?? '',
          ApiKeys.economicNumber: economicNumberController.text.trim(),
          ApiKeys.insurancePolicyNumber: policyNumberController.text.trim(),
          ApiKeys.plateNumber: plateNumberController.text.trim(),
          ApiKeys.slot: slotController.text.trim(),
          ApiKeys.name: equipmentNameController.text.trim(),
          ApiKeys.equipmentType: equipmentType.value.name.trim(),
          ApiKeys.winch: isInoperableVehicleLoad.value.toString(),
          'aws_image_keys': imgList,
          ApiKeys.validity: validityDate.value?.passDateFormate ?? '',
        };

        isShowLoader.value = true;
        final request = ApiRequest(
          path: EndPoints.addEquipment,
          data: data,
          cancelToken: addEquipmentCancelToken,
        );
        final res = await Injector.instance<EquipmentsRepository>()
            .createEquipment(request);
        await res.when(
          success: (data) async {
            if (isClosed || (addEquipmentCancelToken?.isCancelled ?? true)) {
              return;
            }
            data.logD;
            isShowLoader.value = false;
            context.l10n.equipmentAddSuccess.showSuccessAlert();
            AppNavigationService.pop(context, true);
          },
          error: (exception) async {
            if (isClosed || (addEquipmentCancelToken?.isCancelled ?? true)) {
              return;
            }
            isShowLoader.value = false;
            exception.message.showErrorAlert();
          },
        );
      }
    } catch (e) {
      if (isClosed || (addEquipmentCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  Future<void> updateEquipment({required BuildContext context}) async {
    if (isClosed) return;
    try {
      addEquipmentCancelToken?.cancel();
      addEquipmentCancelToken = CancelToken();

      List<String>? imgList = [];

      if (selectedImages.value.isNotEmpty) {
        imgList = await generateEmptyListImages(
          imgList: selectedImages.value.map((e) => File(e.path)).toList(),
        );
      }
      // Handle fields
      final data = {
        ApiKeys.version: selectedEquipmentVersion.dropDownValue?.value ?? '',
        ApiKeys.economicNumber: economicNumberController.text.trim(),
        ApiKeys.insurancePolicyNumber: policyNumberController.text.trim(),
        ApiKeys.plateNumber: plateNumberController.text.trim(),
        ApiKeys.slot: slotController.text.trim(),
        ApiKeys.name: equipmentNameController.text.trim(),
        ApiKeys.equipmentType: equipmentType.value.name.trim(),
        ApiKeys.winch: isInoperableVehicleLoad.value.toString(),
        ApiKeys.awsImageKeys: imgList,
        ApiKeys.existingImagesId:
            imagesIdsValue.value.map((e) => e.id ?? '').toList(),
        ApiKeys.validity: validityDate.value?.passDateFormate ?? '',
      };
      isShowLoader.value = true;
      final request = ApiRequest(
        path: '${EndPoints.updateEquipment}/${tempEquipmentData?.id}/update/',
        data: data,
        cancelToken: addEquipmentCancelToken,
      );
      final res = await Injector.instance<EquipmentsRepository>()
          .updateEquipment(request);
      await res.when(
        success: (data) async {
          if (isClosed || (addEquipmentCancelToken?.isCancelled ?? true)) {
            return;
          }
          data.logD;
          isShowLoader.value = false;
          context.l10n.equipmentUpdateSuccess.showSuccessAlert();
          AppNavigationService.pop(context, true);
        },
        error: (exception) async {
          if (isClosed || (addEquipmentCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (addEquipmentCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  void updateEquipmentType({required int equipmentTypeIndex}) {
    if (isClosed) return;
    try {
      equipmentType.value = EquipmentType.values[equipmentTypeIndex];
    } catch (e) {
      '==>> updateEquipmentType error $e'.logE;
      e.toString().logE;
    }
  }

  Future<void> chooseImages({
    required BuildContext context,
  }) async {
    if (isClosed) return;
    try {
      final list = await AppCommonFunctions.showImagePickerPopup(
            context: context,
          ) ??
          [];

      if (list.isNotEmpty) {
        final remainingSlots =
            7 - (selectedImages.value.length + imagesIdsValue.value.length);

        if (remainingSlots > 0) {
          // Add only up to the remaining slots
          final imagesToAdd = list.take(remainingSlots).toList();
          selectedImages.value.addAll(imagesToAdd);
          selectedImages.notifyListeners();
        } else {
          // ignore: use_build_context_synchronously
          context.l10n.maxImagesError.showInfoAlert();
        }
      }
    } catch (e) {
      '==>> chooseImages error $e'.logE;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    tempEquipmentData = null;
    imagesIdsValue.value.clear();
    imagesIdsValue.dispose();
    selectedEquipmentBrand.removeListener(selectedEquipmentBrandListener);
    selectedEquipmentYear.removeListener(selectedEquipmentYearListener);
    equipmentNameController.dispose();
    slotController.dispose();
    plateNumberController.dispose();
    economicNumberController.dispose();
    policyNumberController.dispose();
    validityDate.dispose();
    equipmentType.dispose();
    selectedEquipmentBrand.dispose();
    selectedEquipmentYear.dispose();
    selectedEquipmentVersion.dispose();
    isInoperableVehicleLoad.dispose();
    selectedImages.dispose();
    equipmentBrandList.clear();
    equipmentYearList.clear();
    equipmentVersionList.clear();
    _getAllTheEquipmentData = null;
    equipmentBrandListCancelToken?.cancel();
    getAllTheBrandDataCancelToken?.cancel();
    addEquipmentCancelToken?.cancel();
    isShowLoader.dispose();
    super.dispose();
  }

  Future<bool> _uploadFile({
    required String url,
    required Uint8List byteImages,
    required String mimeType,
    void Function(double)? onSendProgress,
    CancelToken? cancelToken,
  }) {
    try {
      final baseOptions = BaseOptions(
        connectTimeout: const Duration(minutes: 10),
        sendTimeout: const Duration(minutes: 10),
        receiveTimeout: const Duration(minutes: 10),
        // contentType: mimeType,
        headers: {
          'Content-type': '',
        },
      );

      final dio = Dio(baseOptions);
      return dio.put<Map<String, dynamic>>(
        url,
        data: byteImages,
        cancelToken: cancelToken,
        onSendProgress: (count, total) {
          final progressPercent = count / total;
          onSendProgress?.call(progressPercent);
        },
      ).then(
        (value) => true,
        onError: (Object error) {
          '======= image url error == here == $error'.logE;
          if (error is DioException) {
            if (error.type == DioExceptionType.cancel) {
              return false;
            }
          }
          return false;
        },
      ).onError((error, stackTrace) {
        '======= image url error $error'.logE;
        return false;
      });
    } catch (e) {
      '==>> _uploadFile error $e'.logE;
      e.toString().logE;
      return Future.value(false);
    }
  }

  Future<List<String>>? generateEmptyListImages({
    required List<File> imgList,
  }) async {
    if (isClosed) return [];

    try {
      isShowLoader.value = true;
      final imagesList = <String>[];
      final data = FormData();
      data.fields.addAll([
        const MapEntry('file_extension', 'jpg'),
        MapEntry('folder_name', ImageTypes.EQUIPMENT.name),
        MapEntry('number_of_url', imgList.length.toString()),
      ]);

      final request = ApiRequest(
        path: EndPoints.generateUrl,
        data: data,
      );

      final res =
          await Injector.instance<AccountRepository>().generateUrl(request);

      await res.when(
        success: (data) async {
          if (isClosed) return [];

          final uploadFutures = <Future<void>>[];

          // Loop through the picked images
          if (imgList.length == data.length) {
            for (var i = 0; i < imgList.length; i++) {
              uploadFutures.add(
                _uploadFile(
                  byteImages: imgList[i].readAsBytesSync(),
                  url: data[i]['put_url'].toString(),
                  mimeType: 'image/jpeg',
                ),
              );
            }
          } else {
            rootNavKey.currentContext!.l10n.imagesNotUploaded.showInfoAlert();
            return [];
          }

          // Wait for all uploads to complete
          await Future.wait(uploadFutures);

          // Convert data['keys'] to List<String> and add to imagesList
          imagesList.addAll(
            data.map((e) => e['key_name'].toString()),
          );
          isShowLoader.value = false;
          return imagesList;
        },
        error: (exception) async {
          if (isClosed) return [];

          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
      return imagesList;
    } catch (e) {
      if (isClosed) return [];
      isShowLoader.value = false;
      '==>> generateEmptyListImages error $e'.logE;
      e.toString().logE;
      return [];
    }
  }
}
