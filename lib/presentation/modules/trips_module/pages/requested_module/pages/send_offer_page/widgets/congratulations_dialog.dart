// import 'package:flutter/material.dart';
// import 'package:transportmatch_provider/extensions/ext_build_context.dart';
// import 'package:transportmatch_provider/utils/app_colors.dart';
// import 'package:transportmatch_provider/utils/app_size.dart';
// import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
// import 'package:transportmatch_provider/widgets/app_button.dart';

// /// Show filter bottom sheet
// Future<T?> congratulationsDialogBox<T>(
//   BuildContext context, {
//   required bool isAccept,
// }) {
//   return showDialog<T>(
//     context: context,
//     builder: (context) {
//       return AlertDialog(
//         insetPadding: EdgeInsets.all(AppSize.r20),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.all(Radius.circular(AppSize.r12)),
//         ),
//         icon: Padding(
//           padding: EdgeInsets.only(top: AppSize.h12),
//           child: isAccept
//               ? AppAssets.iconsSuccess.image(
//                   height: AppSize.h70,
//                   fit: BoxFit.fitHeight,
//                 )
//               : AppAssets.iconsNoProvider.image(
//                   height: AppSize.h70,
//                   fit: BoxFit.fitHeight,
//                 ),
//         ),
//         title: Text(
//           isAccept ? 'Congratulations' : 'Offer Rejected',
//           style: context.textTheme.titleLarge,
//         ),
//         content: Padding(
//           padding: EdgeInsets.symmetric(horizontal: AppSize.w1),
//           child: Text(
//             isAccept
//                 ? 'Your offer have been accepted, do you want to continue with this offer?'
//                 : 'Your offer has been rejected, you can counter offer or ignore it.',
//             textAlign: TextAlign.center,
//             style: context.textTheme.titleMedium
//                 ?.copyWith(color: AppColors.ff6C757D, fontSize: AppSize.sp14),
//           ),
//         ),
//         actions: [
//           Row(
//             children: [
//               Expanded(
//                 child: Padding(
//                   padding: EdgeInsets.all(AppSize.r8),
//                   child: isAccept
//                       ? AppButton(
//                           text: 'Accept',
//                           onPressed: () {},
//                         )
//                       : AppButton(
//                           text: 'Counter offer',
//                           onPressed: () {},
//                         ),
//                 ),
//               ),
//               Expanded(
//                 child: Padding(
//                   padding: EdgeInsets.all(AppSize.r8),
//                   child: isAccept
//                       ? AppButton(
//                           isFillButton: false,
//                           text: 'Reject',
//                           onPressed: () {},
//                         )
//                       : AppButton(
//                           isFillButton: false,
//                           text: 'Reject',
//                           onPressed: () {},
//                         ),
//                 ),
//               ),
//             ],
//           ),
//         ],
//       );
//     },
//   );
// }
