// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class SendOfferProvider extends ChangeNotifier {
  SendOfferProvider({this.offerData, this.tripId})
      : assert(
          offerData != null || tripId != null,
          'Either tripId or offerData must be provided',
        ) {
    if (offerData != null) {
      sentOfferDetail.value = offerData;
      tripId = offerData?.id;
      getCurrentOffers();
    }
    if (offerData == null) {
      '==>>>> here ${offerData?.toJson()}'.logE;
      getSentOfferDetail();
    }
  }
  final sentOfferDetail = ValueNotifier<SentOffer?>(null);
  final refreshController = RefreshController();

  bool isClosed = false;
  int? tripId;
  SentOffer? offerData;
  List<OfferProviderModel> offers = [];
  int averageOffer = 0;
  int minOffer = 0;
  int maxOffer = 0;

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// this api to get detail for every sent offer trips
  CancelToken? getSentOfferDetailCancelToken;
  bool isDetailShowLoad = false;
  Future<void> getSentOfferDetail() async {
    isDetailShowLoad = true;
    getSentOfferDetailCancelToken?.cancel();
    getSentOfferDetailCancelToken = CancelToken();
    try {
      final result =
          await Injector.instance<TripRepository>().getSentBookingDetail(
        ApiRequest(
          path: '${EndPoints.getSentOfferDetail}$tripId/',
          cancelToken: getSentOfferDetailCancelToken,
        ),
      );
      await result.when(
        success: (data) async {
          log('======>>>> getSentOfferDetail $data');
          if (getSentOfferDetailCancelToken?.isCancelled ?? true) return;
          sentOfferDetail.value = data;
          offerData = data;
          await getCurrentOffers();
          notifyListeners();
        },
        error: (exception) async {
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      '======>>>> getSentOfferDetail error $e'.logE;
    }
    isDetailShowLoad = false;
  }

  /// this api to get detail for every sent offer trips
  CancelToken? getCurrentOffersCancelToken;
  bool getCurrentOffersLoad = false;
  Future<void> getCurrentOffers() async {
    if (isClosed) return;
    getCurrentOffersLoad = true;
    getCurrentOffersCancelToken?.cancel();
    getCurrentOffersCancelToken = CancelToken();
    try {
      final result = await Injector.instance<TripRepository>().getCurrentOffers(
        ApiRequest(
          path:
              '${EndPoints.getCurrentOffers}${offerData?.exclusiveTrips?.booking ?? 0}/',
          cancelToken: getCurrentOffersCancelToken,
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed || (getCurrentOffersCancelToken?.isCancelled ?? true)) {
            return;
          }
          getCurrentOffersLoad = false;
          offers = [...data];
          averageOffer =
              (data.map((e) => e.offerPrice ?? 0).reduce((a, b) => a + b) /
                      data.length)
                  .toInt();
          minOffer = data
              .map((e) => e.offerPrice ?? 0)
              .reduce((a, b) => a < b ? a : b)
              .toInt();
          maxOffer = data
              .map((e) => e.offerPrice ?? 0)
              .reduce((a, b) => a > b ? a : b)
              .toInt();
          notify();
        },
        error: (exception) async {
          if (isClosed || (getCurrentOffersCancelToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getCurrentOffersCancelToken?.isCancelled ?? true)) {
        return;
      }
      getCurrentOffersLoad = false;
      if (getCurrentOffersCancelToken?.isCancelled ?? true) return;

      '======>>>> getCurrentOffers error $e'.logE;
    }
    getCurrentOffersLoad = false;
  }

  /// this api to update provider offer
  CancelToken? updateOfferCancelToken;
  bool updateCurrentOffersLoad = false;
  bool updateOfferLoad = false;
  Future<void> updateOffer(
    BuildContext context,
    int bookingId,
    int price, {
    bool isExclusive = false,
    required void Function(int newOffer) offerSent,
  }) async {
    if (isClosed) return;
    updateCurrentOffersLoad = true;
    updateOfferCancelToken?.cancel();
    updateOfferCancelToken = CancelToken();
    try {
      final result = await Injector.instance<TripRepository>().updateCost(
        ApiRequest(
          path: EndPoints.updateCost(bookingId.toString()),
          cancelToken: updateOfferCancelToken,
          data: {
            'cost_per_kilometer': price,
            'trip_type': isExclusive ? 'EXCLUSIVE' : 'SHARED',
          },
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed || (updateOfferCancelToken?.isCancelled ?? true)) {
            return;
          }
          context.l10n.tripOfferSent.showSuccessAlert();
          offerSent(price);
          notify();
          AppNavigationService.pop(context);
        },
        error: (exception) async {
          if (isClosed || (updateOfferCancelToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (updateOfferCancelToken?.isCancelled ?? true)) {
        return;
      }
      '======>>>> updateOffer error $e'.logE;
    }
    updateCurrentOffersLoad = false;
  }

  @override
  void dispose() {
    isClosed = true;
    getCurrentOffersCancelToken?.cancel();
    offers.clear();
    updateOfferCancelToken?.cancel();
    updateOfferCancelToken = null;
    getSentOfferDetailCancelToken?.cancel();
    getSentOfferDetailCancelToken = null;
    sentOfferDetail.dispose();
    averageOffer = 0;
    minOffer = 0;
    maxOffer = 0;
    refreshController.dispose();
    super.dispose();
  }
}
