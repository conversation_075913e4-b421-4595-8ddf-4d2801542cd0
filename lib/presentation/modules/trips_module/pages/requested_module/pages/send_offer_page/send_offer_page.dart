import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/models/send_offer_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/pages/offer_price_page/models/offer_price_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/common_card_widgets.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';

class SendOfferPage extends StatefulWidget {
  const SendOfferPage({
    super.key,
    required this.sendOfferParams,
  });
  final SendOfferParams sendOfferParams;

  @override
  State<SendOfferPage> createState() => _SendOfferPageState();
}

class _SendOfferPageState extends State<SendOfferPage> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        widget.sendOfferParams.requestedTripProvider
            .refreshSentOffers(isWantShowLoader: true);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget
          .sendOfferParams.requestedTripProvider.isShowLoaderForSentOffers,
      builder: (context, isShowLoader, child) {
        return AppLoader(
          isShowLoader: isShowLoader,
          child: Selector<RequestedTripProvider, (List<SentOffer>, String?)>(
            selector: (context, provider) =>
                (provider.sentOfferList, provider.sentOffersNextUrl),
            builder: (context, sentOfferList, child) {
              return SmartRefresher(
                controller: widget.sendOfferParams.requestedTripProvider
                    .sentOffersRefreshController,
                enablePullUp: sentOfferList.$2.isNotEmptyAndNotNull,
                onRefresh: () {
                  widget.sendOfferParams.requestedTripProvider
                      .refreshSentOffers(isWantShowLoader: true)
                      .whenComplete(
                        widget.sendOfferParams.requestedTripProvider
                            .sentOffersRefreshController.refreshCompleted,
                      );
                },
                onLoading: () {
                  widget.sendOfferParams.requestedTripProvider
                      .getSentOfferList(
                        isPagination: true,
                      )
                      .whenComplete(
                        widget.sendOfferParams.requestedTripProvider
                            .sentOffersRefreshController.loadComplete,
                      );
                },
                child: sentOfferList.$1.isEmpty && !isShowLoader
                    ? Center(
                        child: Text(context.l10n.noSentOfferYet),
                      )
                    : ListView.builder(
                        itemCount: sentOfferList.$1.length,
                        shrinkWrap: true,
                        padding: EdgeInsets.only(bottom: AppSize.h16),
                        itemBuilder: (context, index) {
                          final booking = sentOfferList.$1[index];
                          return CommonCardWidgets(
                            value: 3,
                            onDetailsPressed: () =>
                                AppNavigationService.pushNamed(
                              context,
                              AppRoutes.tripsOfferPriceScreen,
                              extra: OfferPriceParams(
                                offerData: booking,
                                tripId: booking.id,
                              ),
                            ),
                            sentOfferModel: booking,
                            onNotInterestedTripPressed: () => widget
                                .sendOfferParams.requestedTripProvider
                                .rejectExclusiveTrip(
                              booking.id ?? 0,
                            ),
                            // exclusiveBooking: booking,
                          );
                        },
                      ),
              );
            },
          ),
        );
      },
    );
  }
}
