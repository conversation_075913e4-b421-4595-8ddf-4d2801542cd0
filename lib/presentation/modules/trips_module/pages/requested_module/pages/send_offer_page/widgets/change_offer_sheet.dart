import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/provider/send_offer_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

/// Show filter bottom sheet
Future<T?> showOfferBottomSheet<T>(
  BuildContext context,
  SendOfferProvider sendOfferProvider,
  SentOffer offerData, {
  required void Function(int newOffer) offerSent,
}) {
  final priceController = TextEditingController();
  return showModalBottomSheet<T>(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(AppSize.r12)),
    ),
    backgroundColor: AppColors.pageBGColor,
    isScrollControlled: true,
    builder: (context) {
      return PopScope(
        onPopInvokedWithResult: (didPop, result) => sendOfferProvider
          ..updateOfferCancelToken?.cancel()
          ..updateCurrentOffersLoad = false,
        child: ChangeNotifierProvider.value(
          value: sendOfferProvider,
          child: Selector<SendOfferProvider, bool>(
            selector: (context, sendOfferProvider) =>
                sendOfferProvider.updateCurrentOffersLoad,
            builder: (context, updateCurrentOffersLoad, child) {
              return AppLoader(
                isShowLoader: updateCurrentOffersLoad,
                child: child!,
              );
            },
            child: Padding(
              padding: EdgeInsets.all(AppSize.w16)
                  .add(MediaQuery.of(context).viewInsets),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.l10n.changeOfferPrice,
                    style: context.textTheme.titleLarge?.copyWith(),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: AppSize.h15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TitleInfoWidget(
                          title: context.l10n.currentMaxPrice,
                          titleColor: AppColors.ff6C757D,
                          titleFontWeight: FontWeight.w600,
                          titleFontSize: AppSize.sp13,
                          subTitle:
                              '${sendOfferProvider.maxOffer}'.smartFormat(),
                          subTitleFontWeight: FontWeight.bold,
                          subTitleColor: AppColors.ff67509C,
                        ),
                        TitleInfoWidget(
                          title: context.l10n.currentMinPrice,
                          titleColor: AppColors.ff6C757D,
                          titleFontWeight: FontWeight.w600,
                          titleFontSize: AppSize.sp13,
                          subTitle:
                              '${sendOfferProvider.minOffer}'.smartFormat(),
                          subTitleColor: AppColors.ff67509C,
                          subTitleFontWeight: FontWeight.bold,
                        ),
                      ],
                    ),
                  ),
                  AppTextFormField(
                    title: context.l10n.offerAmount,
                    fillColor: AppColors.ffF8F9FA,
                    borderSide: const BorderSide(color: AppColors.ffDEE2E6),
                    hintText: context.l10n.offerAmount,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    controller: priceController,
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(bottom: AppSize.h10, top: AppSize.h20),
                    child: AppButton(
                      text: context.l10n.save,
                      onPressed: () {
                        if (priceController.text.isEmpty) return;

                        sendOfferProvider.updateOffer(
                          context,
                          offerData.id ?? 0,
                          int.parse(priceController.text),
                          isExclusive: offerData.exclusiveTrips != null,
                          offerSent: offerSent,
                        );

                        /// if  offer is Accept isAccept = true
                        /// if offer is reject isAccept = false
                        // congratulationsDialogBox(context, isAccept: false);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}
