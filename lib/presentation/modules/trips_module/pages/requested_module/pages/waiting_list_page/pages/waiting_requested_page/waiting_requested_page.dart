import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// Waiting Requested Screen
class WaitingRequestedPage extends StatelessWidget {
  /// Constructor
  const WaitingRequestedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.ffF8F9FA,
      appBar: CustomAppBar(
        title: context.l10n.requests,
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.maxFinite,
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(AppSize.r10),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(AppSize.r20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(bottom: AppSize.h10),
                            child: Text(
                              context.l10n.tripInfo,
                              style: context.textTheme.titleLarge,
                            ),
                          ),
                          AppTextFormField(
                            title: context.l10n.selectEquipment,
                            hintText: context.l10n.chooseEquipment,
                            fillColor: AppColors.ffF8F9FA,
                            borderSide:
                                const BorderSide(color: AppColors.ffDEE2E6),
                            suffixIcon:
                                const Icon(Icons.keyboard_arrow_down_sharp),
                          ),
                          Gap(AppSize.h10),
                          AppTextFormField(
                            title: context.l10n.enterAvailableSlots,
                            fillColor: AppColors.ffF8F9FA,
                            borderSide:
                                const BorderSide(color: AppColors.ffDEE2E6),
                          ),
                          Gap(AppSize.h10),
                          AppTextFormField(
                            title: context.l10n.selectDriver,
                            fillColor: AppColors.ffF8F9FA,
                            borderSide:
                                const BorderSide(color: AppColors.ffDEE2E6),
                            hintText: context.l10n.chooseDriver,
                            suffixIcon:
                                const Icon(Icons.keyboard_arrow_down_sharp),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Gap(AppSize.h16),
                  Padding(
                    padding: EdgeInsets.all(AppSize.r6),
                    child: Text.rich(
                      TextSpan(
                        text: context.l10n.enterCost,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp15,
                          fontWeight: FontWeight.w600,
                        ),
                        children: [
                          TextSpan(
                            text: context.l10n.perKm,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontSize: AppSize.sp15,
                              fontWeight: FontWeight.w600,
                              color: AppColors.ffADB5BD,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(bottom: AppSize.h15),
                    child: const AppTextFormField(
                      hintText: r'E.g $2.5',
                    ),
                  ),
                  AppTextFormField(
                    title: context.l10n.deadlineOfferDate,
                    hintText: 'MM/DD/YYY',
                    suffixIcon: AppAssets.iconsCalender.image(
                      height: AppSize.h20,
                      width: AppSize.w20,
                      color: AppColors.black,
                    ),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(top: AppSize.h60),
                child: AppButton(text: context.l10n.sendOffer),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
