import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/marqee_widget.dart';

/// VehiclesInfoWidget
class VehiclesInfoWidget extends StatefulWidget {
  /// Constructor
  const VehiclesInfoWidget({super.key, required this.data});
  final List<CarDetail> data;

  @override
  State<VehiclesInfoWidget> createState() => _VehiclesInfoWidgetState();
}

class _VehiclesInfoWidgetState extends State<VehiclesInfoWidget> {
  late PageController _pageController;

  @override
  void initState() {
    _pageController = PageController();
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0.1,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
                color: AppColors.white,
              ),
              child: Padding(
                padding: EdgeInsets.all(AppSize.h12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.l10n.vehicles_info,
                          style: context.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                            fontSize: AppSize.sp16,
                          ),
                        ),
                        AnimatedBuilder(
                          animation: _pageController,
                          builder: (context, child) {
                            return Text(
                              '${(_pageController.positions.isEmpty ? 0 : _pageController.page?.round() ?? 0) + 1}/${widget.data.length}',
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w400,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    Gap(AppSize.h8),
                    SizedBox(
                      height: AppSize.h172,
                      child: PageView.builder(
                        controller: _pageController,
                        itemCount: widget.data.length,
                        itemBuilder: (context, index) {
                          final vehicle = widget.data[index];
                          return FittedBox(
                            // fit: BoxFit.fitWidth,
                            child: Container(
                              width: context.width - AppSize.w60,
                              padding: EdgeInsets.all(AppSize.h12),
                              margin:
                                  EdgeInsets.symmetric(horizontal: AppSize.w4),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.ffADB5BD),
                                borderRadius: BorderRadius.circular(AppSize.r5),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                spacing: AppSize.h8,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      if (vehicle.car?.brand != null)
                                        Flexible(
                                          child: VehiclesInfoField(
                                            title: context.l10n.carBrand,
                                            value: vehicle.car!.brand!,
                                          ),
                                        ),
                                      Gap(AppSize.w1),
                                      GestureDetector(
                                        onTap: () =>
                                            AppNavigationService.pushNamed(
                                          context,
                                          AppRoutes.tripsCarInfoPage,
                                          extra:
                                              CarInfoParams(carDetail: vehicle),
                                        ),
                                        behavior: HitTestBehavior.opaque,
                                        child: Text(
                                          context.l10n.details,
                                          style: context.textTheme.titleLarge
                                              ?.copyWith(
                                            color: AppColors.primaryColor,
                                            fontSize: AppSize.sp16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (vehicle.car?.year != null &&
                                      vehicle.car?.model != null) ...[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        if (vehicle.car?.model != null)
                                          Flexible(
                                            child: VehiclesInfoField(
                                              title: context.l10n.carModel,
                                              value: vehicle.car!.model!,
                                            ),
                                          ),
                                        Gap(AppSize.w1),
                                        if (vehicle.car?.year != null)
                                          VehiclesInfoField(
                                            title: context.l10n.carYear,
                                            value: vehicle.car!.year!,
                                          ),
                                      ],
                                    ),
                                  ],
                                  if (vehicle.serialNumber != null)
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: VehiclesInfoField(
                                        title: context.l10n.carSerial,
                                        value: vehicle.serialNumber!,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    Gap(AppSize.h10),
                    AnimatedBuilder(
                      animation: _pageController,
                      builder: (context, child) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            for (var i = 0; i < widget.data.length; i++)
                              Container(
                                margin: EdgeInsets.symmetric(
                                  horizontal: AppSize.w4,
                                ),
                                width: AppSize.w10,
                                height: AppSize.h10,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color:
                                      i == (_pageController.page?.round() ?? 0)
                                          ? AppColors.primaryColor
                                          : AppColors.ffCED4DA,
                                ),
                              ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Vehicle info field
class VehiclesInfoField extends StatelessWidget {
  /// Constructor
  const VehiclesInfoField({
    required this.title,
    required this.value,
    super.key,
  });

  /// title
  final String title;

  /// value
  final String value;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: context.textTheme.bodyLarge?.copyWith(
            fontSize: AppSize.sp12,
            color: AppColors.ffADB5BD,
            fontWeight: FontWeight.w500,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        MarqueeWidget(
          child: Text(
            value,
            maxLines: 2,
            style: context.textTheme.bodyLarge?.copyWith(
              fontSize: AppSize.sp18,
              fontWeight: FontWeight.w400,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ],
    );
  }
}
