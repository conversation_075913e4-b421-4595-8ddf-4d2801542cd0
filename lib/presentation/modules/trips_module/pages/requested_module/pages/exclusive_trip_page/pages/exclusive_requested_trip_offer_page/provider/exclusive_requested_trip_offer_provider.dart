import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/places_api_provider_class.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';

import 'package:transportmatch_provider/utils/logger.dart';

class ExclusiveRequestedTripOfferProvider extends ChangeNotifier {
  ExclusiveRequestedTripOfferProvider({
    required this.exclusiveBooking,
    required this.exclusiveRequestedParams,
  }) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if (exclusiveRequestedParams.exclusiveBooking == null &&
          exclusiveRequestedParams.tripId != null) {
        await exclusiveRequestedParams.requestedTripProvider
            .getExclusiveTripBookings(exclusiveRequestedParams.tripId!)
            .then(
          (value) {
            exclusiveBooking = value;
            notify();
          },
        );
      } else {
        exclusiveBooking = exclusiveRequestedParams.exclusiveBooking;
        notify();
      }
      if (exclusiveBooking != null) {
        await exclusiveRequestedParams.tripDataProvider.getDropDownListApiCall(
          startDate:
              exclusiveBooking?.customerStartDate?.dateDropDownApiPramOnly ??
                  '',
          endDate:
              exclusiveBooking?.customerEndDate?.dateDropDownApiPramOnly ?? '',
          isShowLoader: isShowLoader,
        );
      }
    });
  }
  ExclusiveTrip? exclusiveBooking;
  final ExclusiveRequestedParams exclusiveRequestedParams;
  bool isClosed = false;

  /// Google map function and variables
  CameraPosition googleMapInitial = const CameraPosition(
    target: LatLng(37.42796133580664, -122.085749655962),
    zoom: 14.4746,
  );
  GoogleMapController? googleMapController;
  List<Marker> markers = <Marker>[];
  ValueNotifier<List<Polyline>> polyline = ValueNotifier(<Polyline>[]);
  ValueNotifier<int> distance = ValueNotifier(0);
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  Future<void> onMapCreated(
    GoogleMapController controller, {
    required ExclusiveTrip newAssignData,
  }) async {
    try {
      if (isClosed) return;
      googleMapController = controller;
      markers = [];
      polyline.value = [];
      await addGoogleMapMarker(
        LatLng(
          double.parse(
            newAssignData.userStartLocation?.latitude ?? '37.42796133580664',
          ),
          double.parse(
            newAssignData.userStartLocation?.longitude ?? '-122.085749655962',
          ),
        ),
        ApiKeys.start,
      );
      await addGoogleMapMarker(
        LatLng(
          double.parse(
            newAssignData.userEndLocation?.latitude ?? '37.42796133580664',
          ),
          double.parse(
            newAssignData.userEndLocation?.longitude ?? '-122.085749655962',
          ),
        ),
        ApiKeys.end,
      );
    } catch (e) {
      e.toString().logE;
    }
  }

  /// to add google map marker
  Future<void> addGoogleMapMarker(
    LatLng latLng,
    String id, {
    bool isRemove = false,
  }) async {
    if (isClosed) return;
    if (isRemove) {
      markers.removeWhere((element) => element.markerId.value == id);
    } else {
      markers
        ..removeWhere((element) => element.markerId.value == id)
        ..add(
          Marker(
            markerId: MarkerId(id),
            position: latLng,
          ),
        );
      await fitBounds(latLng);
    }
    notify();
  }

  /// to fit bounds
  Future<void> fitBounds(LatLng lat) async {
    if (googleMapController == null || isClosed) return;
    if (markers.length < 2) {
      await googleMapController!
          .moveCamera(CameraUpdate.newLatLngZoom(lat, 10));
      return;
    }

    final bounds = PlaceApiProvider().getLatLngBounds(
      markers.map((e) => e.position).toList(),
    );
    final latList = await PlaceApiProvider().getRouteList(
      origin: LatLng(
        markers[0].position.latitude,
        markers[0].position.longitude,
      ),
      destination: LatLng(
        markers[1].position.latitude,
        markers[1].position.longitude,
      ),
    );
    if (googleMapController == null || isClosed) return;
    await googleMapController!
        .animateCamera(CameraUpdate.newLatLngBounds(bounds, 50))
        .then((v) {
      polyline.value.clear();
      polyline.value = [
        Polyline(
          polylineId: const PolylineId('polyline'),
          points: latList.routeList
              .map((e) => LatLng(e.latitude, e.longitude))
              .toList(),
          color: AppColors.ff0087C7,
        ),
      ];
      distance.value = latList.distance.toInt();
      notify();
    });
  }

  /// This function is to create exclusive trip offer
  CancelToken? createExclusiveTripOfferCancelToken;

  /// This API is called for creating exclusive trip offer
  Future<void> createExclusiveTripOffer(
    BuildContext context, {
    required DateTime startDate,
    required DateTime endDate,
    required int equipmentId,
    required int driverId,
    required int spotAvailableForReservation,
    required int totalTripDistance,
    required double costPerKilometer,
    required int bookingId,
  }) async {
    if (equipmentId == 0) {
      context.l10n.pleaseSelectEquipment.showErrorAlert();
    } else if (spotAvailableForReservation <= 0) {
      context.l10n.reservationSpotGreaterThanZero.showErrorAlert();
    } else if (driverId == 0) {
      context.l10n.pleaseSelectDriver.showErrorAlert();
    } else if (costPerKilometer <= 0) {
      context.l10n.costPerKmGreaterThanZero.showErrorAlert();
    } else if (totalTripDistance <= 0) {
      context.l10n.tripDistanceGreaterThanZero.showErrorAlert();
    } else {
      createExclusiveTripOfferCancelToken?.cancel();
      createExclusiveTripOfferCancelToken = CancelToken();
      isShowLoader.value = true;
      try {
        final result =
            await Injector.instance<TripRepository>().createExclusiveTripOffer(
          ApiRequest(
            path: EndPoints.createExclusiveTripOffer,
            cancelToken: createExclusiveTripOfferCancelToken,
            data: {
              'equipment': equipmentId,
              'driver': driverId,
              'trip_start_date': startDate.toUtc().toIso8601String(),
              'trip_end_date': endDate.toUtc().toIso8601String(),
              'spot_available_for_reservation': spotAvailableForReservation,
              'total_trip_distance': totalTripDistance ~/ 1000,
              'cost_per_kilometer': costPerKilometer,
              // if (date.value != null)
              //   'deadline_date': date.value!.toUtc().toIso8601String(),
              'booking': bookingId,
            },
          ),
        );

        await result.when(
          success: (data) async {
            if ((createExclusiveTripOfferCancelToken?.isCancelled ?? true) ||
                isClosed) {
              return;
            }
            context.l10n.offerSentSuccessfully.showSuccessAlert();
            AppNavigationService.pop(context);
            isShowLoader.value = false;
            notify();
          },
          error: (exception) async {
            isShowLoader.value = false;
            exception.message.showErrorAlert();
          },
        );
      } catch (e) {
        isShowLoader.value = false;
        if ((createExclusiveTripOfferCancelToken?.isCancelled ?? true) ||
            isClosed) {
          return;
        }
        '======>>>> create Exclusive Trip Offer error $e'.logE;
      }
    }
  }

  @override
  void dispose() {
    isClosed = true;
    googleMapController?.dispose();
    googleMapController = null;
    markers.clear();
    polyline.dispose();
    distance.dispose();
    isShowLoader.dispose();
    createExclusiveTripOfferCancelToken?.cancel();
    super.dispose();
  }
}
