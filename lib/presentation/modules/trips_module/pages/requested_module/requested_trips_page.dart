import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/exclusive_trip_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/models/send_offer_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/send_offer_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/common_tab_widgets.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/keep_alive_wrapper.dart';

/// Requested Trips Screen
class RequestedTripsPage extends StatelessWidget {
  /// Constructor
  const RequestedTripsPage({super.key, required this.tripDataProvider});
  final TripDataProvider tripDataProvider;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
      child: ChangeNotifierProvider(
        create: (context) => RequestedTripProvider(),
        builder: (context, chid) {
          return Builder(
            builder: (context) {
              final requestedTripProvider =
                  context.read<RequestedTripProvider>();
              return Column(
                spacing: AppSize.h16,
                children: [
                  Row(
                    spacing: AppSize.w8,
                    // spacing: AppSize.w20,
                    children: [
                      RequestedCommonTabWidgets(
                        text: context.l10n.exclusiveTrip,
                        value: 0,
                        onTap: () =>
                            requestedTripProvider.requestedTabValueChange(0),
                      ),
                      // RequestedCommonTabWidgets(
                      //   text: context.l10n.waitingList,
                      //   value: 1,
                      //   onTap: () =>
                      //       requestedTripProvider.requestedTabValueChange(1),
                      // ),
                      RequestedCommonTabWidgets(
                        text: context.l10n.sentOffers,
                        // value: 1,
                        value: 2,
                        onTap: () =>
                            requestedTripProvider.requestedTabValueChange(2),
                      ),
                    ],
                  ),
                  Expanded(
                    child: PageView.builder(
                      controller: requestedTripProvider.pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, pageIndex) => [
                        KeepAliveWrapper(
                          child: ExclusiveTripPage(
                            requestedTripProvider: requestedTripProvider,
                            tripDataProvider: tripDataProvider,
                          ),
                        ),
                        const KeepAliveWrapper(
                          child: SizedBox.shrink(),
                        ),
                        KeepAliveWrapper(
                          child: SendOfferPage(
                            sendOfferParams: SendOfferParams(
                              requestedTripProvider: requestedTripProvider,
                            ),
                          ),
                        ),
                      ][pageIndex],
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
