import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/dashed_line.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/location_info_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

/// Common Card Widgets for tab
class CommonCardWidgets extends StatelessWidget {
  /// Common Card Constructor
  const CommonCardWidgets({
    required this.value,
    super.key,
    this.exclusiveBooking,
    this.onOfferPressed,
    this.onNotInterestedTripPressed,
    this.onDetailsPressed,
    this.sentOfferModel,
  });

  ///for button ui change
  final int value;

  ///for exclusive booking
  final ExclusiveTrip? exclusiveBooking;

  /// for Details Pressed
  final VoidCallback? onOfferPressed;

  /// for Resume Trip Pressed
  final VoidCallback? onNotInterestedTripPressed;

  /// for Details Pressed
  final VoidCallback? onDetailsPressed;

  /// for Sent Offer screen
  final SentOffer? sentOfferModel;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSize.h16),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(AppSize.r4),
        ),
        child: Padding(
          padding: EdgeInsets.all(AppSize.sp16)
              .subtract(EdgeInsets.only(bottom: value != 3 ? AppSize.sp7 : 0)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with "Your Shipment" and "Edit"
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TitleInfoWidget(
                        title: context.l10n.no_of_vehicles,
                        subTitle:
                            exclusiveBooking?.carDetails?.length.toString() ??
                                sentOfferModel?.totalCar?.toString() ??
                                '0',
                        subTitleFontSize: AppSize.sp18,
                      ),
                    ],
                  ),
                  if (value == 3)
                    GestureDetector(
                      onTap: onDetailsPressed,
                      child: Text(
                        context.l10n.details,
                        style: context.textTheme.titleLarge?.copyWith(
                          color: AppColors.ff0087C7,
                          fontWeight: FontWeight.w600,
                          fontSize: AppSize.sp16,
                        ),
                      ),
                    ),
                ],
              ),

              if (value == 1) ...[
                Gap(AppSize.h10),
                TitleInfoWidget(
                  title: context.l10n.clientName,
                  subTitle: '${exclusiveBooking?.customer?.firstName ?? ""}'
                          ' ${exclusiveBooking?.customer?.lastName ?? ""}'
                      .trim(),
                  subTitleFontSize: AppSize.sp18,
                ),
              ],
              Gap(AppSize.h10),

              /// Locations and dates row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: LocationInfo(
                      title: exclusiveBooking?.userStartLocation?.street ??
                          sentOfferModel
                              ?.exclusiveTrips?.userStartLocation?.street ??
                          '',
                      date: exclusiveBooking?.customerStartDate?.monthDate ??
                          sentOfferModel?.tripStartDate?.monthDate ??
                          '',
                      latitude: exclusiveBooking?.userStartLocation?.latitude ??
                          sentOfferModel
                              ?.exclusiveTrips?.userStartLocation?.latitude ??
                          '',
                      longitude:
                          exclusiveBooking?.userStartLocation?.longitude ??
                              sentOfferModel?.exclusiveTrips?.userStartLocation
                                  ?.longitude ??
                              '',
                      icon: AppAssets.iconsCalender.image(
                        height: AppSize.h10,
                      ),
                    ),
                  ),
                  Flexible(
                    child: Row(
                      children: [
                        AppAssets.iconsLocationOrigin.image(
                          height: AppSize.h14,
                        ),
                        SizedBox(
                          width: AppSize.w70,
                          child: const DashedDivider(
                              /* dashWidth: ApZpSize.w4,
                                indent: AppSize.w2,
                                endIndent: AppSize.w2,*/
                              ),
                        ),
                        AppAssets.iconsLocation.image(
                          height: AppSize.h14,
                        ),
                      ],
                    ),
                  ),
                  Flexible(
                    child: LocationInfo(
                      title: exclusiveBooking?.userEndLocation?.street ??
                          sentOfferModel
                              ?.exclusiveTrips?.userEndLocation?.street ??
                          '',
                      date: exclusiveBooking?.customerEndDate?.monthDate ??
                          sentOfferModel?.tripEndDate?.monthDate ??
                          '',
                      latitude: exclusiveBooking?.userEndLocation?.latitude ??
                          sentOfferModel
                              ?.exclusiveTrips?.userEndLocation?.latitude ??
                          '',
                      longitude: exclusiveBooking?.userEndLocation?.longitude ??
                          sentOfferModel
                              ?.exclusiveTrips?.userEndLocation?.longitude ??
                          '',
                      icon: AppAssets.iconsCalender.image(
                        height: AppSize.h10,
                      ),
                    ),
                  ),
                ],
              ),
              if (value == 1 || value == 2) ...[
                Gap(AppSize.h10),
                const Divider(color: AppColors.ffDEE2E6),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    if (exclusiveBooking?.isOfferRejected ?? false)
                      Text(
                        l10n.youHadRejectedThisTrip,
                        style: context.textTheme.bodySmall
                            ?.copyWith(fontSize: AppSize.sp12),
                      ),
                    TextButton(
                      onPressed: onOfferPressed,
                      child: Text(
                        context.l10n.offer,
                        style: context.textTheme.titleLarge?.copyWith(
                          color: AppColors.successColor,
                          fontSize: AppSize.sp16,
                        ),
                      ),
                    ),
                    if (exclusiveBooking?.isOfferRejected == false)
                      TextButton(
                        onPressed: onNotInterestedTripPressed,
                        child: Text(
                          context.l10n.notInterested,
                          style: context.textTheme.titleLarge?.copyWith(
                            color: AppColors.red,
                            fontSize: AppSize.sp16,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
