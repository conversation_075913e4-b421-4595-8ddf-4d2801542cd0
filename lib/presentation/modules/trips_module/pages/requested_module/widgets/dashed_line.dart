// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

/// DashedDivider
class DashedDivider extends StatelessWidget {
  const DashedDivider({
    super.key,
    this.color = Colors.black,
  });
  final Color color;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity, // Full width of the parent
      height: 1, // Thickness of the divider
      child: CustomPaint(
        painter: Dashed<PERSON>inePainter(
          color: color,
        ),
      ),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  DashedLinePainter({required this.color});
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = size.height
      ..style = PaintingStyle.stroke;

    // Define dash width and gap automatically based on size
    var dashWidth = 6;
    var dashGap = 6;

    // Adjust dash width and gap to fit the width
    final dashCount = (size.width / (dashWidth + dashGap)).floor();
    final totalDashWidth = dashCount * dashWidth;
    final totalGapWidth = (dashCount - 1) * dashGap;

    if (totalDashWidth + totalGapWidth > size.width) {
      // Recalculate dash and gap sizes to fit the space
      final adjustedWidth = size.width / (2 * dashCount - 1);
      dashWidth = adjustedWidth.toInt();
      dashGap = adjustedWidth.toInt();
    }

    var startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX.toDouble(), 0),
        Offset(startX.toDouble() + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashGap;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
