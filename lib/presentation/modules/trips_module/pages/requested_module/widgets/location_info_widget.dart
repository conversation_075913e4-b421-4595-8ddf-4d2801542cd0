import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';

/// Location info widget
class LocationInfo extends StatelessWidget {
  /// Constructor
  const LocationInfo({
    required this.title,
    required this.date,
    required this.icon,
    required this.latitude,
    required this.longitude,
    super.key,
  });

  /// Location title
  final String title;

  /// Location date
  final String date;

  /// Location icon
  final Widget icon;

  final String? latitude;
  final String? longitude;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (title.isNotEmpty)
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: AppSize.sp14,
              fontWeight: FontWeight.w400,
            ),
          ),
        if (date.isNotEmpty)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon,
              Gap(AppSize.w4),
              Text(
                date,
                style: TextStyle(
                  fontSize: AppSize.sp12,
                  color: AppColors.ff6C757D,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        if ((latitude?.isNotEmpty ?? false) &&
            (longitude?.isNotEmpty ?? false) &&
            latitude != '0' &&
            longitude != '0')
          Gap(AppSize.h4),
        GestureDetector(
          onTap: () {
            double.tryParse(latitude ?? '') != null &&
                    double.tryParse(longitude ?? '') != null
                ? AppCommonFunctions.openMap(
                    double.tryParse(latitude ?? '')!,
                    double.tryParse(longitude ?? '')!,
                  )
                : null;
          },
          child: Text(
            l10n.openMap,
            style: context.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.primaryColor.withValues(alpha: 0.7),
              decoration: TextDecoration.underline,
              decorationColor: AppColors.primaryColor.withValues(alpha: 0.7),
            ),
          ),
        ),
      ],
    );
  }
}
