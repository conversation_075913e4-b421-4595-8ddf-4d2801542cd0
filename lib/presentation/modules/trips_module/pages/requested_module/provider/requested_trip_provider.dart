import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class RequestedTripProvider extends ChangeNotifier {
  bool isClosed = false;
  PageController pageController = PageController();
  ValueNotifier<bool> isShowLoaderForExclusiveTrips = ValueNotifier(false);
  ValueNotifier<bool> isShowLoaderForWaitingList = ValueNotifier(false);
  ValueNotifier<bool> isShowLoaderForSentOffers = ValueNotifier(false);

  // Lists for different tab types
  List<ExclusiveTrip> requestedExclusiveBookings = [];
  List<ExclusiveTrip> waitingListBookings =
      []; // Currently not used but added for future use
  List<SentOffer> sentOfferList = [];

  // Legacy lists for backward compatibility
  ValueNotifier<DateTime?> date = ValueNotifier(null);

  final costPerKmController = TextEditingController();
  final spotAvail = TextEditingController();
  final totalCostController = TextEditingController();

  /// requested Value for changing
  int requestedTabValue = 0;

  final exclusiveTripRefreshController = RefreshController();
  final waitingListRefreshController = RefreshController();
  final sentOffersRefreshController = RefreshController();

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// update requested Value
  void requestedTabValueChange(int value) {
    if (isClosed) return;
    try {
      requestedTabValue = value;
      pageController.jumpToPage(value);
      notify();
    } catch (e) {
      '======>>>> requestedTabValueChange error $e'.logE;
    }
  }

  /// Pagination URLs for different tab types
  CancelToken? cancelToken;
  String? exclusiveTripsNextUrl;
  String? waitingListNextUrl; // Currently not used but added for future use
  String? sentOffersNextUrl;

  /// Set the loader value based on status
  void isShowLoader({required bool value}) {
    if (isClosed) return;
    try {
      switch (requestedTabValue) {
        case 0:
          isShowLoaderForExclusiveTrips.value = value;
        case 1:
          isShowLoaderForWaitingList.value = value;
        case 2:
          isShowLoaderForSentOffers.value = value;
        default:
          isShowLoaderForExclusiveTrips.value = value;
      }
    } catch (e) {
      '======>>>> isShowLoader error $e'.logE;
    }
  }

  /// This function is to get requested exclusive bookings
  Future<void> getRequestedExclusiveBookings({
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;
    if (!isPagination) {
      exclusiveTripsNextUrl = null;
    }

    try {
      cancelToken?.cancel();
      cancelToken = CancelToken();
      if (isWantShowLoader) isShowLoader(value: true);

      final result = await Injector.instance<TripRepository>()
          .getRequestedExclusiveBookings(
        ApiRequest(
          path:
              exclusiveTripsNextUrl ?? EndPoints.getRequestedExclusiveBookings,
          cancelToken: cancelToken,
        ),
      );

      final dummyList = [...requestedExclusiveBookings];

      await result.when(
        success: (data) async {
          if (isClosed || (cancelToken?.isCancelled ?? true)) return;

          if (!isPagination) dummyList.clear();
          dummyList.addAll(data.results ?? []);
          requestedExclusiveBookings = [...dummyList];
          // Update pagination URLs
          exclusiveTripsNextUrl = data.next;
          isShowLoader(value: false);
          notify();
        },
        error: (exception) async {
          if (isClosed || (cancelToken?.isCancelled ?? true)) return;
          isShowLoader(value: false);
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (cancelToken?.isCancelled ?? true)) return;
      isShowLoader(value: false);
      '======>>>> getRequestedExclusiveBookings error $e'.logE;
      e.toString().logE;
    }
  }

  /// Refresh exclusive trips list
  Future<void> refreshExclusiveTrips({bool isWantShowLoader = false}) async {
    if (isClosed) return;
    try {
      // Reset pagination URL
      exclusiveTripsNextUrl = null;
      // Clear list
      requestedExclusiveBookings.clear();
      // Load data
      await getRequestedExclusiveBookings(isWantShowLoader: isWantShowLoader);
    } catch (e) {
      if (isClosed) return;
      '======>>>> refreshExclusiveTrips error $e'.logE;
      e.toString().logE;
    }
  }

  /// This function is to get requested exclusive bookings
  CancelToken? rejectExclusiveTripCancelToken;
  Future<void> rejectExclusiveTrip(int bookingId) async {
    if (isClosed) return;
    rejectExclusiveTripCancelToken?.cancel();
    rejectExclusiveTripCancelToken = CancelToken();
    isShowLoader(value: true);
    try {
      final result =
          await Injector.instance<TripRepository>().rejectExclusiveTrip(
        ApiRequest(
          path: EndPoints.rejectExclusiveTripOffer,
          cancelToken: rejectExclusiveTripCancelToken,
          data: {'status': 'REJECTED', 'booking': bookingId},
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed ||
              (rejectExclusiveTripCancelToken?.isCancelled ?? true)) {
            return;
          }
          requestedExclusiveBookings
              .removeWhere((element) => element.id == bookingId);
          rootNavKey.currentContext?.l10n.exclusiveTripRejectedSuccessfully
              .showSuccessAlert();
          notify();
        },
        error: (exception) async {
          if (isClosed ||
              (rejectExclusiveTripCancelToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (rejectExclusiveTripCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader(value: false);
      '======>>>> reject Exclusive Trip Cancel Token error $e'.logE;
    }
    isShowLoader(value: false);
  }

  /// This function is to get sent offer list
  CancelToken? getSentOfferListCancelToken;

  Future<void> getSentOfferList({
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;
    if (!isPagination) {
      sentOffersNextUrl = null;
    }

    getSentOfferListCancelToken?.cancel();
    getSentOfferListCancelToken = CancelToken();
    if (isWantShowLoader) isShowLoader(value: true);

    try {
      final result =
          await Injector.instance<TripRepository>().getSentExclusiveBookings(
        ApiRequest(
          path: sentOffersNextUrl ?? EndPoints.getSentOfferTrips,
          cancelToken: getSentOfferListCancelToken,
        ),
      );

      final tempList = <SentOffer>[...sentOfferList];

      await result.when(
        success: (data) async {
          if (isClosed || (getSentOfferListCancelToken?.isCancelled ?? true)) {
            return;
          }

          if (!isPagination) tempList.clear();
          tempList.addAll(data.results ?? []);
          sentOfferList = tempList;

          // Update pagination URLs
          sentOffersNextUrl = data.next;
          notify();
        },
        error: (exception) async {
          if (isClosed || (getSentOfferListCancelToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getSentOfferListCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader(value: false);
      '======>>>> get Sent Offer List error $e'.logE;
    }

    isShowLoader(value: false);
  }

  /// Refresh sent offers list
  Future<void> refreshSentOffers({bool isWantShowLoader = false}) async {
    if (isClosed) return;
    try {
      // Reset pagination URL
      sentOffersNextUrl = null;
      // Clear list
      sentOfferList.clear();
      // Load data
      await getSentOfferList(isWantShowLoader: isWantShowLoader);
    } catch (e) {
      if (isClosed) return;
      '======>>>> refreshSentOffers error $e'.logE;
      e.toString().logE;
    }
  }

  /// Refresh waiting list
  Future<void> refreshWaitingList() async {
    if (isClosed) return;
    try {
      // This is a placeholder for future implementation
      // Currently the waiting list is static data
      notify();
    } catch (e) {
      if (isClosed) return;
      '======>>>> refreshWaitingList error $e'.logE;
      e.toString().logE;
    }
  }

  /// Helper method to get the current list based on tab value
  List<dynamic> getCurrentList() {
    if (isClosed) return [];
    try {
      switch (requestedTabValue) {
        case 0: // Exclusive Trips
          return requestedExclusiveBookings;
        case 1: // Waiting List
          return waitingListBookings;
        case 2: // Sent Offers
          return sentOfferList;
        default:
          return requestedExclusiveBookings;
      }
    } catch (e) {
      '======>>>> getCurrentList error $e'.logE;
      return [];
    }
  }

  /// Helper method to get the current next URL based on tab value
  String? getCurrentNextUrl() {
    if (isClosed) return null;
    try {
      switch (requestedTabValue) {
        case 0: // Exclusive Trips
          return exclusiveTripsNextUrl;
        case 1: // Waiting List
          return waitingListNextUrl;
        case 2: // Sent Offers
          return sentOffersNextUrl;
        default:
          return exclusiveTripsNextUrl;
      }
    } catch (e) {
      '======>>>> getCurrentNextUrl error $e'.logE;
      return null;
    }
  }

  /// Refresh all lists based on current tab
  Future<void> refreshCurrentTab() async {
    if (isClosed) return;
    try {
      if (requestedTabValue == 0) {
        await refreshExclusiveTrips();
      } else if (requestedTabValue == 1) {
        await refreshWaitingList();
      } else if (requestedTabValue == 2) {
        await refreshSentOffers();
      }
    } catch (e) {
      if (isClosed) return;
      '======>>>> refreshCurrentTab error $e'.logE;
      e.toString().logE;
    }
  }

  CancelToken? getExclusiveTripBookingsToken;
  final isDetailShowLoader = ValueNotifier(false);
  Future<ExclusiveTrip?> getExclusiveTripBookings(int bookingId) async {
    final completer = Completer<ExclusiveTrip?>();
    if (isClosed) return null;
    isDetailShowLoader.value = true;
    getExclusiveTripBookingsToken?.cancel();
    getExclusiveTripBookingsToken = CancelToken();
    // isLoading = true;
    try {
      final result =
          await Injector.instance<TripRepository>().getRequestedTripDetail(
        ApiRequest(
          path: EndPoints.getRequestedTripDetail(bookingId.toString()),
          cancelToken: getExclusiveTripBookingsToken,
        ),
      );

      isDetailShowLoader.value = false;
      await result.when(
        success: (data) async {
          if (isClosed ||
              (getExclusiveTripBookingsToken?.isCancelled ?? true)) {
            return null;
          }
          completer.complete(data);
        },
        error: (exception) async {
          if (isClosed ||
              (getExclusiveTripBookingsToken?.isCancelled ?? true)) {
            return null;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isDetailShowLoader.value = false;
      if (isClosed || (getExclusiveTripBookingsToken?.isCancelled ?? true)) {
        return null;
      }
      '======>>>> getExclusiveTripBookings error $e'.logE;
    }
    return completer.future;
    // isLoading = false;
  }

  @override
  void dispose() {
    isClosed = true;
    cancelToken?.cancel();
    rejectExclusiveTripCancelToken?.cancel();
    getSentOfferListCancelToken?.cancel();
    costPerKmController.dispose();
    spotAvail.dispose();
    totalCostController.dispose();
    requestedExclusiveBookings.clear();
    waitingListBookings.clear();
    sentOfferList.clear();
    isShowLoaderForExclusiveTrips.dispose();
    isShowLoaderForWaitingList.dispose();
    isShowLoaderForSentOffers.dispose();
    date.dispose();
    exclusiveTripsNextUrl = null;
    waitingListNextUrl = null;
    sentOffersNextUrl = null;
    pageController.dispose();
    exclusiveTripRefreshController.dispose();
    waitingListRefreshController.dispose();
    sentOffersRefreshController.dispose();
    super.dispose();
  }
}
