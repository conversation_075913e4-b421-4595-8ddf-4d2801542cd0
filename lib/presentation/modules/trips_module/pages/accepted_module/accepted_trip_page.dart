import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/completed_page/completed_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/ongoing_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/upcoming_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/provider/accepted_trips_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/common_tab_widgets.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/keep_alive_wrapper.dart';

class AcceptedTripPage extends StatelessWidget {
  /// Constructor
  const AcceptedTripPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return AppPadding.symmetric(
      horizontal: AppSize.appPadding,
      child: ChangeNotifierProvider<AcceptedTripsProvider>(
        create: (context) => AcceptedTripsProvider(),
        child: Builder(
          builder: (context) {
            final acceptedTripsProvider = context.read<AcceptedTripsProvider>();
            return Column(
              spacing: AppSize.h16,
              children: [
                Row(
                  spacing: AppSize.w8,
                  children: [
                    AcceptedCommonTabWidgets(
                      text: l10n.upcoming,
                      value: 0,
                      onTap: () => acceptedTripsProvider.acceptedTabValueChange(0),
                    ),
                    AcceptedCommonTabWidgets(
                      text: l10n.ongoing,
                      value: 1,
                      onTap: () => acceptedTripsProvider.acceptedTabValueChange(1),
                    ),
                    AcceptedCommonTabWidgets(
                      text: l10n.completed,
                      value: 2,
                      onTap: () => acceptedTripsProvider.acceptedTabValueChange(2),
                    ),
                  ],
                ),
                Expanded(
                  child: PageView.builder(
                    controller: acceptedTripsProvider.pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, pageIndex) => [
                      KeepAliveWrapper(
                        child: UpcomingPage(
                          acceptedTripsProvider: acceptedTripsProvider,
                        ),
                      ),
                      KeepAliveWrapper(
                        child: OngoingPage(
                          acceptedTripsProvider: acceptedTripsProvider,
                        ),
                      ),
                      KeepAliveWrapper(
                        child: CompletedScreen(
                          acceptedTripsProvider: acceptedTripsProvider,
                        ),
                      ),
                    ][pageIndex],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
