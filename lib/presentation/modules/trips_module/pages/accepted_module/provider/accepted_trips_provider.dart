// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// my Trips Provider
class AcceptedTripsProvider extends ChangeNotifier {
  bool isClosed = false;
  final PageController pageController = PageController();
  final ValueNotifier<bool> isShowLoaderForActiveTrips = ValueNotifier(false);
  final ValueNotifier<bool> isShowLoaderForOngoingTrips = ValueNotifier(false);
  final ValueNotifier<bool> isShowLoaderForCompletedTrips =
      ValueNotifier(false);

  /// accepted Value for changing
  int acceptedTabValue = 0;

  /// Lists for different trip statuses
  List<AcceptedTripModelData> activeTripsList = [];
  List<AcceptedTripModelData> ongoingTripsList = [];
  List<AcceptedTripModelData> completedTripsList = [];

  // /// Legacy list for backward compatibility
  // List<AcceptedTrip> acceptedTripsList = [];

  final spotAvail = TextEditingController();
  final completedPageRefreshController = RefreshController();
  final activePageRefreshController = RefreshController();
  final ongoingPageRefreshController = RefreshController();

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// update requested Value
  void acceptedTabValueChange(int value) {
    if (isClosed) return;
    try {
      acceptedTabValue = value;
      pageController.jumpToPage(value);
      notify();
    } catch (e) {
      e.toString().logE;
    }
  }

  /// Pagination URLs for different trip statuses
  CancelToken? activeTripsCancelToken;
  CancelToken? ongoingTripsCancelToken;
  CancelToken? completedTripsCancelToken;
  String? activeTripsNextUrl;
  String? ongoingTripsNextUrl;
  String? completedTripsNextUrl;

  /// Get the appropriate list based on status
  List<AcceptedTripModelData> getListByStatus(String status) {
    try {
      switch (status) {
        case 'ACTIVE':
          return activeTripsList;
        case 'ONGOING':
          return ongoingTripsList;
        case 'COMPLETED':
          return completedTripsList;
        default:
          return activeTripsList;
      }
    } catch (e) {
      e.toString().logE;
      return [];
    }
  }

  /// Set the loader value based on status
  void isShowLoader({required bool value, required String status}) {
    if (isClosed) return;
    try {
      switch (status) {
        case 'ACTIVE':
          isShowLoaderForActiveTrips.value = value;
        case 'ONGOING':
          isShowLoaderForOngoingTrips.value = value;
        case 'COMPLETED':
          isShowLoaderForCompletedTrips.value = value;
        default:
          isShowLoaderForActiveTrips.value = value;
      }
    } catch (e) {
      e.toString().logE;
    }
  }

  /// Get the appropriate cancel token based on status
  CancelToken? getCancelTokenByStatus(String status) {
    try {
      switch (status) {
        case 'ACTIVE':
          return activeTripsCancelToken;
        case 'ONGOING':
          return ongoingTripsCancelToken;
        case 'COMPLETED':
          return completedTripsCancelToken;
        default:
          return activeTripsCancelToken;
      }
    } catch (e) {
      e.toString().logE;
      return null;
    }
  }

  /// Set the appropriate cancel token based on status
  void setCancelTokenByStatus(String status) {
    try {
      switch (status) {
        case 'ACTIVE':
          activeTripsCancelToken = CancelToken();
        case 'ONGOING':
          ongoingTripsCancelToken = CancelToken();
        case 'COMPLETED':
          completedTripsCancelToken = CancelToken();
        default:
          activeTripsCancelToken = CancelToken();
      }
    } catch (e) {
      e.toString().logE;
    }
  }

  /// Get the appropriate next URL based on status
  String? getNextUrlByStatus(String status) {
    try {
      switch (status) {
        case 'ACTIVE':
          return activeTripsNextUrl;
        case 'ONGOING':
          return ongoingTripsNextUrl;
        case 'COMPLETED':
          return completedTripsNextUrl;
        default:
          return activeTripsNextUrl;
      }
    } catch (e) {
      e.toString().logE;
      return null;
    }
  }

  /// Set the appropriate next URL based on status
  void setNextUrlByStatus(String status, String? url) {
    try {
      if (status == 'ACTIVE') {
        activeTripsNextUrl = url;
      } else if (status == 'ONGOING') {
        ongoingTripsNextUrl = url;
      } else if (status == 'COMPLETED') {
        completedTripsNextUrl = url;
      }
    } catch (e) {
      e.toString().logE;
    }
  }

  /// This function is to get accepted trip list
  Future<void> getAcceptedTrips({
    bool isPagination = false,
    bool isWantShowLoader = false,
    String status = 'ACTIVE',
  }) async {
    if (isClosed) return;
    try {
      // Get the appropriate next URL based on status
      final currentNextUrl = getNextUrlByStatus(status);

      if (!isPagination) {
        setNextUrlByStatus(status, null);
      }

      getCancelTokenByStatus(status)?.cancel();
      setCancelTokenByStatus(status);
      if (isWantShowLoader) isShowLoader(value: true, status: status);

      if (!isPagination) {
        // Clear only the list for the current status
        if (status == 'ACTIVE') {
          activeTripsList.clear();
        } else if (status == 'ONGOING') {
          ongoingTripsList.clear();
        } else if (status == 'COMPLETED') {
          completedTripsList.clear();
        }
      }

      final result = await Injector.instance<TripRepository>().getAcceptedTrips(
        ApiRequest(
          path: currentNextUrl ?? EndPoints.getAcceptedTrips(status),
          cancelToken: getCancelTokenByStatus(status),
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed ||
              (getCancelTokenByStatus(status)?.isCancelled ?? true)) {
            return;
          }
          late List<AcceptedTripModelData> updatedList;
          if (status == 'ACTIVE') {
            updatedList = [...activeTripsList, ...(data.results ?? [])];
            activeTripsList = updatedList;
            // activeTripsList.addAll(data.results ?? []);
          } else if (status == 'ONGOING') {
            updatedList = [...ongoingTripsList, ...(data.results ?? [])];
            ongoingTripsList = updatedList;
            // ongoingTripsList.addAll(data.results ?? []);
          } else if (status == 'COMPLETED') {
            updatedList = [
              ...completedTripsList,
              ...(data.results ?? []),
            ];
            completedTripsList = updatedList;
            // completedTripsList.addAll(data.results ?? []);
          }

          // Update the appropriate next URL based on status
          setNextUrlByStatus(status, data.next);

          notify();
        },
        error: (exception) async {
          if (isClosed ||
              (getCancelTokenByStatus(status)?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed) return;
      e.toString().logE;
    } finally {
      isShowLoader(value: false, status: status);
    }
  }

  /// Helper method to get the current list based on tab value
  List<AcceptedTripModelData> getCurrentList() {
    if (isClosed) return [];
    try {
      switch (acceptedTabValue) {
        case 0: // Upcoming/Active
          return activeTripsList;
        case 1: // Ongoing
          return ongoingTripsList;
        case 2: // Completed
          return completedTripsList;
        default:
          return activeTripsList;
      }
    } catch (e) {
      e.toString().logE;
      return [];
    }
  }

  /// Helper method to get the current next URL based on tab value
  String? getCurrentNextUrl() {
    if (isClosed) return null;
    try {
      switch (acceptedTabValue) {
        case 0: // Upcoming/Active
          return activeTripsNextUrl;
        case 1: // Ongoing
          return ongoingTripsNextUrl;
        case 2: // Completed
          return completedTripsNextUrl;
        default:
          return activeTripsNextUrl;
      }
    } catch (e) {
      e.toString().logE;
      return null;
    }
  }

  /// Refresh all trip lists
  Future<void> refreshAllTrips() async {
    if (isClosed) return;
    try {
      // Reset all pagination URLs
      activeTripsNextUrl = null;
      ongoingTripsNextUrl = null;
      completedTripsNextUrl = null;

      // Clear all lists
      activeTripsList.clear();
      ongoingTripsList.clear();
      completedTripsList.clear();

      // Load data for the current tab
      String status;
      if (acceptedTabValue == 0) {
        status = 'ACTIVE';
      } else if (acceptedTabValue == 1) {
        status = 'ONGOING';
      } else if (acceptedTabValue == 2) {
        status = 'COMPLETED';
      } else {
        status = 'ACTIVE';
      }

      await getAcceptedTrips(status: status);
      notify();
    } catch (e) {
      if (isClosed) return;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    activeTripsCancelToken?.cancel();
    ongoingTripsCancelToken?.cancel();
    completedTripsCancelToken?.cancel();
    spotAvail.dispose();
    activeTripsList.clear();
    ongoingTripsList.clear();
    completedTripsList.clear();
    activeTripsNextUrl = null;
    ongoingTripsNextUrl = null;
    completedTripsNextUrl = null;
    isShowLoaderForActiveTrips.dispose();
    isShowLoaderForOngoingTrips.dispose();
    isShowLoaderForCompletedTrips.dispose();
    pageController.dispose();
    completedPageRefreshController.dispose();
    activePageRefreshController.dispose();
    ongoingPageRefreshController.dispose();
    super.dispose();
  }
}
