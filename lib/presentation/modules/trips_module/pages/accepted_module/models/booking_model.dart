import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';

// class BookingModelData {
//   BookingModelData({
//     this.count,
//     this.next,
//     this.previous,
//     this.results,
//   });

//   factory BookingModelData.fromJson(Map<String, dynamic> json) =>
//       BookingModelData(
//         count: json['count'] as int?,
//         next: json['next'],
//         previous: json['previous'],
//         results: json['results'] == null
//             ? []
//             : List<BookingModel>.from(
//                 (json['results'] as List?)?.map(
//                       (x) => BookingModel.fromJson(x as Map<String, dynamic>),
//                     ) ??
//                     [],
//               ),
//       );
//   int? count;
//   dynamic next;
//   dynamic previous;
//   List<BookingModel>? results;

//   Map<String, dynamic> toJson() => {
//         'count': count,
//         'next': next,
//         'previous': previous,
//         'results': results == null
//             ? []
//             : List<dynamic>.from(results!.map((x) => x.toJson())),
//       };
// }

class BookingModel {
  BookingModel({
    this.id,
    this.isTripBookingCancelled,
    this.isRemainingPaymentButtonEnabled,
    this.status,
    this.bookingDetailId,
    this.netTransportationCharge,
    this.totalConfirmedCar,
    this.booking,
    this.bookedCars,
    this.sharedBookings,
    this.exclusiveBookings,
    this.notes,
    this.customerChatRoom,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'] as int?,
      isTripBookingCancelled: json['is_trip_booking_cancelled'] as bool?,
      isRemainingPaymentButtonEnabled:
          json['is_remaining_payment_button_enabled'] as bool?,
      status: json['status'] as String?,
      bookingDetailId: json['booking_detail_id'] as String?,
      netTransportationCharge: json['net_transportation_charge'] as num?,
      totalConfirmedCar: json['total_confirmed_car'] as int?,
      booking: json['booking'] == null
          ? null
          : Booking.fromJson(json['booking'] as Map<String, dynamic>),
      bookedCars: json['booked_cars'] == null
          ? []
          : List<BookedCar>.from(
              (json['booked_cars'] as List?)?.map(
                    (x) => BookedCar.fromJson(x as Map<String, dynamic>),
                  ) ??
                  [],
            ),
      sharedBookings: json['shared_bookings'] == null
          ? []
          : List<SharedBooking>.from(
              (json['shared_bookings'] as List?)?.map(
                    (x) => SharedBooking.fromJson(x as Map<String, dynamic>),
                  ) ??
                  [],
            ),
      notes: json['notes'] == null
          ? []
          : List<NoteModel>.from(
              (json['notes'] as List?)?.map(
                    (x) => NoteModel.fromJson(x as Map<String, dynamic>),
                  ) ??
                  [],
            ),
      exclusiveBookings: json['exclusive_bookings'] == null
          ? []
          : List<ExclusiveModel>.from(
              (json['exclusive_bookings'] as List?)?.map(
                    (x) => ExclusiveModel.fromJson(x as Map<String, dynamic>),
                  ) ??
                  [],
            ),
      customerChatRoom: json['customer_chat_room_detail'] != null
          ? CustomerChatRoom.fromJson(
              json['customer_chat_room_detail'] as Map<String, dynamic>,
            )
          : null,
    );
  }
  int? id;
  bool? isTripBookingCancelled;
  bool? isRemainingPaymentButtonEnabled;
  String? status;
  String? bookingDetailId;
  dynamic netTransportationCharge;
  int? totalConfirmedCar;
  Booking? booking;
  List<BookedCar>? bookedCars;
  List<SharedBooking>? sharedBookings;
  List<ExclusiveModel>? exclusiveBookings;
  List<NoteModel>? notes;
  CustomerChatRoom? customerChatRoom;

  Map<String, dynamic> toJson() => {
        'id': id,
        'is_trip_booking_cancelled': isTripBookingCancelled,
        'is_remaining_payment_button_enabled': isRemainingPaymentButtonEnabled,
        'status': status,
        'booking_detail_id': bookingDetailId,
        'net_transportation_charge': netTransportationCharge,
        'total_confirmed_car': totalConfirmedCar,
        'booking': booking?.toJson(),
        'booked_cars': bookedCars == null
            ? []
            : List<dynamic>.from(bookedCars!.map((x) => x.toJson())),
        'shared_bookings': sharedBookings == null
            ? []
            : List<dynamic>.from(sharedBookings!.map((x) => x.toJson())),
        'notes': notes == null
            ? []
            : List<dynamic>.from(notes!.map((x) => x.toJson())),
        'exclusive_bookings': exclusiveBookings == null
            ? []
            : List<dynamic>.from(
                exclusiveBookings?.map((x) => x.toJson()) ?? [],
              ),
        'customer_chat_room_detail': customerChatRoom?.toJson(),
      };
}

class BookedCar {
  BookedCar({
    this.id,
    this.serialNumber,
    this.requiredWinch,
    this.netCarCharge,
    this.netTransportationCarCharge,
    this.carDescription,
    this.status,
    this.refundStatus,
    this.bookedCarId,
    this.car,
    this.charges,
    this.bookingType,
    this.isHandedOverToDriver,
    this.images,
  });

  factory BookedCar.fromJson(Map<String, dynamic> json) => BookedCar(
        id: json['id'] as int?,
        serialNumber: json['serial_number'] as String?,
        bookingType: json['booking_type'] as String?,
        requiredWinch: json['required_winch'] as bool?,
        netCarCharge: json['net_car_charge'] as num?,
        netTransportationCarCharge:
            json['net_transportation_car_charge'] as num?,
        carDescription: json['car_description'] as String?,
        status: json['status'] as String?,
        refundStatus: json['refund_status'] as String?,
        bookedCarId: json['booked_car_id'] as String?,
        isHandedOverToDriver: json['is_handed_over_to_driver'] as bool?,
        car: json['car'] == null
            ? null
            : Car.fromJson(json['car'] as Map<String, dynamic>),
        charges: json['charges'] == null
            ? []
            : List<Charge>.from(
                (json['charges'] as List?)?.map(
                      (x) => Charge.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
        images: json['images'] == null
            ? []
            : List<CarImageModel>.from(
                (json['images'] as List?)?.map(
                      (x) => CarImageModel.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
      );
  int? id;
  String? serialNumber;
  String? bookingType;
  bool? requiredWinch;
  bool? isHandedOverToDriver;
  num? netCarCharge;
  num? netTransportationCarCharge;
  String? carDescription;
  String? status;
  dynamic refundStatus;
  String? bookedCarId;
  Car? car;
  List<Charge>? charges;
  List<CarImageModel>? images;

  Map<String, dynamic> toJson() => {
        'id': id,
        'serial_number': serialNumber,
        'booking_type': bookingType,
        'required_winch': requiredWinch,
        'net_car_charge': netCarCharge,
        'net_transportation_car_charge': netTransportationCarCharge,
        'car_description': carDescription,
        'status': status,
        'refund_status': refundStatus,
        'booked_car_id': bookedCarId,
        'is_handed_over_to_driver': isHandedOverToDriver,
        'car': car?.toJson(),
        'charges': charges == null
            ? []
            : List<dynamic>.from(
                (charges as List?)?.map((x) => x.toJson()) ?? [],
              ),
        'images': images == null
            ? []
            : List<dynamic>.from(
                (images as List?)?.map((x) => x.toJson()) ?? [],
              ),
      };
}

class Car {
  Car({
    this.id,
    this.size,
    this.model,
    this.year,
    this.brand,
    this.createdAt,
    this.isDeleted,
    this.deletedAt,
  });

  factory Car.fromJson(Map<String, dynamic> json) => Car(
        id: json['id'] as int?,
        size: json['size'] as num?,
        model: json['model'] as String?,
        year: json['year'] as String?,
        brand: json['brand'] as String?,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
        isDeleted: json['is_deleted'] as bool?,
        deletedAt: json['deleted_at'] == null
            ? null
            : DateTime.parse(json['deleted_at'] as String),
      );
  int? id;
  num? size;
  String? model;
  String? year;
  String? brand;
  DateTime? createdAt;
  bool? isDeleted;
  DateTime? deletedAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'size': size,
        'model': model,
        'year': year,
        'brand': brand,
        'created_at': createdAt?.toIso8601String(),
        'is_deleted': isDeleted,
        'deleted_at': deletedAt?.toIso8601String(),
      };
}

class Charge {
  Charge({
    this.id,
    this.chargeType,
    this.charge,
    this.chargeDescription,
    this.bookedCarChargeId,
  });

  factory Charge.fromJson(Map<String, dynamic> json) => Charge(
        id: json['id'] as int?,
        chargeType: json['charge_type'] as String?,
        charge: json['charge'] as double?,
        chargeDescription: json['charge_description'] as String?,
        bookedCarChargeId: json['booked_car_charge_id'] as String?,
      );
  int? id;
  String? chargeType;
  double? charge;
  String? chargeDescription;
  String? bookedCarChargeId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'charge_type': chargeType,
        'charge': charge,
        'charge_description': chargeDescription,
        'booked_car_charge_id': bookedCarChargeId,
      };
}

class Booking {
  Booking({
    this.id,
    this.customer,
    this.bookingId,
  });

  factory Booking.fromJson(Map<String, dynamic> json) => Booking(
        id: json['id'] as int?,
        customer: json['customer'] == null
            ? null
            : Customer.fromJson(json['customer'] as Map<String, dynamic>),
        bookingId: json['booking_id'] as String?,
      );
  int? id;
  Customer? customer;
  String? bookingId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'customer': customer?.toJson(),
        'booking_id': bookingId,
      };
}

class Customer {
  Customer({
    this.firstName,
    this.lastName,
    this.email,
    this.user,
    this.bookingContactNumber,
    this.bookingContactNumberCountryCode,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        firstName: json['first_name'] as String?,
        lastName: json['last_name'] as String?,
        email: json['email'] as String?,
        user: json['user'] as int?,
        bookingContactNumber: json['booking_contact_number'] as String?,
        bookingContactNumberCountryCode:
            json['booking_contact_number_country_code'] as String?,
      );
  String? firstName;
  String? lastName;
  String? email;
  int? user;
  String? bookingContactNumber;
  String? bookingContactNumberCountryCode;

  Map<String, dynamic> toJson() => {
        'first_name': firstName,
        'last_name': lastName,
        'email': email,
        'user': user,
        'booking_contact_number': bookingContactNumber,
        'booking_contact_number_country_code': bookingContactNumberCountryCode,
      };
}

class SharedBooking {
  SharedBooking({
    this.id,
    this.stopLocationCarDropDate,
    this.stopLocationCarPickUpDate,
    this.intermediateStartStopLocation,
    this.intermediateEndStopLocation,
    this.totalTripDistance,
    this.sharedBookingId,
  });

  factory SharedBooking.fromJson(Map<String, dynamic> json) => SharedBooking(
        id: json['id'] as int?,
        stopLocationCarDropDate: json['stop_location_car_drop_date'] == null
            ? null
            : DateTime.parse(json['stop_location_car_drop_date'] as String),
        stopLocationCarPickUpDate: json['stop_location_car_pick_up_date'] ==
                null
            ? null
            : DateTime.parse(json['stop_location_car_pick_up_date'] as String),
        intermediateStartStopLocation:
            json['intermediate_start_stop_location'] == null
                ? null
                : IntermediatePickUpPoint.fromJson(
                    json['intermediate_start_stop_location']
                        as Map<String, dynamic>,
                  ),
        intermediateEndStopLocation: json['intermediate_end_stop_location'] ==
                null
            ? null
            : IntermediatePickUpPoint.fromJson(
                json['intermediate_end_stop_location'] as Map<String, dynamic>,
              ),
        totalTripDistance: json['total_trip_distance'] as num?,
        sharedBookingId: json['shared_booking_id'] as String?,
      );
  int? id;
  DateTime? stopLocationCarDropDate;
  DateTime? stopLocationCarPickUpDate;
  IntermediatePickUpPoint? intermediateStartStopLocation;
  IntermediatePickUpPoint? intermediateEndStopLocation;
  num? totalTripDistance;
  String? sharedBookingId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'stop_location_car_drop_date':
            stopLocationCarDropDate?.toIso8601String(),
        'stop_location_car_pick_up_date':
            stopLocationCarPickUpDate?.toIso8601String(),
        'intermediate_start_stop_location':
            intermediateStartStopLocation?.toJson(),
        'intermediate_end_stop_location': intermediateEndStopLocation?.toJson(),
        'total_trip_distance': totalTripDistance,
        'shared_booking_id': sharedBookingId,
      };
}

class ExclusiveModel {
  ExclusiveModel({
    this.id,
    this.exclusiveBookingId,
    this.isOutForDelivery = false,
    this.isOutForDeliveryAt,
  });

  factory ExclusiveModel.fromJson(Map<String, dynamic> json) => ExclusiveModel(
        id: json['id'] as int?,
        exclusiveBookingId: json['exclusive_booking_id'] as String?,
        isOutForDelivery: (json['is_out_for_delivery'] as bool?) ?? false,
        isOutForDeliveryAt: json['out_for_delivery_at'] == null
            ? null
            : DateTime.parse(json['out_for_delivery_at'] as String),
      );
  int? id;
  String? exclusiveBookingId;
  bool isOutForDelivery;
  DateTime? isOutForDeliveryAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'exclusive_booking_id': exclusiveBookingId,
        'is_out_for_delivery': isOutForDelivery,
        'out_for_delivery_at': isOutForDeliveryAt?.toIso8601String(),
      };
}

class NoteModel {
  NoteModel({
    this.id,
    this.description,
    this.bookingDetail,
    this.createdAt,
  });

  factory NoteModel.fromJson(Map<String, dynamic> json) => NoteModel(
        id: json['id'] as int?,
        description: json['description'] as String?,
        bookingDetail: json['booking_detail'] as int?,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
      );
  int? id;
  String? description;
  int? bookingDetail;
  DateTime? createdAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'description': description,
        'booking_detail': bookingDetail,
        'created_at': createdAt?.toIso8601String(),
      };
}

class CustomerChatRoom {
  CustomerChatRoom({
    this.id,
    this.isActive,
    this.unreadMessageCount,
  });

  factory CustomerChatRoom.fromJson(Map<String, dynamic> json) {
    return CustomerChatRoom(
      id: json['id'] as int?,
      isActive: json['is_active'] as bool?,
      unreadMessageCount: json['unread_message_count'] as int?,
    );
  }
  int? id;
  bool? isActive;
  int? unreadMessageCount;

  Map<String, dynamic> toJson() => {
        'id': id,
        'is_active': isActive,
        'unread_message_count': unreadMessageCount,
      };
}

class CarImageModel {
  const CarImageModel({required this.id, required this.imageUrl});

  factory CarImageModel.fromJson(Map<String, dynamic> json) => CarImageModel(
        id: json['id'] as int?,
        imageUrl: json['image_url'] as String?,
      );
  final int? id;
  final String? imageUrl;

  Map<String, dynamic> toJson() => {'id': id, 'image_url': imageUrl};
}
