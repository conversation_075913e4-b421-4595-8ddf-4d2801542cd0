import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/checklist_api_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/models/checklist_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

class ChecklistWidget extends StatelessWidget {
  const ChecklistWidget({super.key, this.checklistModel, required this.isAdd});
  final ChecklistApiModel? checklistModel;
  final bool isAdd;

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckListProvider>(
      builder: (context, controller, child) {
        final check = controller.checkDataList;
        return Column(
          children: check.map(
            (e) {
              final list = e['list'] as List<CheckListModel>;
              final mainIndex =
                  check.indexWhere((l) => l['title'] == e['title']);
              return _CheckListWidget(
                list,
                e['title'] as String,
                (index) {
                  (controller.checkDataList[mainIndex]['list'][index]
                          as CheckListModel)
                      .isCheck
                      .value = !list[index].isCheck.value;
                  controller.notify();
                },
                checklistModel: checklistModel,
                isEdit: isAdd,
              );
            },
          ).toList(),
        );
      },
    );
  }
}

/// Button Row
class ButtonRow extends StatelessWidget {
  /// Constructor
  const ButtonRow({
    required this.onPressed,
    required this.data,
    this.isEdit = false,
    this.value = false,
    super.key,
  });

  final VoidCallback onPressed;
  final CheckListModel data;
  final bool isEdit;
  final bool value;

  @override
  Widget build(BuildContext context) {
    return AppPadding(
      bottom: AppSize.h4,
      child: Row(
        children: [
          Flexible(
            fit: FlexFit.tight,
            child: Text(
              data.name,
              style: context.textTheme.titleSmall?.copyWith(
                fontSize: AppSize.sp16,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          if (data.value != null && !isEdit)
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: CircleAvatar(
                radius: 11.5,
                backgroundColor:
                    data.value! ? AppColors.ff17BD8D : AppColors.errorColor,
                child: Icon(
                  data.value! ? Icons.check_rounded : Icons.close_rounded,
                  color: AppColors.white,
                  size: 15,
                ),
              ),
            )
          else
            Switch.adaptive(
              value: value,
              onChanged: (_) => onPressed(),
              trackOutlineColor: WidgetStatePropertyAll(
                !value ? AppColors.primaryColor : AppColors.white,
              ),
              thumbColor: WidgetStatePropertyAll(
                !value ? AppColors.primaryColor : AppColors.white,
              ),
            ),
          // Checkbox(
          //   value: value,
          //   checkColor: Colors.white,
          //   onChanged: (_) => onPressed(),
          // )
        ],
      ),
    );
  }
}

class _CheckListWidget extends StatefulWidget {
  const _CheckListWidget(
    this.list,
    this.title,
    this.onPress, {
    this.checklistModel,
    this.isEdit = false,
  });
  final List<CheckListModel> list;
  final String title;
  final ChecklistApiModel? checklistModel;
  final bool isEdit;
  final Function(int index) onPress;

  @override
  State<_CheckListWidget> createState() => __CheckListWidgetState();
}

class __CheckListWidgetState extends State<_CheckListWidget> {
  int length = 6;

  @override
  void initState() {
    length = widget.list.length > 6 ? 6 : widget.list.length;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: AppSize.h10),
          child: Text(
            widget.title,
            style: context.textTheme.titleLarge?.copyWith(
              fontSize: AppSize.sp16,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
        ...List.generate(
          length,
          (index) => ValueListenableBuilder(
            valueListenable: widget.list[index].isCheck,
            builder: (context, value, child) {
              return Padding(
                padding: EdgeInsets.only(
                  bottom: widget.checklistModel != null ? AppSize.h10 : 0,
                ),
                child: ButtonRow(
                  data: widget.list[index],
                  isEdit: widget.isEdit,
                  value: value,
                  onPressed: () => widget.onPress(index),
                ),
              );
            },
          ),
        ),
        if (widget.list.length > 6)
          Align(
            child: TextButton(
              onPressed: () {
                setState(() {
                  if (length == widget.list.length) {
                    length = 6;
                  } else {
                    length = widget.list.length;
                  }
                });
              },
              child: Text(
                length == widget.list.length
                    ? context.l10n.viewLess
                    : context.l10n.viewMore,
                style: TextStyle(
                  fontSize: AppSize.sp16,
                  fontWeight: FontWeight.w700,
                  color: AppColors.primaryColor,
                ),
              ),
            ),
          ),
        Gap(AppSize.h10),
      ],
    );
  }
}
