import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/checklist_api_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/widgets/app_image.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:widget_zoom/widget_zoom.dart';

class ChecklistImageWidget extends StatelessWidget {
  const ChecklistImageWidget({
    super.key,
    this.checklistModel,
    required this.isAdd,
  });
  final ChecklistApiModel? checklistModel;
  final bool isAdd;

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckListProvider>(
      builder: (context, controller, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 15, bottom: 10),
              child: Text(
                context.l10n.uploadCarImages,
                style: context.textTheme.titleLarge?.copyWith(
                  fontSize: AppSize.sp16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            ...controller.angleList.map(
              (e) {
                final imageList = controller.imageList
                    .where(
                      (image) => image.angle?.toLowerCase() == e,
                    )
                    .toList();
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      switch (e) {
                        AppStrings.left => context.l10n.fromLeftSide,
                        AppStrings.right => context.l10n.fromRightSide,
                        AppStrings.front => context.l10n.fromFrontSide,
                        AppStrings.back => context.l10n.fromBackSide,
                        _ => context.l10n.fromOtherSide,
                      },
                      style: TextStyle(
                        fontSize: AppSize.sp14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (imageList.isEmpty && !isAdd) AppPadding(
                      bottom: AppSize.h6,
                      child: Text(
                              '- ${context.l10n.noImagesAdded}',
                              style: TextStyle(
                                fontSize: AppSize.sp12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                    ) else SizedBox(
                            height: AppSize.h92,
                            child: ListView.builder(
                              shrinkWrap: true,
                              padding: EdgeInsets.only(
                                top: AppSize.h5,
                                bottom: AppSize.h10,
                              ),
                              scrollDirection: Axis.horizontal,
                              itemCount: (imageList.length) + (isAdd ? 1 : 0),
                              itemBuilder: (context, index) {
                                return index == 0 && isAdd
                                    ? Padding(
                                        padding:
                                            EdgeInsets.only(right: AppSize.w10),
                                        child: GestureDetector(
                                          onTap: () async =>
                                              controller.addChecklistImage(
                                            e,
                                            imageList.length,
                                          ),
                                          child: DecoratedBox(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                AppSize.r8,
                                              ),
                                              color: AppColors.ffF2EEF8,
                                            ),
                                            child: SizedBox(
                                              width: AppSize.w80,
                                              height: AppSize.h92,
                                              child: const Icon(Icons.add),
                                            ),
                                          ),
                                        ),
                                      )
                                    : Builder(
                                        builder: (context) {
                                          final imageData = imageList[
                                              isAdd ? index - 1 : index];
                                          final img = imageData.imageUrl ?? '';
                                          return imageData.isRemove
                                              ? const SizedBox.shrink()
                                              : Padding(
                                                  padding: EdgeInsets.only(
                                                    right: AppSize.w10,
                                                  ),
                                                  child: Stack(
                                                    alignment:
                                                        AlignmentDirectional
                                                            .topEnd,
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                          AppSize.r8,
                                                        ),
                                                        child: ColoredBox(
                                                          color: AppColors
                                                              .ffF2EEF8,
                                                          child: WidgetZoom(
                                                            heroAnimationTag:
                                                                '$img$index',
                                                            zoomWidget: img
                                                                    .contains(
                                                              'https',
                                                            )
                                                                ? AppImage
                                                                    .network(
                                                                    img,
                                                                    width:
                                                                        AppSize
                                                                            .w80,
                                                                    height:
                                                                        AppSize
                                                                            .h92,
                                                                    fit: BoxFit
                                                                        .contain,
                                                                  )
                                                                : Image.file(
                                                                    File(img),
                                                                    width:
                                                                        AppSize
                                                                            .w80,
                                                                    height:
                                                                        AppSize
                                                                            .h92,
                                                                    fit: BoxFit
                                                                        .contain,
                                                                  ),
                                                          ),
                                                        ),
                                                      ),
                                                      if (isAdd)
                                                        GestureDetector(
                                                          onTap: () => controller
                                                              .removeChecklistImage(
                                                            index,
                                                            e,
                                                          ),
                                                          child: Container(
                                                            color: Colors
                                                                .transparent,
                                                            padding:
                                                                EdgeInsets.all(
                                                              AppSize.sp5,
                                                            ),
                                                            child: CircleAvatar(
                                                              radius:
                                                                  AppSize.r8,
                                                              backgroundColor:
                                                                  AppColors
                                                                      .errorColor,
                                                              child: Icon(
                                                                Icons.delete,
                                                                size: AppSize
                                                                    .sp12,
                                                                color: AppColors
                                                                    .white,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                );
                                        },
                                      );
                              },
                            ),
                          ),
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}
