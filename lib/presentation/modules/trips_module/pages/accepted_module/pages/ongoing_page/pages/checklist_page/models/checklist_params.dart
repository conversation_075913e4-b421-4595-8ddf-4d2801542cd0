// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';

/// Parameters for the ChecklistScreen
class ChecklistParams {
  /// Constructor
  ChecklistParams({
    required this.carInfo,
    required this.clientName,
    required this.booking,
    this.isExclusive = false,
    required this.tripDetailsModel,
  });

  /// The car info
  final BookedCar carInfo;

  /// The client name
  final String clientName;

  /// The booking model
  final BookingModel booking;

  /// The trip details model
  final TripDetailsModel tripDetailsModel;

  /// Whether this is an exclusive booking
  final bool isExclusive;
}
