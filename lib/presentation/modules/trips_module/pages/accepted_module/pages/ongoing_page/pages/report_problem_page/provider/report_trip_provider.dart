import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';

import 'package:transportmatch_provider/utils/logger.dart';

class ReportTripProvider extends ChangeNotifier {
  // Flag to track if the provider is disposed
  bool isClosed = false;

  // Form Key
  final formKey = GlobalKey<FormState>();

  // Controllers
  final explainProblem = TextEditingController();
  final affectedTime = TextEditingController();

  // State variables
  final isLoading = ValueNotifier<bool>(false);

  // API tokens
  CancelToken? cancelToken;

  /// Report a trip issue
  Future<void> reportTripAPICall(String tripId, BuildContext context) async {
    if (isClosed) return;

    try {
      cancelToken?.cancel();
      cancelToken = CancelToken();
      isLoading.value = true;

      final res = await Injector.instance<TripRepository>().reportTrip(
        ApiRequest(
          path: EndPoints.createReport,
          data: {
            'trip': tripId,
            'affected_time': '${affectedTime.text}:00:00',
            'description': explainProblem.text,
          },
          cancelToken: cancelToken,
        ),
      );

      res.when(
        success: (data) {
          if (isClosed || (cancelToken?.isCancelled ?? true)) return;

          isLoading.value = false;
          'Report created successfully.'.showSuccessAlert();
          AppNavigationService.pop(context);
          '==>> report trip success $data'.logI;
          explainProblem.clear();
          affectedTime.clear();
        },
        error: (exception) {
          if (isClosed || (cancelToken?.isCancelled ?? true)) return;

          isLoading.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (cancelToken?.isCancelled ?? true)) return;

      isLoading.value = false;
      '==>> report trip error $e'.logE;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    cancelToken?.cancel();
    explainProblem.dispose();
    affectedTime.dispose();
    isLoading.dispose();
    super.dispose();
  }
}
