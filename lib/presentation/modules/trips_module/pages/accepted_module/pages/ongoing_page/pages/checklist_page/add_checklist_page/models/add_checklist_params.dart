// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';

/// Parameters for the AddChecklistScreen
class AddChecklistParams {
  /// Constructor
  AddChecklistParams({
    required this.checkListProvider,
    this.carInfo,
    this.clientName = '-',
    this.isExclusive = false,
    this.isAdd = false,
    this.isFromNotification = false,
    this.checkListId,
    this.isVerify = false,
  });

  /// The car info
  final BookedCar? carInfo;

  /// Whether this is from a notification
  final bool isFromNotification;

  /// The checklist ID
  final int? checkListId;

  /// The client name
  final String clientName;

  /// The checklist provider
  final CheckListProvider checkListProvider;

  /// Whether this is an exclusive booking
  final bool isExclusive;

  /// Whether this is adding a new checklist
  final bool isAdd;

  /// Need to verify checklist
  bool isVerify = false;
}
