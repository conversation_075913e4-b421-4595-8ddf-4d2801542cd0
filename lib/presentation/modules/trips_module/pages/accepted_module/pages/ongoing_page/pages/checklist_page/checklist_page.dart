import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/checklist_api_model.dart'
    show ChecklistApiModel;
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/models/checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

///Check list Screen Ui
class CheckListPage extends StatelessWidget {
  /// Constructor
  const CheckListPage({super.key, required this.checklistParams});
  final ChecklistParams checklistParams;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return ChangeNotifierProvider(
      create: (context) => CheckListProvider(
        carId: checklistParams.carInfo.id != null
            ? checklistParams.carInfo.id.toString()
            : '',
        booking: checklistParams.booking,
        tripDetailsModel: checklistParams.tripDetailsModel,
      ),
      child: Selector<CheckListProvider, bool>(
        selector: (p0, p1) => p1.isShowLoader,
        builder: (context, isLoading, child) {
          final checkListProvider = context.read<CheckListProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              title: l10n.checklist,
              actions: [
                Selector<CheckListProvider, List<ChecklistApiModel>>(
                  selector: (p0, p1) => p1.checkList,
                  builder: (context, checkList, child) {
                    final isProvider =
                        Injector.instance<AppDB>().userModel?.user?.role ==
                            UserType.Provider.name;
                    final isAddCheckListButtonDisable =
                        checkListProvider.isAddCheckListButtonDisable(
                      carStatus: checklistParams.carInfo.status,
                      // isOutForDelivery: checklistParams
                      //         .booking
                      //         .exclusiveBookings
                      //         ?.firstOrNull
                      //         ?.isOutForDelivery ??
                      //     false,
                      isExclusive: checklistParams.isExclusive,
                    );
                    return (isAddCheckListButtonDisable || isLoading)
                        ? const SizedBox.shrink()
                        : isProvider
                            ? const SizedBox.shrink()
                            : IconButton(
                                highlightColor: Colors.transparent,
                                onPressed: () =>
                                    checkListProvider.addCheckListData(
                                  context,
                                  carInfo: checklistParams.carInfo,
                                  clientName: checklistParams.clientName,
                                  isExclusive: checklistParams.isExclusive,
                                  checkListProvider: checkListProvider,
                                  isAdd: true,
                                ),
                                icon: const Icon(
                                  Icons.add,
                                  color: AppColors.ff0087C7,
                                ),
                              );
                  },
                ),
              ],
            ),
            body: AppLoader(
              isShowLoader: isLoading,
              child: Selector<CheckListProvider,
                  (List<ChecklistApiModel>, String?)>(
                selector: (p0, p1) => (p1.checkList, p1.nxtUrl),
                builder: (context, value, child) {
                  return SmartRefresher(
                    controller: checkListProvider.refreshController,
                    enablePullUp: value.$2 != null,
                    onRefresh: () {
                      checkListProvider.getCheckList(
                        checklistParams.carInfo.id?.toString() ?? '',
                      );
                      checkListProvider.refreshController.refreshCompleted();
                    },
                    onLoading: () {
                      checkListProvider.getCheckList(
                        checklistParams.carInfo.id?.toString() ?? '',
                        isPagination: true,
                      );
                      checkListProvider.refreshController.loadComplete();
                    },
                    child: value.$1.isEmpty && !isLoading
                        ? Center(
                            child: Padding(
                              padding: EdgeInsets.only(
                                bottom: AppSize.h40,
                                right: AppSize.appPadding,
                                left: AppSize.appPadding,
                              ),
                              child: Text(
                                context.l10n.noChecklistPerformedYet,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          )
                        : ListView.builder(
                            itemCount: value.$1.length,
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSize.appPadding,
                              vertical: AppSize.h10,
                            ),
                            primary: false,
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              final checkList = value.$1[index];
                              return Padding(
                                padding: EdgeInsets.only(bottom: AppSize.h16),
                                child: GestureDetector(
                                  onTap: () =>
                                      checkListProvider.addCheckListData(
                                    context,
                                    clientName: checklistParams.clientName,
                                    isExclusive: checklistParams.isExclusive,
                                    checklistApiData: checkList,
                                    checkListProvider: checkListProvider,
                                    carInfo: checklistParams.carInfo,
                                  ),
                                  child: DecoratedBox(
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      borderRadius:
                                          BorderRadius.circular(AppSize.r10),
                                      border:
                                          Border.all(color: AppColors.ffADB5BD),
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.all(AppSize.sp16),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        spacing: 5,
                                        children: [
                                          richText(
                                            title: context.l10n.type,
                                            value:
                                                checkList.checklistType ?? '',
                                          ),
                                          richText(
                                            title: context.l10n.performedDuring,
                                            value: checkList.performedDuring
                                                    ?.upToLower ??
                                                '',
                                          ),
                                          Align(
                                            alignment: Alignment.centerRight,
                                            child: Text(
                                              checkList.createdAt?.mmmDdYyyy ??
                                                  '',
                                              style: TextStyle(
                                                fontSize: AppSize.sp11,
                                                fontWeight: FontWeight.w600,
                                                color: const Color.fromARGB(
                                                  255,
                                                  45,
                                                  47,
                                                  48,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget richText({
    required String title,
    required String value,
  }) {
    return RichText(
      text: TextSpan(
        text: '$title: ',
        style: TextStyle(
          fontSize: AppSize.sp14,
          color: AppColors.ffADB5BD,
          fontWeight: FontWeight.w500,
        ),
        children: [
          TextSpan(
            text: value,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }
}

/// Button Row
class ButtonRow extends StatelessWidget {
  /// Constructor
  const ButtonRow({
    required this.onPressed,
    required this.title,
    required this.value,
    super.key,
  });

  final VoidCallback onPressed;
  final String title;
  final bool value;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Flexible(
          fit: FlexFit.tight,
          child: Text(
            title,
            style: context.textTheme.titleMedium,
          ),
        ),
        Checkbox(
          value: value,
          checkColor: Colors.white,
          onChanged: (_) => onPressed(),
        ),
      ],
    );
  }
}

// class _CheckListWidget extends StatefulWidget {
//   const _CheckListWidget(this.list, this.title, this.onPress);
//   final List<CheckListModel> list;
//   final String title;
//   final Function(int index) onPress;

//   @override
//   State<_CheckListWidget> createState() => __CheckListWidgetState();
// }

// class __CheckListWidgetState extends State<_CheckListWidget> {
//   int length = 6;

//   @override
//   void initState() {
//     length = widget.list.length > 6 ? 6 : widget.list.length;
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: EdgeInsets.only(bottom: AppSize.h10),
//           child: Text(
//             widget.title,
//             style: context.textTheme.titleLarge,
//           ),
//         ),
//         ...List.generate(
//           length,
//           (index) => Selector<ChecklistController, bool>(
//             selector: (p0, p1) => switch (widget.title) {
//               'Exterior' => p1.exteriorCheckList[index].isCheck,
//               'Interior' => p1.interiorCheckList[index].isCheck,
//               'Accessories' => p1.accessoriesCheckList[index].isCheck,
//               _ => p1.extinguisherCheckList[index].isCheck,
//             },
//             builder: (context, value, child) {
//               return ButtonRow(
//                 title: widget.list[index].name,
//                 value: widget.list[index].isCheck,
//                 onPressed: () => widget.onPress(index),
//               );
//             },
//           ),
//         ),
//         if (widget.list.length > 6)
//           Align(
//             child: TextButton(
//               onPressed: () {
//                 setState(() {
//                   if (length == widget.list.length) {
//                     length = 6;
//                   } else {
//                     length = widget.list.length;
//                   }
//                 });
//               },
//               child: Text(
//                 length == widget.list.length ? 'View less' : 'View more',
//                 style: const TextStyle(fontSize: 15),
//               ),
//             ),
//           ),
//         const Gap(10),
//       ],
//     );
//   }
// }
