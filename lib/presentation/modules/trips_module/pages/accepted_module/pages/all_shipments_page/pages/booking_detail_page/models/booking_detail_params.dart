// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';

/// Parameters for the BookingDetail screen
class BookingDetailParams {
  /// Constructor
  BookingDetailParams({
    required this.booking,
    required this.tripDetailsModel,
  });
  
  /// The booking model
  final BookingModel booking;
  
  /// The trip details model
  final TripDetailsModel tripDetailsModel;
}
