import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/provider/accepted_details_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/location_widget.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

/// All ShipmentsCard card
class AllShipmentsCard extends StatelessWidget {
  /// Constructor
  const AllShipmentsCard({
    super.key,
    // required this.tripDetailsProvider.tripDetailsModel!,
    required this.tripDetailsProvider,
  });
  // final tripDetailsProvider.tripDetailsModel! tripDetailsProvider.tripDetailsModel!;
  final TripDetailsProvider tripDetailsProvider;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h16),
      padding: EdgeInsets.all(AppSize.sp16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSize.h16,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TitleInfoWidget(
                title: context.l10n.no_of_vehicles,
                subTitle: tripDetailsProvider.tripDetailsModel?.totalBookedCars
                        ?.toString() ??
                    '-',
                titleColor: AppColors.ffADB5BD,
                subTitleFontSize: AppSize.sp18,
              ),
              Text.rich(
                TextSpan(
                  text:
                      '\$${((tripDetailsProvider.tripDetailsModel!.totalTripDistance ?? 0) * (tripDetailsProvider.tripDetailsModel!.costPerKilometer ?? 0)).round()}'
                          .smartFormat(),
                  style: context.textTheme.titleLarge?.copyWith(
                    color: AppColors.ff67509C,
                    fontSize: AppSize.sp16,
                    fontWeight: FontWeight.w600,
                  ),
                  children: [
                    TextSpan(
                      text: ' /${context.l10n.slot}',
                      style: context.textTheme.titleLarge?.copyWith(
                        color: AppColors.ffADB5BD,
                        fontSize: AppSize.sp16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TitleInfoWidget(
                title: context.l10n.transporter,
                subTitle:
                    tripDetailsProvider.tripDetailsModel!.driver?.firstName ??
                        '',
                subTitleFontSize: AppSize.sp18,
              ),
              TitleInfoWidget(
                title: context.l10n.totalTripCost,
                subTitle:
                    '\$${((tripDetailsProvider.tripDetailsModel!.totalTripDistance ?? 0) * (tripDetailsProvider.tripDetailsModel!.costPerKilometer ?? 0) * (tripDetailsProvider.tripDetailsModel!.spotAvailableForReservation ?? 0)).round()}'
                        .smartFormat(),
                subTitleColor: AppColors.ff67509C,
                subTitleFontSize: AppSize.sp16,
                subTitleFontWeight: FontWeight.w600,
              ),
            ],
          ),
          Column(
            children: [
              // Locations row
              LocationWidget(
                title1: tripDetailsProvider.tripDetailsModel!.exclusiveTripData
                        ?.userStartLocation?.street ??
                    tripDetailsProvider
                        .tripDetailsModel!.startStopLocation?.name ??
                    '',
                date1: tripDetailsProvider.tripDetailsModel!.tripStartDate
                        ?.formatDateTimeToLocalDate()
                        ?.monthDate ??
                    '',
                title2: tripDetailsProvider.tripDetailsModel!.exclusiveTripData
                        ?.userEndLocation?.street ??
                    tripDetailsProvider
                        .tripDetailsModel!.endStopLocation?.name ??
                    '',
                date2: tripDetailsProvider.tripDetailsModel!.tripEndDate
                        ?.formatDateTimeToLocalDate()
                        ?.monthDate ??
                    // tripDetailsProvider.tripDetailsModel!.endStopLocation?.date?.monthDate ??
                    '',
                startLatitude: tripDetailsProvider.tripDetailsModel
                        ?.exclusiveTripData?.userStartLocation?.latitude ??
                    tripDetailsProvider.tripDetailsModel?.startStopLocation
                        ?.address?.latitude ??
                    '0',
                startLongitude: tripDetailsProvider.tripDetailsModel
                        ?.exclusiveTripData?.userStartLocation?.longitude ??
                    tripDetailsProvider.tripDetailsModel?.startStopLocation
                        ?.address?.longitude ??
                    '0',
                endLatitude: tripDetailsProvider.tripDetailsModel
                        ?.exclusiveTripData?.userEndLocation?.latitude ??
                    tripDetailsProvider
                        .tripDetailsModel?.endStopLocation?.address?.latitude ??
                    '0',
                endLongitude: tripDetailsProvider.tripDetailsModel
                        ?.exclusiveTripData?.userEndLocation?.longitude ??
                    tripDetailsProvider.tripDetailsModel?.endStopLocation
                        ?.address?.longitude ??
                    '0',
                isStackIcon: true,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
