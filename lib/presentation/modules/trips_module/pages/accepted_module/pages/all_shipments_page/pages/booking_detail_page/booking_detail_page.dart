import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/booking_detail_page/models/booking_detail_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/widgets/location_row.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/widgets/vehicles_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class BookingDetailPage extends StatelessWidget {
  const BookingDetailPage({
    super.key,
    required this.bookingDetailParams,
  });
  final BookingDetailParams bookingDetailParams;

  @override
  Widget build(BuildContext context) {
    final stopLocation = bookingDetailParams
        .booking.sharedBookings?.firstOrNull?.intermediateStartStopLocation;
    final endLocation = bookingDetailParams
        .booking.sharedBookings?.firstOrNull?.intermediateEndStopLocation;
    return Scaffold(
      backgroundColor: AppColors.ffF8F9FA,
      appBar: CustomAppBar(
        title: context.l10n.bookingDetail,
      ),
      body: ListView(
        primary: false,
        padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
        children: [
          VehiclesWidget(
            carList: bookingDetailParams.booking.bookedCars ?? [],
            isExclusive:
                !(bookingDetailParams.booking.sharedBookings?.isNotEmpty ??
                    false),
            clientName:
                "${bookingDetailParams.booking.booking?.customer?.firstName ?? ''}"
                " ${bookingDetailParams.booking.booking?.customer?.lastName ?? ''}",
            booking: bookingDetailParams.booking,
            tripDetailsModel: bookingDetailParams.tripDetailsModel,
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: AppSize.h16),
            child: LocationRow(
              icon: AppAssets.iconsLocationOrigin.image(
                width: AppSize.w20,
                height: AppSize.h20,
                fit: BoxFit.cover,
              ),
              title:
                  '${context.l10n.pickFrom} ${bookingDetailParams.tripDetailsModel.startStopLocation?.address?.fullAddress ?? bookingDetailParams.tripDetailsModel.exclusiveTripData?.userStartLocation?.state}',
              date: (stopLocation?.estimatedArrivalDate ??
                          bookingDetailParams.tripDetailsModel.tripStartDate
                              ?.formatDateTimeToLocalDate())
                      ?.monthDate ??
                  '',
            ),
          ),
          LocationRow(
            icon: AppAssets.iconsLocation.image(
              width: AppSize.w20,
              height: AppSize.h20,
              fit: BoxFit.cover,
            ),
            title:
                '${context.l10n.dropAt} ${bookingDetailParams.tripDetailsModel.endStopLocation?.address?.fullAddress ?? bookingDetailParams.tripDetailsModel.exclusiveTripData?.userEndLocation?.state ?? ''}',
            date: (endLocation?.estimatedArrivalDate ??
                        bookingDetailParams.tripDetailsModel.tripEndDate
                            ?.formatDateTimeToLocalDate())
                    ?.monthDate ??
                '',
          ),
          if (bookingDetailParams.booking.notes?.isNotEmpty ?? false)
            Gap(AppSize.h10),
          if (bookingDetailParams.booking.notes?.isNotEmpty ?? false)
            Card(
              color: AppColors.white,
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSize.w10,
                  vertical: AppSize.h10,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 5,
                  children: [
                    Text(
                      context.l10n.notes,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      itemCount: bookingDetailParams.booking.notes?.length ?? 0,
                      itemBuilder: (context, index) {
                        final note = bookingDetailParams.booking.notes?[index];
                        return Padding(
                          padding: EdgeInsets.only(left: AppSize.w5),
                          child: Text(
                            "${AppStrings.arrowWithoutSpace}${note?.description ?? ''}",
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
