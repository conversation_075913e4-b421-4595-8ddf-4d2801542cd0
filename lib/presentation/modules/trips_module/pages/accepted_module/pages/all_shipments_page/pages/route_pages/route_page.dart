import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/route_pages/model/router_request_param.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/route_pages/provider/route_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/logger.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:url_launcher/url_launcher_string.dart';

class RoutePage extends StatelessWidget {
  const RoutePage({super.key, required this.routerRequestParam});
  final RouterRequestParam routerRequestParam;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: context.l10n.yourEquipmentRoute),
      body: ChangeNotifierProvider(
        create: (context) =>
            RouteProvider(routerRequestParam, context: context),
        builder: (context, child) {
          return ValueListenableBuilder(
            valueListenable: context.read<RouteProvider>().isShowLoader,
            builder: (context, isShowLoader, child) => AppLoader(
              isShowLoader: isShowLoader,
              child: child!,
            ),
            child: Consumer<RouteProvider>(
              builder: (context, routeProvider, child) {
                return Stack(
                  children: [
                    GoogleMap(
                      initialCameraPosition:
                          routeProvider.initialCameraPosition,
                      polylines: routeProvider.polyline.toSet(),
                      markers: routeProvider.markers,
                      onMapCreated: routeProvider.onMapCreated,
                      // zoomControlsEnabled: false,
                      onCameraMove: routeProvider.onCameraMove,
                      onTap: (argument) {
                        routeProvider
                          ..customInfoWindowController.hideInfoWindow!()
                          ..notify();
                      },
                    ),
                    CustomInfoWindow(
                      controller: routeProvider.customInfoWindowController,
                      height: AppSize.h50,
                      width: AppSize.w100,
                    ),
                    Positioned(
                      bottom: 25,
                      right: 25,
                      child: ValueListenableBuilder(
                        valueListenable: routeProvider.redirectingUrl,
                        builder: (context, redirectingUrl, child) {
                          return redirectingUrl.isNotEmpty
                              ? GestureDetector(
                                  onTap: () async {
                                    if (await canLaunchUrlString(
                                      redirectingUrl,
                                    )) {
                                      '==>>>> redirectingUrl $redirectingUrl'
                                          .logE;
                                      await launchUrlString(redirectingUrl);
                                    }
                                  },
                                  child: CircleAvatar(
                                    radius: AppSize.sp25,
                                    backgroundColor: AppColors.ff0087C7,
                                    child: const Icon(
                                      Icons.directions,
                                      color: AppColors.white,
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink();
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          );
        },
      ),
    );
  }
}
