class ChatMessagesModel {
  ChatMessagesModel({
    required this.count,
    required this.next,
    required this.previous,
    required this.results,
  });

  factory ChatMessagesModel.fromJson(Map<String, dynamic> json) {
    return ChatMessagesModel(
      count: json['count'] as int,
      next: json['next'] as String?,
      previous: json['previous'] as String?,
      results: List<Message>.from(
        (json['results'] as List)
            .map((x) => Message.fromJson(x as Map<String, dynamic>)),
      ),
    );
  }
  int count;
  String? next;
  String? previous;
  List<Message> results;

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      'results': results.map((x) => x.toJson()).toList(),
    };
  }
}

class Sender {
  Sender({
    this.id,
    this.firstName,
  });

  factory Sender.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return Sender();
    }
    return Sender(
      id: json['id'] as int?,
      firstName: json['first_name'] as String?,
    );
  }
  final int? id;
  final String? firstName;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
    };
  }
}

class Message {
  Message({
    this.id,
    this.chatRoom,
    this.isRoomActive,
    this.sender,
    this.message,
    this.messageType,
    this.fileUrl,
    this.createdAt,
  });

  factory Message.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return Message();
    }
    return Message(
      id: json['id'] as int?,
      chatRoom: json['chat_room'] as int?,
      isRoomActive: json['is_room_active'] as bool?,
      sender: Sender.fromJson(json['sender'] as Map<String, dynamic>?),
      message: json['message'] as String?,
      messageType: json['message_type'] as String?,
      fileUrl: json['file_url'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
    );
  }
  final int? id;
  final int? chatRoom;
  final bool? isRoomActive;
  final Sender? sender;
  final String? message;
  final String? messageType;
  final String? fileUrl;
  final DateTime? createdAt;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chat_room': chatRoom,
      'is_room_active': isRoomActive,
      'sender': sender?.toJson(),
      'message': message,
      'message_type': messageType,
      'file_url': fileUrl,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}
