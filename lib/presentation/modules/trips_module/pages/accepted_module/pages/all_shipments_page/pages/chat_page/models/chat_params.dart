// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/enum/chat_type.dart';

/// Parameters for the ChatScreen
class ChatParams {
  /// Constructor
  ChatParams({
    required this.receiverId,
    required this.bookingDetailId,
    required this.chatType,
    this.customerChatRoomParameter,
    this.updateChatModel,
    required this.title,
  });
  
  /// The receiver ID
  final int receiverId;
  
  /// The booking detail ID
  final int bookingDetailId;
  
  /// The chat type
  final ChatType chatType;
  
  /// The customer chat room parameter
  final CustomerChatRoom? customerChatRoomParameter;
  
  /// Function to update the chat model
  final void Function({int? chatRoomId, bool? isActive})? updateChatModel;
  
  /// The chat title
  final String title;
}
