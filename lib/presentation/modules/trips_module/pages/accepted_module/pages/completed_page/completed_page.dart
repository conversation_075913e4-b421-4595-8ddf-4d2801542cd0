import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/completed_page/widgets/completed_transporter_card.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/provider/accepted_trips_provider.dart'
    show AcceptedTripsProvider;
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/provider/accepted_trips_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';

// /// Completed Screen
// class CompletedPage extends StatefulWidget {
//   /// Constructor
//   const CompletedPage({super.key, required this.tripsProvider});
//   final TripsProvider tripsProvider;

//   @override
//   State<CompletedPage> createState() => _CompletedPageState();
// }

// class _CompletedPageState extends State<CompletedPage> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.ffF8F9FA,
//       body: ListView.builder(
//         itemCount: 2,
//         itemBuilder: (context, index) {
//           return Padding(
//             padding: EdgeInsets.only(bottom: AppSize.h5),
//             child: const CompletedTransporterCard(
//               vehiclesCount: 10,
//               deliveredLocation: 'California',
//               deliveredDate: 'Nov 28',
//             ),
//           );
//         },
//       ),
//     );
//   }
// }

/// Completed Screen
class CompletedScreen extends StatelessWidget {
  /// Constructor
  const CompletedScreen({super.key, required this.acceptedTripsProvider});
  final AcceptedTripsProvider acceptedTripsProvider;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: acceptedTripsProvider
        ..getAcceptedTrips(
          status: AcceptedTripType.COMPLETED.name,
          isWantShowLoader: true,
        ),
      child: ValueListenableBuilder(
        valueListenable: acceptedTripsProvider.isShowLoaderForCompletedTrips,
        builder: (context, isShowLoader, child) {
          return AppLoader(
            isShowLoader: isShowLoader,
            child: Selector<AcceptedTripsProvider,
                (List<AcceptedTripModelData>, String?)>(
              selector: (context, provider) =>
                  (provider.completedTripsList, provider.completedTripsNextUrl),
              builder: (context, value, child) {
                return SmartRefresher(
                  controller:
                      acceptedTripsProvider.completedPageRefreshController,
                  enablePullUp: value.$2.isNotEmptyAndNotNull,
                  onRefresh: () {
                    acceptedTripsProvider
                        .getAcceptedTrips(
                          status: AcceptedTripType.COMPLETED.name,
                        )
                        .whenComplete(
                          acceptedTripsProvider
                              .completedPageRefreshController.refreshCompleted,
                        );
                  },
                  onLoading: () {
                    acceptedTripsProvider
                        .getAcceptedTrips(
                          isPagination: true,
                          status: AcceptedTripType.COMPLETED.name,
                        )
                        .whenComplete(
                          acceptedTripsProvider
                              .completedPageRefreshController.loadComplete,
                        );
                  },
                  child: value.$1.isEmpty && !isShowLoader
                      ? Center(
                          child: Text(context.l10n.noTripCompletedYet),
                        )
                      : ListView.builder(
                          itemCount: value.$1.length,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding:
                                  EdgeInsets.symmetric(vertical: AppSize.h5),
                              child: GestureDetector(
                                onTap: () => AppNavigationService.pushNamed(
                                  context,
                                  AppRoutes.tripsAllShipmentsScreen,
                                  extra: AllShipmentsParams(
                                    tripId: value.$1[index].id,
                                  ),
                                ),
                                child: CompletedTransporterCard(
                                  data: value.$1[index],
                                ),
                              ),
                            );
                          },
                        ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
