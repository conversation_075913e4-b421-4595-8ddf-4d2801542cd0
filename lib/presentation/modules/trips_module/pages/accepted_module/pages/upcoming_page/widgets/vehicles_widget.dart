import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/models/checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/widgets/vehicle_info_widgets.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/marqee_widget.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

/// VehiclesInfoWidget
class VehiclesWidget extends StatefulWidget {
  /// Constructor
  const VehiclesWidget({
    super.key,
    required this.carList,
    required this.clientName,
    this.isExclusive = false,
    // required this.tripModel,
    required this.tripDetailsModel,
    required this.booking,
  });

  final List<BookedCar> carList;
  final String clientName;
  final bool isExclusive;
  // final AcceptedTripModelData tripModel;
  final TripDetailsModel tripDetailsModel;
  final BookingModel booking;

  @override
  State<VehiclesWidget> createState() => _VehiclesWidgetState();
}

class _VehiclesWidgetState extends State<VehiclesWidget> {
  late PageController _pageController;

  @override
  void initState() {
    _pageController = PageController();
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: AppSize.h12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: context.width,
            padding: EdgeInsets.all(AppSize.h12),
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(AppSize.r5),
              border: Border.all(color: AppColors.ffADB5BD),
            ),
            child: TitleInfoWidget(
              title: context.l10n.clientName,
              subTitle: '${widget.booking.booking?.customer?.firstName ?? ""}'
                  ' ${widget.booking.booking?.customer?.lastName ?? ""}',
            ),
          ),
          Gap(AppSize.h16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.l10n.vehiclesInfo,
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: AppSize.sp16,
                ),
              ),
              AnimatedBuilder(
                animation: _pageController,
                builder: (context, child) {
                  return Text(
                    '${(_pageController.positions.isEmpty ? 0 : _pageController.page?.round() ?? 0) + 1}/${widget.carList.length}',
                    style: context.textTheme.bodyLarge
                        ?.copyWith(fontSize: AppSize.sp16),
                  );
                },
              ),
            ],
          ),
          Gap(AppSize.h4),
          SizedBox(
            height: AppSize.h172,
            child: PageView.builder(
              controller: _pageController,
              itemCount: widget.carList.length,
              itemBuilder: (context, index) {
                final vehicle = widget.carList[index];
                return Container(
                  width: context.width - AppSize.w60,
                  padding: EdgeInsets.all(AppSize.h12),
                  margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.ffADB5BD),
                    borderRadius: BorderRadius.circular(AppSize.r5),
                    color: AppColors.white,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: AppSize.h8,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (vehicle.car?.brand != null)
                            Flexible(
                              child: VehiclesInfoField(
                                title: context.l10n.carBrand,
                                value: vehicle.car!.brand!,
                              ),
                            ),
                          Gap(AppSize.w10),
                          Flexible(
                            child: Row(
                              children: [
                                Flexible(
                                  child: GestureDetector(
                                    onTap: () => AppNavigationService.pushNamed(
                                      context,
                                      AppRoutes.tripsCarInfoPage,
                                      extra: CarInfoParams(
                                        carDetail: CarDetail(
                                          car: vehicle.car,
                                          brand: vehicle.car?.brand,
                                          year: vehicle.car?.year,
                                          model: vehicle.car?.model,
                                          serialNumber: vehicle.serialNumber,
                                          carDescription:
                                              vehicle.carDescription,
                                          images: vehicle.images,
                                        ),
                                      ),
                                    ),
                                    behavior: HitTestBehavior.opaque,
                                    child: Text(
                                      context.l10n.details,
                                      style: context.textTheme.titleLarge
                                          ?.copyWith(
                                        color: AppColors.primaryColor,
                                        overflow: TextOverflow.ellipsis,
                                        fontSize: AppSize.sp16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                                Gap(AppSize.w10),
                                // if (((vehicle.status ==
                                //             VehicleStatus
                                //                 .RECEIVED_BY_ORIGIN_STOP_ADMIN
                                //                 .name) ||
                                //         (vehicle.status ==
                                //             VehicleStatus
                                //                 .HANDED_OVER_TO_DRIVER.name) ||
                                //         widget.isExclusive) &&
                                //     widget.tripDetailsModel.status ==
                                //         AcceptedTripType.ONGOING.name)
                                Flexible(
                                  child: GestureDetector(
                                    onTap: () => AppNavigationService.pushNamed(
                                      context,
                                      AppRoutes.tripsChecklistScreen,
                                      extra: ChecklistParams(
                                        carInfo: vehicle,
                                        clientName: widget.clientName,
                                        isExclusive: widget.isExclusive,
                                        booking: widget.booking,
                                        tripDetailsModel:
                                            widget.tripDetailsModel,
                                      ),
                                      // ChecklistScreen(
                                      //   carInfo: vehicle,
                                      //   clientName: widget.clientName,
                                      //   isExclusive: widget.isExclusive,
                                      //   booking: widget.booking,
                                      //   tripDetailsModel:
                                      //       widget.tripDetailsModel,
                                      // ),
                                    ),
                                    behavior: HitTestBehavior.opaque,
                                    child: Text(
                                      context.l10n.checklist,
                                      style: context.textTheme.titleLarge
                                          ?.copyWith(
                                        color: AppColors.red,
                                        fontSize: AppSize.sp16,
                                        overflow: TextOverflow.ellipsis,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (vehicle.car?.year != null &&
                          vehicle.car?.model != null) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if (vehicle.car?.model != null)
                              Flexible(
                                child: VehiclesInfoField(
                                  title: context.l10n.carModel,
                                  value: vehicle.car!.model!,
                                ),
                              ),
                            Gap(AppSize.w10),
                            if (vehicle.car?.year != null)
                              Flexible(
                                child: VehiclesInfoField(
                                  title: context.l10n.carYear,
                                  value: vehicle.car!.year!,
                                ),
                              ),
                          ],
                        ),
                      ],
                      if (vehicle.serialNumber != null)
                        Align(
                          alignment: Alignment.centerLeft,
                          child: VehiclesInfoField(
                            title: context.l10n.carSerial,
                            value: vehicle.serialNumber!,
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
          SizedBox(height: AppSize.h8),
          AnimatedBuilder(
            animation: _pageController,
            builder: (context, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (var i = 0; i < widget.carList.length; i++)
                    Container(
                      margin: EdgeInsets.symmetric(
                        horizontal: AppSize.w4,
                      ),
                      width: AppSize.sp8,
                      height: AppSize.sp8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: i == (_pageController.page?.round() ?? 0)
                            ? Colors.blue
                            : Colors.grey[400],
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
