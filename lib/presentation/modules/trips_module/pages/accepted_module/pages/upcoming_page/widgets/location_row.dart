import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// Card Row widget
class LocationRow extends StatelessWidget {
  /// Constructor
  const LocationRow({
    required this.title,
    required this.date,
    this.icon,
    super.key,
    this.titleColor,
    this.titleSize,
    this.dateColor,
    this.dateSize,
    this.fontWeight,
  });

  /// Icon
  final Widget? icon;

  /// Title
  final String title;

  /// TitleColor
  final Color? titleColor;

  /// TitleSize
  final double? titleSize;

  /// TitleSize
  final FontWeight? fontWeight;

  /// Date
  final String date;

  /// DateColor
  final Color? dateColor;

  /// DateSize
  final double? dateSize;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSize.r8),
          color: AppColors.white,
        ),
        /* shape: Border.,
        Radius: BorderRadius.circular(AppSize.r12),*/

        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w16,
          vertical: AppSize.h20,
        ),
        /* shape: Border.,
          Radius: BorderRadius.circular(AppSize.r12),*/
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          spacing: AppSize.w10,
          children: [
            Flexible(
              fit: FlexFit.tight,
              child: Row(
                spacing: AppSize.w8,
                children: [
                  icon ?? const SizedBox.shrink(),
                  Flexible(
                    child: Text(
                      title,
                      style: context.textTheme.bodyLarge?.copyWith(
                        color: titleColor ?? AppColors.ff495057,
                        fontSize: titleSize ?? AppSize.sp16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Text(
              date,
              style: context.textTheme.bodyLarge?.copyWith(
                fontSize: dateSize ?? AppSize.sp16,
                fontWeight: fontWeight ?? FontWeight.w700,
                color: dateColor ?? AppColors.ff67509C,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
