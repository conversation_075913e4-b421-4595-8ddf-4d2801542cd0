import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';

extension Add on Address {
  String? get fullAddress {
    final addressVal = AppCommonFunctions.cleanUpAddress(
      '$street${AppCommonFunctions.addComma(neighborhood)}'
      '${AppCommonFunctions.addComma(city)}'
      '${AppCommonFunctions.addComma(state)}'
      '${AppCommonFunctions.addComma(country)}'
      '${AppCommonFunctions.addComma(postalCode)}',
    );
    return addressVal;
  }
}

class StopLocation {
  StopLocation({
    this.name,
    this.address,
    this.id,
    this.stopAdminUserId,
  });

  factory StopLocation.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return StopLocation();
    }
    return StopLocation(
      name: json['name'] as String?,
      address: Address.fromJson(json['address'] as Map<String, dynamic>),
      id: json['id'] as int?,
      stopAdminUserId: json['stop_admin_user_id'] as int?,
    );
  }
  final String? name;
  final Address? address;
  final int? id;
  final int? stopAdminUserId;

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address?.toJson(),
      'id': id,
      'stop_admin_user_id': stopAdminUserId,
    };
  }
}

class IntermediatePickUpPoint {
  IntermediatePickUpPoint({
    this.stopLocation,
    this.estimatedArrivalDate,
    this.stopLocationIndex,
    this.distance,
    this.id,
    this.isArrived,
    this.arrivedAt,
    this.isMovedToNextStop,
    this.movedAt,
  });

  factory IntermediatePickUpPoint.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return IntermediatePickUpPoint();
    }
    return IntermediatePickUpPoint(
      stopLocation: json['stop_location'] == null
          ? null
          : StopLocation.fromJson(
              json['stop_location'] as Map<String, dynamic>?,
            ),
      estimatedArrivalDate: json['estimated_arrival_date'] as String?,
      stopLocationIndex: json['stop_location_index'] as int?,
      distance: (json['distance'] as num?)?.toDouble(),
      id: json['id'] as int?,
      isArrived: json['is_arrived'] as bool?,
      arrivedAt: json['arrived_at'] as String?,
      isMovedToNextStop: json['is_moved_to_next_stop'] as bool?,
      movedAt: json['moved_at'] as String?,
    );
  }
  final StopLocation? stopLocation;
  final String? estimatedArrivalDate;
  final int? stopLocationIndex;
  final double? distance;
  final int? id;
  final bool? isArrived;
  final String? arrivedAt;
  final bool? isMovedToNextStop;
  final String? movedAt;

  Map<String, dynamic> toJson() {
    return {
      'stop_location': stopLocation?.toJson(),
      'estimated_arrival_date': estimatedArrivalDate,
      'stop_location_index': stopLocationIndex,
      'distance': distance,
      'id': id,
      'is_arrived': isArrived,
      'arrived_at': arrivedAt,
      'is_moved_to_next_stop': isMovedToNextStop,
      'moved_at': movedAt,
    };
  }
}

class SharedTripData {
  SharedTripData({
    this.sharedTripId,
    this.id,
    this.isSaved,
  });

  factory SharedTripData.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return SharedTripData();
    }
    return SharedTripData(
      sharedTripId: json['shared_trip_id'] as String?,
      id: json['id'] as int?,
      isSaved: json['is_saved'] as bool?,
    );
  }
  final String? sharedTripId;
  final int? id;
  final bool? isSaved;

  Map<String, dynamic> toJson() {
    return {
      'shared_trip_id': sharedTripId,
      'id': id,
      'is_saved': isSaved,
    };
  }
}

class ExclusiveTripData {
  ExclusiveTripData({
    this.booking,
    this.exclusiveTripId,
    this.id,
    this.userStartLocation,
    this.userEndLocation,
    this.offerStatus,
  });

  factory ExclusiveTripData.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return ExclusiveTripData();
    }
    return ExclusiveTripData(
      booking: json['booking'] as int?,
      exclusiveTripId: json['exclusive_trip_id'] as String?,
      id: json['id'] as int?,
      userStartLocation: json['user_start_location'] == null
          ? null
          : Address.fromJson(
              json['user_start_location'] as Map<String, dynamic>,
            ),
      userEndLocation: json['user_end_location'] == null
          ? null
          : Address.fromJson(json['user_end_location'] as Map<String, dynamic>),
      offerStatus: json['offer_status'] as String?,
    );
  }
  final int? booking;
  final String? exclusiveTripId;
  final int? id;
  final Address? userStartLocation;
  final Address? userEndLocation;
  final String? offerStatus;

  Map<String, dynamic> toJson() {
    return {
      'booking': booking,
      'exclusive_trip_id': exclusiveTripId,
      'id': id,
      'user_start_location': userStartLocation?.toJson(),
      'user_end_location': userEndLocation?.toJson(),
      'offer_status': offerStatus,
    };
  }
}

class WaitingListTripData {
  WaitingListTripData({
    this.tripType,
    this.waitingListTripId,
    this.id,
  });

  factory WaitingListTripData.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return WaitingListTripData();
    }
    return WaitingListTripData(
      tripType: json['trip_type'] as String?,
      waitingListTripId: json['waiting_list_trip_id'] as String?,
      id: json['id'] as int?,
    );
  }
  final String? tripType;
  final String? waitingListTripId;
  final int? id;

  Map<String, dynamic> toJson() {
    return {
      'trip_type': tripType,
      'waiting_list_trip_id': waitingListTripId,
      'id': id,
    };
  }
}

class Report {
  Report({
    this.trip,
    this.description,
    this.affectedTime,
    this.id,
  });

  factory Report.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return Report();
    }
    return Report(
      trip: json['trip'] as int?,
      description: json['description'] as String?,
      affectedTime: json['affected_time'] as String?,
      id: json['id'] as int?,
    );
  }
  final int? trip;
  final String? description;
  final String? affectedTime;
  final int? id;

  Map<String, dynamic> toJson() {
    return {
      'trip': trip,
      'description': description,
      'affected_time': affectedTime,
      'id': id,
    };
  }
}

class Equipment {
  Equipment({
    this.name,
    this.provider,
    this.plateNumber,
    this.slot,
    this.economicNumber,
    this.insurancePolicyNumber,
    this.id,
    this.winch,
  });

  factory Equipment.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return Equipment();
    }
    return Equipment(
      name: json['name'] as String?,
      provider: json['provider'] as int?,
      plateNumber: json['plate_number'] as String?,
      slot: json['slot'] as int?,
      economicNumber: json['economic_number'] as String?,
      insurancePolicyNumber: json['insurance_policy_number'] as String?,
      id: json['id'] as int?,
      winch: json['winch'] as bool?,
    );
  }
  final String? name;
  final int? provider;
  final String? plateNumber;
  final int? slot;
  final String? economicNumber;
  final String? insurancePolicyNumber;
  final int? id;
  final bool? winch;

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'provider': provider,
      'plate_number': plateNumber,
      'slot': slot,
      'economic_number': economicNumber,
      'insurance_policy_number': insurancePolicyNumber,
      'id': id,
      'winch': winch,
    };
  }
}

class Driver {
  Driver({
    this.firstName,
    this.lastName,
    this.id,
  });

  factory Driver.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return Driver();
    }
    return Driver(
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      id: json['id'] as int?,
    );
  }
  final String? firstName;
  final String? lastName;
  final int? id;

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'id': id,
    };
  }
}

class TripDetailsModel {
  TripDetailsModel({
    this.startStopLocation,
    this.endStopLocation,
    this.spotAvailableForReservation,
    this.tripStartDate,
    this.tripEndDate,
    this.costPerKilometer,
    this.deadlineDate,
    this.totalTripDistance,
    this.totalBookedCars,
    this.intermediatePickUpPoint,
    this.tripType,
    this.tripId,
    this.sharedTripData,
    this.exclusiveTripData,
    this.waitingListTripData,
    this.reports,
    this.coveredDistance,
    this.isTripCancellable,
    this.id,
    this.equipment,
    this.driver,
    this.allowIntermediatePickup,
    this.status,
    this.completedAt,
    this.customPoints,
    this.totalTripCost,
  });

  factory TripDetailsModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return TripDetailsModel();
    }
    return TripDetailsModel(
      startStopLocation: StopLocation.fromJson(
        json['start_stop_location'] as Map<String, dynamic>?,
      ),
      endStopLocation: StopLocation.fromJson(
        json['end_stop_location'] as Map<String, dynamic>?,
      ),
      spotAvailableForReservation:
          json['spot_available_for_reservation'] as int?,
      tripStartDate: json['trip_start_date'] as String?,
      tripEndDate: json['trip_end_date'] as String?,
      costPerKilometer: (json['cost_per_kilometer'] as num?)?.toDouble(),
      deadlineDate: json['deadline_date'] as String?,
      totalTripDistance: (json['total_trip_distance'] as num?)?.toDouble(),
      totalBookedCars: json['total_booked_cars'] as int?,
      intermediatePickUpPoint: (json['intermediate_pick_up_point']
              as List<dynamic>?)
          ?.map(
            (e) => IntermediatePickUpPoint.fromJson(e as Map<String, dynamic>?),
          )
          .toList(),
      customPoints: json['custom_points'] == null
          ? null
          : (json['custom_points'] as List<dynamic>)
              .map(
                (e) => Address.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      tripType: json['trip_type'] as String?,
      tripId: json['trip_id'] as String?,
      sharedTripData: SharedTripData.fromJson(
        json['shared_trip_data'] as Map<String, dynamic>?,
      ),
      exclusiveTripData: ExclusiveTripData.fromJson(
        json['exclusive_trip_data'] as Map<String, dynamic>?,
      ),
      waitingListTripData: WaitingListTripData.fromJson(
        json['waiting_list_trip_data'] as Map<String, dynamic>?,
      ),
      reports: (json['reports'] as List<dynamic>?)
          ?.map((e) => Report.fromJson(e as Map<String, dynamic>?))
          .toList(),
      coveredDistance: (json['covered_distance'] as num?)?.toDouble(),
      isTripCancellable: json['is_trip_cancellable'] as bool?,
      id: json['id'] as int?,
      equipment: Equipment.fromJson(json['equipment'] as Map<String, dynamic>?),
      driver: Driver.fromJson(json['driver'] as Map<String, dynamic>?),
      allowIntermediatePickup: json['allow_intermediate_pickup'] as bool?,
      status: json['status'] as String?,
      completedAt: json['completed_at'] as String?,
      totalTripCost: (json['total_trip_cost'] as num?)?.toDouble(),
    );
  }
  final StopLocation? startStopLocation;
  final StopLocation? endStopLocation;
  final int? spotAvailableForReservation;
  final String? tripStartDate;
  final String? tripEndDate;
  final double? costPerKilometer;
  final String? deadlineDate;
  final double? totalTripDistance;
  final int? totalBookedCars;
  final List<IntermediatePickUpPoint>? intermediatePickUpPoint;
  final List<Address>? customPoints;
  final String? tripType;
  final String? tripId;
  final SharedTripData? sharedTripData;
  final ExclusiveTripData? exclusiveTripData;
  final WaitingListTripData? waitingListTripData;
  final List<Report>? reports;
  final double? coveredDistance;
  final bool? isTripCancellable;
  final int? id;
  final Equipment? equipment;
  final Driver? driver;
  final bool? allowIntermediatePickup;
  final String? status;
  final String? completedAt;
  final double? totalTripCost;

  Map<String, dynamic> toJson() {
    return {
      'start_stop_location': startStopLocation?.toJson(),
      'end_stop_location': endStopLocation?.toJson(),
      'spot_available_for_reservation': spotAvailableForReservation,
      'trip_start_date': tripStartDate,
      'trip_end_date': tripEndDate,
      'cost_per_kilometer': costPerKilometer,
      'deadline_date': deadlineDate,
      'total_trip_distance': totalTripDistance,
      'total_booked_cars': totalBookedCars,
      'intermediate_pick_up_point':
          intermediatePickUpPoint?.map((e) => e.toJson()).toList(),
      'custom_points': customPoints?.map((e) => e.toJson()).toList(),
      'trip_type': tripType,
      'trip_id': tripId,
      'shared_trip_data': sharedTripData?.toJson(),
      'exclusive_trip_data': exclusiveTripData?.toJson(),
      'waiting_list_trip_data': waitingListTripData?.toJson(),
      'reports': reports?.map((e) => e.toJson()).toList(),
      'covered_distance': coveredDistance,
      'is_trip_cancellable': isTripCancellable,
      'id': id,
      'equipment': equipment?.toJson(),
      'driver': driver?.toJson(),
      'allow_intermediate_pickup': allowIntermediatePickup,
      'status': status,
      'completed_at': completedAt,
      'total_trip_cost': totalTripCost,
    };
  }
}
