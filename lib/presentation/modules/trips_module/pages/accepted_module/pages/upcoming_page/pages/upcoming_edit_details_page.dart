// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:transportmatch_provider/l10n/l10n.dart';
// import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart'
//     show AcceptedTripModelData;
// import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
// import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_screen/provider/accepted_details_provider.dart';
// import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_screen/widgets/location_row.dart';
// import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_screen/widgets/vehicles_widget.dart';
// import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/trip_info_widgets.dart';
// import 'package:transportmatch_provider/utils/app_colors.dart';
// import 'package:transportmatch_provider/utils/app_size.dart';
// import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
// import 'package:transportmatch_provider/widgets/app_button.dart';
// import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

// /// Upcoming Edit Details Screen
// class UpcomingEditDetailsPage extends StatelessWidget {
//   /// Constructor
//   const UpcomingEditDetailsPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Builder(
//       builder: (context) {
//         final tripDetailsProvider = context.read<TripDetailsProvider>();
//         return Scaffold(
//           backgroundColor: AppColors.ffF8F9FA,
//           appBar: CustomAppBar(
//             title: context.l10n.editDetails,
//             actions: [
//               IconButton(
//                 onPressed: () {
//                   tripDetailsProvider.editUpdate(
//                     value: tripDetailsProvider.isEdit =
//                         !tripDetailsProvider.isEdit,
//                   );
//                 },
//                 icon: AppAssets.iconsEditProfile
//                     .image(height: AppSize.h500, fit: BoxFit.fitHeight),
//               ),
//             ],
//           ),
//           body: Padding(
//             padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
//             child: SingleChildScrollView(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(context.l10n.currentTripStatus),
//                   Row(
//                     children: [
//                       Expanded(
//                         child: GestureDetector(
//                           onTap: () {
//                             if (tripDetailsProvider.isEdit == true) {
//                               tripDetailsProvider.selectedIndexUpdate(
//                                 value: 0,
//                               );
//                             }
//                           },
//                           child: Padding(
//                             padding: const EdgeInsets.all(8),
//                             child: Container(
//                               decoration: BoxDecoration(
//                                 color: tripDetailsProvider.selectedIndex == 0
//                                     ? tripDetailsProvider.isEdit
//                                         ? AppColors.ffDCD2EE
//                                         : AppColors.ffF2EEF8
//                                     : AppColors.transparent,
//                                 borderRadius: BorderRadius.circular(AppSize.r5),
//                                 border: Border.all(color: AppColors.ffE9ECEF),
//                               ),
//                               child: Padding(
//                                 padding: EdgeInsets.symmetric(
//                                   vertical: AppSize.h10,
//                                 ),
//                                 child: Center(
//                                   child: Text(
//                                     context.l10n.upcoming,
//                                     style: TextStyle(
//                                       color:
//                                           tripDetailsProvider.selectedIndex == 0
//                                               ? tripDetailsProvider.isEdit
//                                                   ? AppColors.ff67509C
//                                                   : AppColors.ffC6B5E4
//                                               : AppColors.ffADB5BD,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                       Expanded(
//                         child: GestureDetector(
//                           onTap: () {
//                             if (tripDetailsProvider.isEdit == true) {
//                               tripDetailsProvider.selectedIndexUpdate(
//                                 value: 1,
//                               );
//                             }
//                           },
//                           child: Padding(
//                             padding: const EdgeInsets.all(8),
//                             child: Container(
//                               decoration: BoxDecoration(
//                                 color: tripDetailsProvider.selectedIndex == 1
//                                     ? tripDetailsProvider.isEdit
//                                         ? AppColors.ffDCD2EE
//                                         : AppColors.ffF2EEF8
//                                     : AppColors.transparent,
//                                 border: Border.all(color: AppColors.ffE9ECEF),
//                                 borderRadius: BorderRadius.circular(AppSize.r5),
//                               ),
//                               child: Padding(
//                                 padding: EdgeInsets.symmetric(
//                                   vertical: AppSize.h10,
//                                 ),
//                                 child: Center(
//                                   child: Text(
//                                     context.l10n.ongoing,
//                                     style: TextStyle(
//                                       color:
//                                           tripDetailsProvider.selectedIndex == 1
//                                               ? tripDetailsProvider.isEdit
//                                                   ? AppColors.ff67509C
//                                                   : AppColors.ffC6B5E4
//                                               : AppColors.ffADB5BD,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                       Expanded(
//                         child: GestureDetector(
//                           onTap: () {
//                             if (tripDetailsProvider.isEdit) {
//                               tripDetailsProvider.selectedIndexUpdate(
//                                 value: 2,
//                               );
//                             }
//                           },
//                           child: Padding(
//                             padding: const EdgeInsets.all(8),
//                             child: Container(
//                               decoration: BoxDecoration(
//                                 color: tripDetailsProvider.selectedIndex == 2
//                                     ? tripDetailsProvider.isEdit
//                                         ? AppColors.ffDCD2EE
//                                         : AppColors.ffF2EEF8
//                                     : AppColors.transparent,
//                                 border: Border.all(color: AppColors.ffE9ECEF),
//                                 borderRadius: BorderRadius.circular(AppSize.r5),
//                               ),
//                               child: Padding(
//                                 padding: EdgeInsets.symmetric(
//                                   vertical: AppSize.h10,
//                                 ),
//                                 child: Center(
//                                   child: Text(
//                                     context.l10n.completed,
//                                     style: TextStyle(
//                                       color:
//                                           tripDetailsProvider.selectedIndex == 2
//                                               ? tripDetailsProvider.isEdit
//                                                   ? AppColors.ff67509C
//                                                   : AppColors.ffC6B5E4
//                                               : AppColors.ffADB5BD,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(bottom: AppSize.h10),
//                     child: TripInfoWidgets(
//                       readOnly: !tripDetailsProvider.isEdit,
//                       acceptedTripProvider: context.read(),
//                       tripDataProvider: context.read(),
//                     ),
//                   ),
//                   VehiclesWidget(
//                     carList: const [],
//                     clientName: '',
//                     booking: BookingModel(),
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(bottom: AppSize.h10),
//                     child: LocationRow(
//                       icon: AppAssets.iconsLocationOrigin.image(
//                         width: AppSize.w20,
//                         height: AppSize.h20,
//                         fit: BoxFit.cover,
//                       ),
//                       title: 'Drop to Otay Mesa',
//                       date: 'Nov 21',
//                     ),
//                   ),
//                   LocationRow(
//                     icon: AppAssets.iconsLocation.image(
//                       width: AppSize.w20,
//                       height: AppSize.h20,
//                       fit: BoxFit.cover,
//                     ),
//                     title: 'Pickup to Baja California',
//                     date: 'Nov 28',
//                   ),
//                   Padding(
//                     padding: EdgeInsets.symmetric(vertical: AppSize.h20),
//                     child: AppButton(
//                       text: context.l10n.cancelBooking,
//                       isFillButton: false,
//                       borderColor: Colors.red,
//                       textStyle: const TextStyle(
//                         color: Colors.red,
//                         fontWeight: FontWeight.w600,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
