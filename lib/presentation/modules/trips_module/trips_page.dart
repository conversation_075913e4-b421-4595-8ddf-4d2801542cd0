import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/accepted_trip_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/requested_trips_page.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/common_tab_widgets.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// Trips Screen
class TripsPage extends StatefulWidget {
  /// Constructor
  const TripsPage({super.key});

  @override
  State<TripsPage> createState() => _TripsPageState();
}

class _TripsPageState extends State<TripsPage>
    with SingleTickerProviderStateMixin {
  late final TabController tabController;
  final ValueNotifier<int> activeTabTitleBuilder = ValueNotifier(0);

  void tabListener() {
    activeTabTitleBuilder.value = tabController.index;
  }

  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(tabListener);
    super.initState();
  }

  @override
  void dispose() {
    tabController
      ..removeListener(tabListener)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider(
      create: (context) => TripDataProvider(),
      child: Builder(
        builder: (context) {
          return SafeArea(
            child: Column(
              children: [
                ValueListenableBuilder<int>(
                  valueListenable: activeTabTitleBuilder,
                  builder: (context, active, _) {
                    return TabBar(
                      controller: tabController,
                      dividerColor: AppColors.transparent,
                      indicatorSize: TabBarIndicatorSize.tab,
                      indicatorColor: AppColors.primaryColor,
                      tabs: [
                        CommonTabBar(
                          title: l10n.requests,
                          index: 0,
                          animation: tabController.animation!,
                        ),
                        CommonTabBar(
                          title: l10n.accepted,
                          index: 1,
                          animation: tabController.animation!,
                        ),
                      ],
                    );
                  },
                ),
                Gap(AppSize.sp10),
                Flexible(
                  child: TabBarView(
                    controller: tabController,
                    children: [
                      RequestedTripsPage(
                        tripDataProvider: context.read<TripDataProvider>(),
                      ),
                      const AcceptedTripPage(),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
