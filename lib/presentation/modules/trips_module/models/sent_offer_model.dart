import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';

class SentOfferModel {
  SentOfferModel({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory SentOfferModel.fromJson(Map<String, dynamic> json) => SentOfferModel(
        count: json['count'] as int?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<SentOffer>.from(
                (json['results'] as List?)?.map(
                      (x) => SentOffer.fromJson(x as Map<String, dynamic>),
                    ) ??[ ],
              ),
      );
  int? count;
  String? next;
  String? previous;
  List<SentOffer>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results == null
            ? []
            : List<dynamic>.from(results!.map((x) => x.toJson())),
      };
}

class SentOffer {
  SentOffer({
    this.id,
    this.tripStartDate,
    this.tripEndDate,
    this.startStopLocation,
    this.endStopLocation,
    this.spotAvailableForReservation,
    this.exclusiveTrips,
    this.bookedSlot,
    this.totalCar,
  });

  factory SentOffer.fromJson(Map<String, dynamic> json) => SentOffer(
        id: json['id'] as int?,
        tripStartDate: json['trip_start_date'] == null
            ? null
            : DateTime.parse(json['trip_start_date'] as String),
        tripEndDate: json['trip_end_date'] == null
            ? null
            : DateTime.parse(json['trip_end_date'] as String),
        startStopLocation: json['start_stop_location'] as String?,
        endStopLocation: json['end_stop_location'] as String?,
        bookedSlot: json['booked_slot'] as num?,
        totalCar: json['cars_wanted_to_move'] as num?,
        spotAvailableForReservation:
            json['spot_available_for_reservation'] as num?,
        exclusiveTrips: json['exclusive_trips'] == null
            ? null
            : ExclusiveTrips.fromJson(
                json['exclusive_trips'] as Map<String, dynamic>,
              ),
      );
  int? id;
  num? bookedSlot;
  num? totalCar;
  DateTime? tripStartDate;
  DateTime? tripEndDate;
  String? startStopLocation;
  String? endStopLocation;
  num? spotAvailableForReservation;
  ExclusiveTrips? exclusiveTrips;

  Map<String, dynamic> toJson() => {
        'id': id,
        'trip_start_date': tripStartDate?.toIso8601String(),
        'trip_end_date': tripEndDate?.toIso8601String(),
        'start_stop_location': startStopLocation,
        'end_stop_location': endStopLocation,
        'booked_slot': bookedSlot,
        'cars_wanted_to_move': totalCar,
        'spot_available_for_reservation': spotAvailableForReservation,
        'exclusive_trips': exclusiveTrips?.toJson(),
      };
}

class ExclusiveTrips {
  ExclusiveTrips({
    this.id,
    this.userStartLocation,
    this.userEndLocation,
    this.offerStatus,
    this.booking,
    this.exclusiveTripId,
  });

  factory ExclusiveTrips.fromJson(Map<String, dynamic> json) => ExclusiveTrips(
        id: json['id'] as int?,
        userStartLocation: json['user_start_location'] == null
            ? null
            : UserLocation.fromJson(
                json['user_start_location'] as Map<String, dynamic>,
              ),
        userEndLocation: json['user_end_location'] == null
            ? null
            : UserLocation.fromJson(
                json['user_end_location'] as Map<String, dynamic>,
              ),
        offerStatus: json['offer_status'] as String?,
        booking: json['booking'] as int?,
        exclusiveTripId: json['exclusive_trip_id'] as String?,
      );
  int? id;
  UserLocation? userStartLocation;
  UserLocation? userEndLocation;
  String? offerStatus;
  int? booking;
  String? exclusiveTripId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'user_start_location': userStartLocation?.toJson(),
        'user_end_location': userEndLocation?.toJson(),
        'offer_status': offerStatus,
        'booking': booking,
        'exclusive_trip_id': exclusiveTripId,
      };
}

class OfferProviderModel {
  OfferProviderModel({
    this.id,
    this.offerPrice,
    this.ratings,
    this.isOwner,
  });

  factory OfferProviderModel.fromJson(Map<String, dynamic> json) =>
      OfferProviderModel(
        id: json['id'] as int?,
        offerPrice: json['cost_per_kilometer'] as num?,
        ratings: json['ratings'] as num?,
        isOwner: json['is_owner'] as bool?,
      );
  int? id;
  num? offerPrice;
  num? ratings;
  bool? isOwner;

  Map<String, dynamic> toJson() => {
        'id': id,
        'cost_per_kilometer': offerPrice,
        'ratings': ratings,
        'is_owner': isOwner,
      };
}
