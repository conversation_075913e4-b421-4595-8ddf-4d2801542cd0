import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';

class ExclusiveTripModel {
  ExclusiveTripModel({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory ExclusiveTripModel.fromJson(Map<String, dynamic> json) =>
      ExclusiveTripModel(
        count: json['count'] as int?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<ExclusiveTrip>.from(
                (json['results'] as List<dynamic>).map(
                  (x) => ExclusiveTrip.fromJson(x as Map<String, dynamic>),
                ),
              ),
      );
  int? count;
  String? next;
  String? previous;
  List<ExclusiveTrip>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results == null
            ? []
            : List<dynamic>.from(results!.map((x) => x.toJson())),
      };
}

class ExclusiveTrip {
  ExclusiveTrip({
    this.id,
    this.customer,
    this.userStartLocation,
    this.isOfferRejected,
    this.userEndLocation,
    this.totalTripDistance,
    this.customerStartDate,
    this.customerEndDate,
    this.allVehicleInOneTrip,
    this.bookingStatus,
    this.bookingType,
    this.waitingType,
    this.carDetails,
    this.createdAt,
  });

  factory ExclusiveTrip.fromJson(Map<String, dynamic> json) => ExclusiveTrip(
        id: json['id'] as int?,
        customer: json['customer'] == null
            ? null
            : Customer.fromJson(json['customer'] as Map<String, dynamic>),
        userStartLocation: json['user_start_location'] == null
            ? null
            : UserLocation.fromJson(
                json['user_start_location'] as Map<String, dynamic>,
              ),
        isOfferRejected: json['is_offer_rejected'] as bool?,
        userEndLocation: json['user_end_location'] == null
            ? null
            : UserLocation.fromJson(
                json['user_end_location'] as Map<String, dynamic>,
              ),
        totalTripDistance: json['total_trip_distance'] as num?,
        customerStartDate: json['customer_start_date'] == null
            ? null
            : DateTime.parse(json['customer_start_date'] as String),
        customerEndDate: json['customer_end_date'] == null
            ? null
            : DateTime.parse(json['customer_end_date'] as String),
        allVehicleInOneTrip: json['all_vehicle_in_one_trip'] as bool?,
        bookingStatus: json['booking_status'] as String?,
        bookingType: json['booking_type'] as String?,
        waitingType: json['waiting_type'] as String?,
        carDetails: json['car_details'] == null
            ? []
            : List<CarDetail>.from(
                (json['car_details'] as List<dynamic>).map(
                  (x) => CarDetail.fromJson(x as Map<String, dynamic>),
                ),
              ),
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
      );
  int? id;
  Customer? customer;
  UserLocation? userStartLocation;
  UserLocation? userEndLocation;
  bool? isOfferRejected;
  num? totalTripDistance;
  DateTime? customerStartDate;
  DateTime? customerEndDate;
  bool? allVehicleInOneTrip;
  String? bookingStatus;
  String? bookingType;
  String? waitingType;
  List<CarDetail>? carDetails;
  DateTime? createdAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'customer': customer?.toJson(),
        'user_start_location': userStartLocation?.toJson(),
        'user_end_location': userEndLocation?.toJson(),
        'is_offer_rejected': isOfferRejected,
        'total_trip_distance': totalTripDistance,
        'customer_start_date': customerStartDate?.toIso8601String(),
        'customer_end_date': customerEndDate?.toIso8601String(),
        'all_vehicle_in_one_trip': allVehicleInOneTrip,
        'booking_status': bookingStatus,
        'booking_type': bookingType,
        'waiting_type': waitingType,
        'car_details': carDetails == null
            ? []
            : List<dynamic>.from(carDetails!.map((x) => x.toJson())),
        'created_at': createdAt?.toIso8601String(),
      };
}

class CarDetail {
  CarDetail({
    this.id,
    this.brand,
    this.year,
    this.model,
    this.size,
    this.serialNumber,
    this.car,
    this.isCarPickedUpToStopLocation,
    this.pickUpServiceAndDropOffService,
    this.pickUpServiceAndDropOffServiceType,
    this.fromCarToBePickedUpLocation,
    this.isCarVerificationRequired,
    this.verificationStatus,
    this.verificationStatusUpdatedAt,
    this.isWinchRequired,
    this.carDescription,
    this.images,
  });

  factory CarDetail.fromJson(Map<String, dynamic> json) => CarDetail(
        id: json['id'] as int?,
        brand: json['brand'] as String?,
        year: json['year']?.toString(),
        model: json['model'] as String?,
        size: json['size'] as num?,
        serialNumber: json['serial_number'] as String?,
        car: json['car'] == null
            ? null
            : Car.fromJson(json['car'] as Map<String, dynamic>),
        isCarPickedUpToStopLocation:
            json['is_car_picked_up_to_stop_location'] as bool?,
        pickUpServiceAndDropOffService:
            json['pick_up_service_and_drop_off_service'] as int?,
        pickUpServiceAndDropOffServiceType:
            json['pick_up_service_and_drop_off_service_type'] as String?,
        fromCarToBePickedUpLocation:
            json['from_car_to_be_picked_up_location'] == null
                ? null
                : Address.fromJson(
                    json['from_car_to_be_picked_up_location']
                        as Map<String, dynamic>,
                  ),
        isCarVerificationRequired:
            json['is_car_verification_required'] as bool?,
        verificationStatus: json['verification_status'] as String?,
        verificationStatusUpdatedAt: json['verification_status_updated_at'] ==
                null
            ? null
            : DateTime.parse(json['verification_status_updated_at'] as String),
        isWinchRequired: json['is_winch_required'] as bool?,
        carDescription: json['car_description'] as String?,
        images: json['images'] == null
            ? []
            : List<CarImageModel>.from(
                (json['images'] as List?)?.map(
                      (x) => CarImageModel.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
      );
  int? id;
  String? brand;
  String? year;
  String? model;
  num? size;
  String? serialNumber;
  Car? car;
  bool? isCarPickedUpToStopLocation;
  int? pickUpServiceAndDropOffService;
  String? pickUpServiceAndDropOffServiceType;
  Address? fromCarToBePickedUpLocation;
  bool? isCarVerificationRequired;
  dynamic verificationStatus;
  DateTime? verificationStatusUpdatedAt;
  bool? isWinchRequired;
  String? carDescription;
  List<CarImageModel>? images;

  Map<String, dynamic> toJson() => {
        'id': id,
        'brand': brand,
        'year': year,
        'model': model,
        'size': size,
        'serial_number': serialNumber,
        'car': car?.toJson(),
        'is_car_picked_up_to_stop_location': isCarPickedUpToStopLocation,
        'pick_up_service_and_drop_off_service': pickUpServiceAndDropOffService,
        'pick_up_service_and_drop_off_service_type':
            pickUpServiceAndDropOffServiceType,
        'from_car_to_be_picked_up_location': fromCarToBePickedUpLocation,
        'is_car_verification_required': isCarVerificationRequired,
        'verification_status': verificationStatus,
        'verification_status_updated_at':
            verificationStatusUpdatedAt?.toIso8601String(),
        'is_winch_required': isWinchRequired,
        'car_description': carDescription,
        'images': images == null
            ? []
            : List<dynamic>.from(
                (images as List?)?.map((x) => x.toJson()) ?? [],
              ),
      };
}

class Customer {
  Customer({
    this.firstName,
    this.lastName,
    this.email,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        firstName: json['first_name'] as String?,
        lastName: json['last_name'] as String?,
        email: json['email'] as String?,
      );
  String? firstName;
  String? lastName;
  String? email;

  Map<String, dynamic> toJson() => {
        'first_name': firstName,
        'last_name': lastName,
        'email': email,
      };
}

class UserLocation {
  UserLocation({
    this.street,
    this.neighborhood,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.countryCode,
    this.latitude,
    this.longitude,
  });

  factory UserLocation.fromJson(Map<String, dynamic> json) => UserLocation(
        street: json['street'] as String?,
        neighborhood: json['neighborhood'] as String?,
        city: json['city'] as String?,
        state: json['state'] as String?,
        postalCode: json['postal_code'] as String?,
        country: json['country'] as String?,
        countryCode: json['country_code'] as String?,
        latitude: json['latitude']?.toString(),
        longitude: json['longitude']?.toString(),
      );
  String? street;
  String? neighborhood;
  String? city;
  String? state;
  String? postalCode;
  String? country;
  String? countryCode;
  String? latitude;
  String? longitude;

  Map<String, dynamic> toJson() => {
        'street': street,
        'neighborhood': neighborhood,
        'city': city,
        'state': state,
        'postal_code': postalCode,
        'country': country,
        'country_code': countryCode,
        'latitude': latitude,
        'longitude': longitude,
      };
}
