// ignore_for_file: public_member_api_docs

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class LiveTripsProvider extends ChangeNotifier {
  LiveTripsProvider() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        await getTripDataApiCall(isWantShowLoader: true);
      },
    );
  }

  bool isClosed = false;
  final refreshController = RefreshController();
  ValueNotifier<List<SavedRoute>> liveTripList = ValueNotifier(<SavedRoute>[]);
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? getTripDataAPICallCancelToken;

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// get all live trip
  ValueNotifier<String?> nextUrl = ValueNotifier(null);
  Future<void> getTripDataApiCall({
    String? url,
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;
    try {
      getTripDataAPICallCancelToken?.cancel();
      getTripDataAPICallCancelToken = CancelToken();
      final request = ApiRequest(
        path: url ?? EndPoints.getTripList,
        cancelToken: getTripDataAPICallCancelToken,
      );
      if (isWantShowLoader) {
        isShowLoader.value = true;
      }

      final res =
          await Injector.instance<TripRepository>().getTripList(request);
      if (isClosed) return;
      if (isWantShowLoader) {
        isShowLoader.value = false;
      }
      await res.when(
        success: (data) async {
          if (isClosed ||
              (getTripDataAPICallCancelToken?.isCancelled ?? true)) {
            return;
          }
          if (url.isEmptyOrNull) liveTripList.value.clear();
          nextUrl.value = data.next as String?;
          for (final datas in data.results ?? <SavedRoute>[]) {
            if (!liveTripList.value.any((e) => e.id == datas.id)) {
              log('===== ${datas.toJson()}');
              liveTripList.value.add(datas);
            }
          }
          notify();
        },
        error: (exception) async {
          if (isClosed ||
              (getTripDataAPICallCancelToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getTripDataAPICallCancelToken?.isCancelled ?? true)) {
        return;
      }
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    getTripDataAPICallCancelToken?.cancel();
    refreshController.dispose();
    liveTripList.value.clear();
    liveTripList.dispose();
    isShowLoader.dispose();
    nextUrl.dispose();
    super.dispose();
  }
}
