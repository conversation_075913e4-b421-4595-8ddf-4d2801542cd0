// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/create_trip_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/location_widget.dart';

class SmallTripCard extends StatelessWidget {
  const SmallTripCard({
    required this.data,
    super.key,
  });
  final SavedRoute data;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.sp16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r6),
        color: AppColors.white,
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LocationWidget(
              title1: data.startStopLocation?.fullAddress ?? 'Start',
              date1: data.tripStartDate?.monthDate ?? '',
              title2: data.endStopLocation?.fullAddress ?? 'End',
              date2: data.tripEndDate?.monthDate ?? '',
              startLatitude: data.startStopLocation?.latitude ?? '',
              startLongitude: data.startStopLocation?.longitude ?? '',
              endLatitude: data.endStopLocation?.latitude ?? '',
              endLongitude: data.endStopLocation?.longitude ?? '',
            ),
            Gap(AppSize.h14),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: AppSize.h4,
                    children: [
                      Text(
                        context.l10n.driver,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp12,
                          color: AppColors.ff6C757D,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        "${data.driver?.firstName ?? ''} ${data.driver?.lastName ?? ''}"
                            .trim(),
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp16,
                          color: AppColors.ff343A40,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.homeNewRouteScreen,
                    extra: CreateTripParams(
                      savedRouteData: data,
                    ),
                  ),
                  child: Text(
                    context.l10n.edit,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontSize: AppSize.sp16,
                      color: AppColors.ff0087C7,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
