// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/enums/route_enum.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/create_trip_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/choose_start_and_end_stop_location_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/new_and_saved_route_selection_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/saved_trip_drop_down_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/search_location_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/select_stop_location_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/trip_date_and_time_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/trip_google_map_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/trip_info_widget.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/widgets/trip_pricing_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class CreateTripPage extends StatelessWidget {
  const CreateTripPage({
    super.key,
    this.newRouteParams,
  });
  final CreateTripParams? newRouteParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.pageBGColor,
      appBar: CustomAppBar(
        title: newRouteParams?.savedRouteData == null
            ? context.l10n.createTrip
            : context.l10n.editTrip,
      ),
      body: ChangeNotifierProvider(
        create: (context) => CreateTripProvider(
          isEdit: newRouteParams?.savedRouteData != null,
          isSetSavedRoute: newRouteParams?.isSetSavedRoute ?? false,
        ),
        child: Consumer<CreateTripProvider>(
          builder: (context, createTripProvider, child) {
            return ListenableBuilder(
              listenable: createTripProvider.isShowLoader,
              builder: (context, child) {
                return AppLoader(
                  isShowLoader: createTripProvider.isShowLoader.value,
                  child: SmartRefresher(
                    controller: createTripProvider.refreshController,
                    onRefresh: () {
                      Future.value(
                        [
                          createTripProvider.clearData(),
                          createTripProvider.getSavedTripList(
                            isFromRefresh: true,
                          ),
                        ],
                      ).whenComplete(
                        createTripProvider.refreshController.refreshCompleted,
                      );
                    },
                    child: child,
                  ),
                );
              },
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSize.appPadding,
                ),
                child: Column(
                  children: [
                    if (newRouteParams?.savedRouteData == null)
                      Gap(AppSize.h16),
                    if (newRouteParams?.savedRouteData == null)
                      NewAndSavedRouteSelectionWidget(
                        createTripProvider: createTripProvider,
                      ),
                    if (newRouteParams?.savedRouteData == null)
                      Gap(AppSize.h18),
                    if (newRouteParams?.savedRouteData == null)
                      SavedTripDropDownWidget(
                        createTripProvider: createTripProvider,
                      ),
                    if (
                        // (newRouteParams?.savedRouteData == null &&
                        !createTripProvider.isEdit &&
                            (createTripProvider.selectedRoute.value ==
                                RouteEnum.newRoute))
                      SearchLocationWidget(
                        createTripProvider: createTripProvider,
                      ),
                    if (
                        // newRouteParams?.savedRouteData == null
                        createTripProvider.selectedRoute.value ==
                            RouteEnum.newRoute)
                      Gap(AppSize.sp20),
                    ChooseStartAndEndStopLocation(
                      createTripProvider: createTripProvider,
                    ),
                    Gap(AppSize.sp20),

                    // ValueListenableBuilder(
                    //   valueListenable: createTripProvider.selectedRoute,
                    //   builder: (context, selectedRoute, _) {
                    //     return selectedRoute != RouteEnum.savedRoute
                    //         ? TripGoogleMapWidget(
                    //             createTripProvider: createTripProvider,
                    //             newAssignData: newRouteParams?.savedRouteData,
                    //           )
                    //         : const SizedBox.shrink();
                    //   },
                    // ),

                    // if(!createTripProvider.isEdit)
                    TripGoogleMapWidget(
                      createTripProvider: createTripProvider,
                      newAssignData: newRouteParams?.savedRouteData,
                    ),

                    SelectStopLocationWidget(
                      createTripProvider: createTripProvider,
                    ),
                    Gap(AppSize.sp20),
                    TripDateAndTimeWidget(
                      createTripProvider: createTripProvider,
                    ),
                    Gap(AppSize.sp20),

                    TripInfoWidget(createTripProvider: createTripProvider),
                    Gap(AppSize.sp20),
                    TripCostWidget(
                      createTripProvider: createTripProvider,
                      isReadOnly: newRouteParams?.savedRouteData != null ||
                          createTripProvider.isEdit,
                    ),
                    Gap(AppSize.sp20),
                    AppButton(
                      text: newRouteParams?.savedRouteData != null
                          ? context.l10n.save
                          : context.l10n.makeTripLive,
                      onPressed: () =>
                          createTripProvider.createUpdateRoute(context),
                    ),
                    Gap(AppSize.sp20),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
