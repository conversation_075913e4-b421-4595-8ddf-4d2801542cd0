// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/enums/route_enum.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

class NewAndSavedRouteSelectionWidget extends StatelessWidget {
  const NewAndSavedRouteSelectionWidget({
    required this.createTripProvider,
    super.key,
  });

  final CreateTripProvider createTripProvider;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<RouteEnum>(
      valueListenable: createTripProvider.selectedRoute,
      builder: (context, selectedRoute, _) {
        return Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  createTripProvider
                    ..selectedRoute.value = RouteEnum.newRoute
                    ..clearData();
                },
                child: Row(
                  children: [
                    Radio<RouteEnum>(
                      value: RouteEnum.newRoute,
                      visualDensity: const VisualDensity(
                        horizontal: VisualDensity.minimumDensity,
                        vertical: VisualDensity.minimumDensity,
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      groupValue: selectedRoute,
                      onChanged: (value) {
                        createTripProvider
                          ..selectedRoute.value = value!
                          ..clearData();
                      },
                    ),
                    Gap(AppSize.w6),
                    Text(
                      context.l10n.newRoute,
                      style: context.textTheme.titleMedium!.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: AppSize.sp18,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (createTripProvider.savedRouteList.value.isEmpty) {
                    createTripProvider
                      ..selectedRoute.value = RouteEnum.savedRoute
                      ..clearData()
                      ..getSavedTripList();
                  } else {
                    createTripProvider
                      ..selectedRoute.value = RouteEnum.savedRoute
                      ..clearData();
                  }
                },
                child: Row(
                  children: [
                    Radio<RouteEnum>(
                      value: RouteEnum.savedRoute,
                      visualDensity: const VisualDensity(
                        horizontal: VisualDensity.minimumDensity,
                        vertical: VisualDensity.minimumDensity,
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      groupValue: selectedRoute,
                      onChanged: (value) {
                        if (createTripProvider.savedRouteList.value.isEmpty &&
                            value == RouteEnum.savedRoute) {
                          createTripProvider
                            ..selectedRoute.value = value!
                            ..clearData()
                            ..getSavedTripList();
                        } else {
                          createTripProvider
                            ..selectedRoute.value = value!
                            ..clearData();
                        }
                      },
                    ),
                    Gap(AppSize.w6),
                    Text(
                      context.l10n.savedRoute,
                      style: context.textTheme.titleMedium!.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: AppSize.sp18,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
