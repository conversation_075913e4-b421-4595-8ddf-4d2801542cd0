// ignore_for_file: public_member_api_docs

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/enums/route_enum.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_dropdown.dart';

class SavedTripDropDownWidget extends StatelessWidget {
  const SavedTripDropDownWidget({
    required this.createTripProvider,
    super.key,
  });

  final CreateTripProvider createTripProvider;
  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        return ValueListenableBuilder(
          valueListenable: createTripProvider.selectedRoute,
          builder: (context, selectedRoute, _) {
            return selectedRoute != RouteEnum.savedRoute
                ? const SizedBox.shrink()
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Gap(AppSize.sp10),
                      ValueListenableBuilder(
                        valueListenable: createTripProvider.savedRouteList,
                        builder: (context, savedRouteList, child) {
                          return AppDropdown(
                            title: context.l10n.savedRoute,
                            controller: createTripProvider
                                .selectedSavedRouteDropDownController,
                            items: savedRouteList
                                .map(
                                  (e) => DropDownValueModel(
                                    value: e.id?.toString(),
                                    name:
                                        "${e.startStopLocation?.name ?? ''} ${context.l10n.to} ${e.endStopLocation?.name ?? ''}",
                                  ),
                                )
                                .toList(),
                          );
                        },
                      ),
                      Gap(AppSize.sp20),
                    ],
                  );
          },
        );
      },
    );
  }
}
