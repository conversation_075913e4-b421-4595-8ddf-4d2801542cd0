// ignore_for_file: public_member_api_docs, lines_longer_than_80_chars

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/address_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/search_address_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_image.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

class SearchLocationWidget extends StatelessWidget {
  const SearchLocationWidget({
    required this.createTripProvider,
    super.key,
  });

  final CreateTripProvider createTripProvider;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Column(
          children: [
            ValueListenableBuilder(
              valueListenable: createTripProvider.originLocationController,
              builder: (context, value, child) {
                return AppTextFormField(
                  controller: TextEditingController(
                    text: value?.address ?? value?.street,
                  ),
                  hintText: context.l10n.searchOriginLocation,
                  readOnly: true,
                  onTap: () async {
                    await AppNavigationService.pushNamed(
                      context,
                      AppRoutes.homeSearchAddressPage,
                      extra: SearchAddressParams(
                        createTripProvider: createTripProvider,
                        title: context.l10n.searchOriginLocation,
                        onTap: (address) {
                          createTripProvider.originLocationController.value =
                              AddressModel(
                            address: address.address,
                            latitude: address.latitude,
                            longitude: address.longitude,
                          );
                        },
                      ),

                      // AddressSearch(
                      //   createTripProvider: createTripProvider,
                      //   title: context.l10n.searchOriginLocation,
                      //   onTap: (address) {
                      //     createTripProvider.originLocationController.value =
                      //         AddressModel(
                      //       address: address.address,
                      //       latitude: address.latitude,
                      //       longitude: address.longitude,
                      //     );
                      //   },
                      // ),

                      /// below function is for transfer
                      /// value from address search to menu screen
                      afterBack: (value) {
                        if (value is (AddressModel, String)) {
                          final stockLocation = createTripProvider
                              .originStockLocationList.value
                              .firstWhere(
                            (e) => e.id.toString() == value.$2,
                          );
                          final lat = LatLng(
                            double.parse(
                              stockLocation.address?.latitude ?? '0',
                            ),
                            double.parse(
                              stockLocation.address?.longitude ?? '0',
                            ),
                          );

                          createTripProvider
                            ..originLocationController.value = AddressModel(
                              address: value.$1.address,
                              latitude: value.$1.latitude,
                              longitude: value.$1.longitude,
                            )
                            ..clearIntermediatePoints()
                            ..selectedOriginStockLocation.setDropDown(
                              DropDownValueModel(
                                name: stockLocation.name ?? '',
                                value: stockLocation,
                              ),
                            )
                            ..addGoogleMapMarker(lat, ApiKeys.start)
                            ..fitBounds(lat)
                            ..notify();
                        }
                      },
                    );
                    // final result = await showSearch(
                    //   context: context,
                    //   delegate: AddressSearch(),
                    // );
                    // if ((result?.placeId ?? '').isNotEmpty) {
                    //   // final url =
                    //   //     'https://maps.googleapis.com/maps/api/place/details/json?place_id=${result.placeId}&key=${FlutterConfig.get('GOOGLE_DIRECTION_KEY') as String}';
                    //   // final response = await http.get(Uri.parse(url));
                    //   // final jsonData = json.decode(response.body);
                    //   // final jsonResult = jsonData['result']['geometry']['location']
                    //   //     as Map<String, dynamic>;
                    //   // await controller.controller?.animateCamera(
                    //   //   CameraUpdate.newCameraPosition(
                    //   //     CameraPosition(
                    //   //       target: LatLng(
                    //   //         double.parse(jsonResult.values.elementAt(0).toString()),
                    //   //         double.parse(jsonResult.values.elementAt(1).toString()),
                    //   //       ),
                    //   //       zoom: 12,
                    //   //     ),
                    //   //   ),
                    //   // );
                    //   controller.originLocationController.text =
                    //       result!.description;
                    // }
                  },
                );
              },
            ),
            Gap(AppSize.h12),
            ValueListenableBuilder(
              valueListenable: createTripProvider.dropLocationController,
              builder: (context, value, child) {
                return AppTextFormField(
                  controller: TextEditingController(
                    text: value?.address ?? value?.street,
                  ),
                  hintText: context.l10n.searchDropLocation,
                  readOnly: true,
                  onTap: () async {
                    await AppNavigationService.pushNamed(
                      context,
                      AppRoutes.homeSearchAddressPage,
                      extra: SearchAddressParams(
                        createTripProvider: createTripProvider,
                        title: context.l10n.searchDropLocation,
                        isDrop: true,
                        onTap: (address) {
                          createTripProvider.dropLocationController.value =
                              AddressModel(
                            address: address.address,
                            latitude: address.latitude,
                            longitude: address.longitude,
                          );
                        },
                      ),

                      // AddressSearch(
                      //   createTripProvider: createTripProvider,
                      //   title: context.l10n.searchDropLocation,
                      //   isDrop: true,
                      //   onTap: (address) => createTripProvider
                      //       .dropLocationController.value = AddressModel(
                      //     address: address.address,
                      //     latitude: address.latitude,
                      //     longitude: address.longitude,
                      //   ),
                      // ),

                      /// below function is for transfer
                      /// value from address search to menu screen
                      afterBack: (value) {
                        if (value is (AddressModel, String)) {
                          final stockLocation = createTripProvider
                              .dropStockLocationList.value
                              .firstWhere(
                            (e) => e.id.toString() == value.$2,
                          );
                          final lat = LatLng(
                            double.parse(
                              stockLocation.address?.latitude ?? '0',
                            ),
                            double.parse(
                              stockLocation.address?.longitude ?? '0',
                            ),
                          );
                          createTripProvider
                            ..dropLocationController.value = AddressModel(
                              address: value.$1.address,
                              latitude: value.$1.latitude,
                              longitude: value.$1.longitude,
                            )
                            ..clearIntermediatePoints()
                            ..selectedDropStockLocation.setDropDown(
                              DropDownValueModel(
                                name: stockLocation.name ?? '',
                                value: stockLocation,
                              ),
                            )
                            ..addGoogleMapMarker(lat, ApiKeys.end)
                            ..fitBounds(lat)
                            ..notify();
                        }
                      },
                    );
                    // final result = await showSearch(
                    //   context: context,
                    //   delegate: const AddressSearch(),
                    // );
                    // if ((result?.placeId ?? '').isNotEmpty) {
                    //   // final url =
                    //   //     'https://maps.googleapis.com/maps/api/place/details/json?place_id=${result.placeId}&key=${FlutterConfig.get('GOOGLE_DIRECTION_KEY') as String}';
                    //   // final response = await http.get(Uri.parse(url));
                    //   // final jsonData = json.decode(response.body);
                    //   // final jsonResult = jsonData['result']['geometry']['location']
                    //   //     as Map<String, dynamic>;
                    //   // await controller.controller?.animateCamera(
                    //   //   CameraUpdate.newCameraPosition(
                    //   //     CameraPosition(
                    //   //       target: LatLng(
                    //   //         double.parse(jsonResult.values.elementAt(0).toString()),
                    //   //         double.parse(jsonResult.values.elementAt(1).toString()),
                    //   //       ),
                    //   //       zoom: 12,
                    //   //     ),
                    //   //   ),
                    //   // );
                    //   controller.dropLocationController.text =
                    //       result!.description;
                    // }
                  },
                );
              },
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            InkWell(
              onTap: () async {
                if (createTripProvider.originLocationController.value == null &&
                    createTripProvider.dropLocationController.value == null) {
                  return;
                }

                /// for transfer origin and stock location value
                final temp = createTripProvider.originLocationController.value;
                createTripProvider.originLocationController.value =
                    createTripProvider.dropLocationController.value;
                createTripProvider.dropLocationController.value = temp;

                /// for transfer selected stock location value
                final tempSelection =
                    // controller.selectedOriginStockLocation.value;
                    createTripProvider
                        .selectedOriginStockLocation.dropDownValue;

                createTripProvider.selectedOriginStockLocation.setDropDown(
                  createTripProvider.selectedDropStockLocation.dropDownValue,
                );
                // controller.selectedDropStockLocation.value;
                createTripProvider.selectedDropStockLocation
                    .setDropDown(tempSelection);
                // = tempSelection;

                /// for transfer stock location list
                final tempList =
                    createTripProvider.originStockLocationList.value;
                createTripProvider.originStockLocationList.value =
                    createTripProvider.dropStockLocationList.value;
                createTripProvider.dropStockLocationList.value = tempList;

                /// Swap start and end location markers
                final startMarker = createTripProvider.markers.firstWhere(
                  (m) => m.markerId.value == ApiKeys.start,
                  orElse: () => const Marker(markerId: MarkerId('')),
                );
                final endMarker = createTripProvider.markers.firstWhere(
                  (m) => m.markerId.value == ApiKeys.end,
                  orElse: () => const Marker(markerId: MarkerId('')),
                );
                final newStartMarker = Marker(
                  markerId: const MarkerId(ApiKeys.start),
                  position: endMarker.position,
                  icon: endMarker.icon,
                  infoWindow: endMarker.infoWindow,
                  onTap: endMarker.onTap,
                );
                final newEndMarker = Marker(
                  markerId: const MarkerId(ApiKeys.end),
                  position: startMarker.position,
                  icon: startMarker.icon,
                  infoWindow: startMarker.infoWindow,
                  onTap: startMarker.onTap,
                );

                if (startMarker.markerId.value.isNotEmpty &&
                    endMarker.markerId.value.isNotEmpty) {
                  createTripProvider.markers
                    ..remove(startMarker)
                    ..remove(endMarker)
                    ..insert(0, newStartMarker)
                    ..add(newEndMarker);
                }

                await createTripProvider.addAllStopCities(
                  createTripProvider.selectedStopCities.value,
                );

                /// change intermediate points and clear all selected date and time
                for (var i = 0;
                    i < createTripProvider.selectedStopCities.value.length;
                    i++) {
                  createTripProvider
                    ..selectedStopCities.value[i].date = null
                    ..selectedStopCities.value[i].time = null;
                }
                createTripProvider
                  ..selectedStopCities.value = createTripProvider
                      .selectedStopCities.value.reversed
                      .toList()
                  ..notify();
              },
              child: Container(
                height: AppSize.sp40,
                width: AppSize.sp40,
                margin: EdgeInsets.only(right: AppSize.sp14),
                padding: EdgeInsets.all(AppSize.sp10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppSize.r14),
                  border: Border.all(
                    color: AppColors.ffDEE2E6,
                  ),
                  color: AppColors.white,
                ),
                child: AppImage.asset(AppAssets.iconsSwap.path),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
