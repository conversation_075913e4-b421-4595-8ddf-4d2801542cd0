// ignore_for_file: public_member_api_docs

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_dropdown.dart';

class ChooseStartAndEndStopLocation extends StatelessWidget {
  const ChooseStartAndEndStopLocation({
    required this.createTripProvider,
    super.key,
  });

  final CreateTripProvider createTripProvider;

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: createTripProvider.isEdit ? 0.5 : 1,
      child: AbsorbPointer(
        absorbing: createTripProvider.isEdit,
        child: Container(
          padding: EdgeInsets.all(AppSize.h20),
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(AppSize.r10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: AppSize.h4,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: AppSize.h4),
                child: Text(
                  context.l10n.stockLocations,
                  style: context.textTheme.titleLarge?.copyWith(
                    fontSize: AppSize.w20,
                    fontWeight: FontWeight.w700,
                    color: AppColors.ff212529,
                  ),
                ),
              ),
              Row(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: AppSize.h4),
                    child: AppAssets.iconsLocationOrigin.image(
                      height: AppSize.h16,
                      width: AppSize.w14,
                    ),
                  ),
                  Text(
                    ' ${context.l10n.originStockLocation}',
                    style: context.textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(bottom: AppSize.h16),
                child: ValueListenableBuilder(
                  valueListenable: createTripProvider.originStockLocationList,
                  builder: (context, value, child) {
                    return InkWell(
                      onTap: value.isEmpty
                          ? () {
                              context.l10n
                                  .pleaseSearchOriginLocationToViewStockLocations
                                  .showInfoAlert();
                            }
                          : null,
                      child: AbsorbPointer(
                        absorbing: value.isEmpty,
                        child: AppDropdown(
                          controller:
                              createTripProvider.selectedOriginStockLocation,
                          fillColor: AppColors.ffF8F9FA,
                          hintText: context.l10n.chooseOriginStockLocation,
                          // validator: (p0) => ,
                          items: value
                              .map(
                                (e) => DropDownValueModel(
                                  value: e,
                                  name: e.name?.toString() ?? '',
                                ),
                              )
                              .toList(),
                          // selectedItem:
                          //     controller.selectedOriginStockLocation.value?.name,
                          onChanged: (dropdownValue) {
                            if (dropdownValue != null &&
                                dropdownValue is DropDownValueModel 
                               ) {
                              // controller.selectedOriginStockLocation
                              //     .setDropDown(dropdownValue);
                              // controller.selectedOriginStockLocation.value = controller
                              //     .originStockLocationList.value
                              //     .firstWhere((e) => e.name == value);
                              // final address = controller.originStockLocationList.value
                              //     .firstWhere((element) => element.name == value)
                              //     .address;
                              final lat = LatLng(
                                double.parse(
                                  (dropdownValue.value as StopLocation?)
                                          ?.address
                                          ?.latitude ??
                                      '0',
                                ),
                                double.parse(
                                  (dropdownValue.value as StopLocation?)
                                          ?.address
                                          ?.longitude ??
                                      '0',
                                ),
                              );
                              createTripProvider
                                ..addGoogleMapMarker(lat, ApiKeys.start)
                                ..fitBounds(lat);
                            }
                            // Handle selection
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
              Row(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: AppSize.h4),
                    child: AppAssets.iconsLocation.image(
                      height: AppSize.h16,
                      width: AppSize.w14,
                    ),
                  ),
                  Text(
                    ' ${context.l10n.dropStockLocation}',
                    style: context.textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              ValueListenableBuilder(
                valueListenable: createTripProvider.dropStockLocationList,
                builder: (context, value, child) {
                  return InkWell(
                    onTap: value.isEmpty
                        ? () {
                            context
                                .l10n.pleaseSearchDropLocationToViewStockLocations
                                .showInfoAlert();
                          }
                        : null,
                    child: AbsorbPointer(
                      absorbing: value.isEmpty,
                      child: AppDropdown(
                        controller: createTripProvider.selectedDropStockLocation,
                        fillColor: AppColors.ffF8F9FA,
                        hintText: context.l10n.chooseDropStockLocation,
                        items: value
                            .map(
                              (e) => DropDownValueModel(
                                value: e,
                                name: e.name?.toString() ?? '',
                              ),
                            )
                            .toList(),
                        // selectedItem: controller.selectedDropStockLocation.value?.name,
                        onChanged: (dropdownValue) {
                          if (dropdownValue != null &&
                              dropdownValue is DropDownValueModel) {
                            // controller.selectedDropStockLocation
                            //     .setDropDown(dropdownValue);
                            // controller.selectedDropStockLocation.value = controller
                            //     .dropStockLocationList.value
                            //     .firstWhere((e) => e.name == value);
                            // final address = controller.dropStockLocationList.value
                            //     .firstWhere((element) => element.name == value)
                            //     .address;
                            final lat = LatLng(
                              double.parse(
                                (dropdownValue.value as StopLocation?)
                                        ?.address
                                        ?.latitude ??
                                    '0',
                              ),
                              double.parse(
                                (dropdownValue.value as StopLocation?)
                                        ?.address
                                        ?.longitude ??
                                    '0',
                              ),
                            );
                            createTripProvider
                              ..addGoogleMapMarker(lat, ApiKeys.end)
                              ..fitBounds(lat);
                          }
                          // Handle selection
                        },
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
