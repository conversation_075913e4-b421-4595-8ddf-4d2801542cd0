// ignore_for_file: public_member_api_docs

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/address_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/search_address_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class TripGoogleMapWidget extends StatelessWidget {
  const TripGoogleMapWidget({
    required this.createTripProvider,
    this.newAssignData,
    super.key,
  });

  final CreateTripProvider createTripProvider;
  final SavedRoute? newAssignData;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Consumer<CreateTripProvider>(
          builder: (context, createTripProvider, child) {
            return Column(
              children: [
                SizedBox(
                  height: context.height / 2.5,
                  width: context.width,
                  child: GoogleMap(
                    // mapType: MapType.hybrid,
                    markers: createTripProvider.markers.toSet(),
                    initialCameraPosition: createTripProvider.googleMapInitial,
                    polylines: createTripProvider.polyline.toSet(),
                    onMapCreated: (controller) =>
                        createTripProvider.onMapCreated(
                      controller,
                      newAssignData: newAssignData,
                    ),
                    onTap: (argument) {
                      argument.logFatal;
                    },
                    gestureRecognizers: {}..add(
                        const Factory<OneSequenceGestureRecognizer>(
                          EagerGestureRecognizer.new,
                        ),
                      ),
                  ),
                ),
                AbsorbPointer(
                  absorbing: createTripProvider.isEdit,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Gap(AppSize.h8),
                      if ((createTripProvider.selectedOriginStockLocation
                                      .dropDownValue !=
                                  null &&
                              createTripProvider.selectedDropStockLocation
                                      .dropDownValue !=
                                  null) &&
                          createTripProvider.customWaypoints.value.isEmpty &&
                          !createTripProvider.isEdit)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSize.w6,
                            vertical: AppSize.h2,
                          ),
                          // padding: EdgeInsets.all(AppSize.sp4),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor.withValues(alpha: .1),
                            borderRadius: BorderRadius.circular(AppSize.r12),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info,
                                color: AppColors.primaryColor,
                                size: AppSize.sp20,
                              ),
                              SizedBox(width: AppSize.w8),
                              Expanded(
                                child: Row(
                                  children: [
                                    Flexible(
                                      child: Text(
                                        context.l10n
                                            .addStopsWithoutMarkingThemAsIntermediate,
                                        maxLines: 3,
                                        style: context.textTheme.bodySmall
                                            ?.copyWith(
                                          fontSize: AppSize.sp11,
                                          color: AppColors.primaryColor,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    Gap(AppSize.w10),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: TextButton(
                                        onPressed: () async {
                                          await AppNavigationService.pushNamed(
                                            context,
                                            AppRoutes.homeSearchAddressPage,
                                            extra: SearchAddressParams(
                                              createTripProvider:
                                                  createTripProvider,
                                              title: context.l10n.addStopPoint,
                                              isCustomWaypoint: true,
                                              onTap: (address) {},
                                            ),
                                            afterBack: (value) {
                                              if (value is AddressModel) {
                                                // Create a StopLocation from the selected address
                                                final stopLocation =
                                                    StopLocation(
                                                  customWaypointId:
                                                      'custom_${value.latitude}_${value.longitude}',
                                                  name: value.address,
                                                  latitude: value.latitude,
                                                  longitude: value.longitude,
                                                  address: Address(
                                                    latitude: value.latitude,
                                                    longitude: value.longitude,
                                                    street: value.address,
                                                    city: value.city,
                                                    state: value.state,
                                                    country: value.country,
                                                    postalCode:
                                                        value.postalCode,
                                                    countryCode:
                                                        value.countryCode,
                                                  ),
                                                );

                                                '==>> stopLocation ${stopLocation.toJson()}'
                                                    .logD;

                                                // Add as custom waypoint
                                                createTripProvider
                                                    .addCustomWaypoint(
                                                  stopLocation,
                                                );
                                              }
                                            },
                                          );
                                        },
                                        style: TextButton.styleFrom(
                                          minimumSize:
                                              Size(AppSize.w100, AppSize.h30),
                                          padding: EdgeInsets.symmetric(
                                            horizontal: AppSize.w6,
                                            vertical: AppSize.h2,
                                          ),
                                          backgroundColor:
                                              AppColors.primaryColor,
                                          foregroundColor: Colors.white,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              AppSize.r8,
                                            ),
                                          ),
                                        ),
                                        child: Text(
                                          context.l10n.customise,
                                          style:
                                              TextStyle(fontSize: AppSize.sp12),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        )
                      else if ((createTripProvider.selectedOriginStockLocation
                                      .dropDownValue !=
                                  null &&
                              createTripProvider.selectedDropStockLocation
                                      .dropDownValue !=
                                  null) &&
                          createTripProvider.customWaypoints.value.isNotEmpty)
                        Theme(
                          data: Theme.of(context)
                              .copyWith(dividerColor: Colors.transparent),
                          child: ExpansionTile(
                            tilePadding: EdgeInsets.zero,
                            childrenPadding: EdgeInsets.symmetric(
                              horizontal: AppSize.w16,
                              vertical: AppSize.h1,
                            ),
                            title: Text(
                              context.l10n.customStopPoints,
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w700,
                                fontSize: AppSize.sp16,
                              ),
                            ),
                            initiallyExpanded: true,
                            trailing: createTripProvider.isEdit
                                ? null
                                : IconButton(
                                    onPressed: () async {
                                      await AppNavigationService.pushNamed(
                                        context,
                                        AppRoutes.homeSearchAddressPage,
                                        extra: SearchAddressParams(
                                          createTripProvider:
                                              createTripProvider,
                                          title: context.l10n.addStopPoint,
                                          isCustomWaypoint: true,
                                          onTap: (address) {},
                                        ),
                                        afterBack: (value) {
                                          if (value is AddressModel) {
                                            // Create a StopLocation from the selected address
                                            final stopLocation = StopLocation(
                                              customWaypointId:
                                                  'custom_${value.latitude}_${value.longitude}',
                                              name: value.address,
                                              latitude: value.latitude,
                                              longitude: value.longitude,
                                              address: Address(
                                                latitude: value.latitude,
                                                longitude: value.longitude,
                                                street: value.address,
                                                city: value.city,
                                                state: value.state,
                                                country: value.country,
                                                postalCode: value.postalCode,
                                                countryCode: value.countryCode,
                                              ),
                                            );
                                            '==>> stopLocation ${stopLocation.toJson()}'
                                                .logD;
                                            // Add as custom waypoint
                                            createTripProvider
                                                .addCustomWaypoint(
                                              stopLocation,
                                            );
                                          }
                                        },
                                      );
                                    },
                                    icon: const Icon(
                                      Icons.add,
                                      color: AppColors.primaryColor,
                                    ),
                                  ),
                            backgroundColor: AppColors.transparent,
                            collapsedBackgroundColor: AppColors.transparent,
                            children: createTripProvider.customWaypoints.value
                                .map(
                                  (e) => ListTile(
                                    contentPadding: EdgeInsets.zero,
                                    title: Text(
                                      e.name ?? '',
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                        fontWeight: FontWeight.w400,
                                        fontSize: AppSize.sp14,
                                      ),
                                    ),
                                    trailing: InkWell(
                                      onTap: () {
                                        createTripProvider
                                            .removeCustomWaypoints(
                                          e,
                                          createTripProvider
                                              .customWaypoints.value
                                              .indexOf(e),
                                        );
                                      },
                                      child: AppAssets.iconsDelete.image(
                                        height: AppSize.sp18,
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),

        Gap(AppSize.sp6),
        ValueListenableBuilder(
          valueListenable: createTripProvider.saveRoute,
          builder: (context, value, child) {
            return Row(
              children: [
                Transform.scale(
                  scale: 1.4,
                  child: Checkbox(
                    value: value,
                    onChanged: (val) {
                      if (val != null) {
                        createTripProvider.saveRoute.value = val;
                      }
                    },
                    checkColor: AppColors.white,
                    fillColor: WidgetStateProperty.all(
                      value ? AppColors.primaryColor : Colors.transparent,
                    ),
                  ),
                ),
                Text(
                  context.l10n.saveThisRoute,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            );
          },
        ),
        // if (createTripProvider.stopCities.value.isNotEmpty)
        Opacity(
          opacity: createTripProvider.isEdit ? 0.5 : 1,
          child: AbsorbPointer(
            absorbing: createTripProvider.isEdit,
            child: ValueListenableBuilder(
              valueListenable: createTripProvider.allowIntermediatePickup,
              builder: (context, value, child) {
                return Row(
                  children: [
                    Transform.scale(
                      scale: 1.4,
                      child: Checkbox.adaptive(
                        value: value,
                        onChanged: (val) {
                          if (val != null) {
                            createTripProvider.allowIntermediatePickup.value =
                                val;
                          }
                          createTripProvider.clearSelectedCity();
                        },
                        checkColor: AppColors.white,
                        fillColor: WidgetStateProperty.all(
                          value ? AppColors.primaryColor : Colors.transparent,
                        ),
                      ),
                    ),
                    Text(
                      context.l10n.allowIntermediatePickup,
                      style: context.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
        if (createTripProvider.stopCities.value.isNotEmpty) Gap(AppSize.h6),
      ],
    );
  }
}

// class MapSample extends StatefulWidget {
//   const MapSample({super.key});

//   @override
//   State<MapSample> createState() => MapSampleState();
// }

// class MapSampleState extends State<MapSample> {
//   final Completer<GoogleMapController> _controller =
//       Completer<GoogleMapController>();

//   static const CameraPosition _kGooglePlex = CameraPosition(
//     target: LatLng(37.42796133580664, -122.085749655962),
//     zoom: 14.4746,
//   );

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: GoogleMap(
//         mapType: MapType.hybrid,
//         initialCameraPosition: _kGooglePlex,
//         onMapCreated: _controller.complete,
//         gestureRecognizers: {}..add(
//             const Factory<OneSequenceGestureRecognizer>(
//               EagerGestureRecognizer.new,
//             ),
//           ),
//       ),
//     );
//   }
// }
