// ignore_for_file: public_member_api_docs

import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:math';

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/driver_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/equipment_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/enums/route_enum.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/places_api_provider_class.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/address_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/logger.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';

class CreateTripProvider extends ChangeNotifier {
  CreateTripProvider({
    this.isEdit = false,
    this.isSetSavedRoute = false,
  }) {
    WidgetsBinding.instance.addPostFrameCallback((t) async {
      if (isClosed) return;
      selectedSavedRouteDropDownController
          .addListener(chosenSavedRouteFromDropDownListener);
      if (isEdit) {
        if (tripeStartDate.value != null && tripeEndDate.value != null) {
          isShowLoader.value = true;
          await getDropDownListApiCall(
            startDate: tripeStartDate.value?.dateDropDownApiPramOnly ?? '',
            endDate: tripeEndDate.value?.dateDropDownApiPramOnly ?? '',
          );
          isShowLoader.value = false;
        }
      } else if (isSetSavedRoute) {
        if (isSetSavedRoute) {
          selectedRoute.value = RouteEnum.savedRoute;
        }
      }
      selectedStopCities.addListener(() {
        print(
          '===>>> selectedStopCities changed : ${selectedStopCities.value.length}',
        );
        for (final e in selectedStopCities.value) {
          print('===>>> selectedStopCities changed : ${e.toJson()}');
        }
      });
    });
  }
  bool isClosed = false;
  final bool isEdit;
  final bool isSetSavedRoute;
  final refreshController = RefreshController();

  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  ValueNotifier<String?> tripId = ValueNotifier(null);
  ValueNotifier<List<StopLocation>> originStockLocationList =
      ValueNotifier(<StopLocation>[]);
  ValueNotifier<List<StopLocation>> dropStockLocationList =
      ValueNotifier(<StopLocation>[]);
  ValueNotifier<List<StopLocation>> stopCities =
      ValueNotifier(<StopLocation>[]);
  ValueNotifier<List<StopLocation>> selectedStopCities =
      ValueNotifier(<StopLocation>[]);
  // ValueNotifier<StopLocation?> selectedOriginStockLocation =
  //     ValueNotifier(null);
  // ValueNotifier<StopLocation?> selectedDropStockLocation = ValueNotifier(null);
  final SingleValueDropDownController selectedOriginStockLocation =
      SingleValueDropDownController();
  final SingleValueDropDownController selectedDropStockLocation =
      SingleValueDropDownController();
  final SingleValueDropDownController selectedSavedRouteDropDownController =
      SingleValueDropDownController();

  /// Origin location controller
  final originLocationController = ValueNotifier<AddressModel?>(null);

  /// Destination location controller
  final dropLocationController = ValueNotifier<AddressModel?>(null);
  final kmPerController = TextEditingController();
  final totalTripController = TextEditingController();
  final availableSlot = TextEditingController();
  final ValueNotifier<RouteEnum> selectedRoute =
      ValueNotifier<RouteEnum>(RouteEnum.newRoute);
  // List<EquipmentDataModel> equipDataList = [];
  // EquipmentDataModel? selectedEquipment;
  final SingleValueDropDownController selectedEquipment =
      SingleValueDropDownController();
  // List<DriverData> driverDataList = [];
  // DriverData? selectedDriver;
  final SingleValueDropDownController selectedDriver =
      SingleValueDropDownController();
  ValueNotifier<bool> saveRoute = ValueNotifier(false);
  ValueNotifier<bool> allowIntermediatePickup = ValueNotifier(false);
  ValueNotifier<DateTime?> tripeStartDate = ValueNotifier(null);
  ValueNotifier<DateTime?> tripeEndDate = ValueNotifier(null);
  ValueNotifier<num> distance = ValueNotifier(0);
  ValueNotifier<List<SavedRoute>> savedRouteList =
      ValueNotifier(<SavedRoute>[]);
  CancelToken? getDriversCancelToken;
  CancelToken? getEquipmentCancelToken;

  final ValueNotifier<List<DriverDropDownModel>> driverListModelList =
      ValueNotifier([]);
  final ValueNotifier<List<EquipmentDropDownModel>> equipmentListModelList =
      ValueNotifier([]);

  // Add a new list to store custom waypoints
  final customWaypoints = ValueNotifier<List<StopLocation>>([]);

  Future<void> getDropDownListApiCall({
    required String startDate,
    required String endDate,
  }) async {
    '==>> getDropDownListApiCall $startDate $endDate'.logE;
    if (isClosed || (startDate.isEmptyOrNull && endDate.isEmptyOrNull)) return;
    isShowLoader.value = true;
    try {
      await Future.wait([
        getDriversDropDownListApiCall(
          startDate: startDate,
          endDate: endDate,
        ),
        getEquipmentDropDownListApiCall(
          startDate: startDate,
          endDate: endDate,
        ),
      ]);
      isShowLoader.value = false;
      // overlay?.hide();
    } catch (e) {
      isShowLoader.value = false;
      // overlay?.hide();
      if (isClosed) return;
      e.toString().logE;
    }
  }

  Future<void> getDriversDropDownListApiCall({
    String? startDate,
    String? endDate,
  }) async {
    if (isClosed) return;
    try {
      getDriversCancelToken?.cancel();
      getDriversCancelToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.driverDropDownData,
        cancelToken: getDriversCancelToken,
        params: {
          ApiKeys.startDate: startDate,
          ApiKeys.endDate: endDate,
        },
      );

      final res = await Injector.instance<TripRepository>()
          .getDriverDropDownData(request);

      await res.when(
        success: (data) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          driverListModelList.value.clear();
          driverListModelList.value = data;
          if (driverListModelList.value.isEmpty) {
            rootNavKey.currentContext!.l10n.noDriversAvailableOnSelectedDate
                .showErrorAlert(duration: const Duration(seconds: 3));
          }
          // notify();
        },
        error: (exception) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
      e.toString().logE;
    }
  }

  Future<void> getEquipmentDropDownListApiCall({
    String? startDate,
    String? endDate,
  }) async {
    if (isClosed) return;
    try {
      getEquipmentCancelToken?.cancel();
      getEquipmentCancelToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.equipmentDropDownData,
        cancelToken: getEquipmentCancelToken,
        params: {
          ApiKeys.startDate: startDate,
          ApiKeys.endDate: endDate,
        },
      );

      final res = await Injector.instance<TripRepository>()
          .getEquipmentDropDownData(request);

      await res.when(
        success: (data) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          equipmentListModelList.value.clear();
          equipmentListModelList.value = data;
          if (equipmentListModelList.value.isEmpty) {
            rootNavKey.currentContext!.l10n.noEquipmentAvailableOnSelectedDate
                .showErrorAlert(duration: const Duration(seconds: 2));
          }
          // notify();
        },
        error: (exception) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
      e.toString().logE;
    }
  }

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  void chosenSavedRouteFromDropDownListener() {
    if (isClosed) return;
    try {
      final index = savedRouteList.value.indexWhere((e) {
        return e.id.toString() ==
            selectedSavedRouteDropDownController.dropDownValue?.value
                .toString();
      });
      if (index == -1) return;
      assignData(
        savedRouteList.value[index],
        isFromSaved: true,
      );
    } catch (e) {
      '==>> chosenSavedRouteFromDropDownListener error $e'.logE;
      e.toString().logE;
    }
  }

  CancelToken? listNearCancelToken;
  Future<List<StopLocation>> listNearByLocation({
    required BuildContext context,
    bool isDrop = false,
    required LatLng latLng,
  }) async {
    final completer = Completer<List<StopLocation>>();
    if (isClosed) return [];
    try {
      if (!isEdit) {
        isShowLoader.value = true;
      }
      listNearCancelToken?.cancel();
      listNearCancelToken = CancelToken();

      Map<String, dynamic> data;
      data = {
        ApiKeys.latitude: latLng.latitude.toString().doubletToShort(),
        ApiKeys.longitude: latLng.longitude.toString().doubletToShort(),
      };
      final request = ApiRequest(
        path: EndPoints.listNearByLocation,
        params: data,
        cancelToken: listNearCancelToken,
      );
      final res =
          await Injector.instance<TripRepository>().listNearByLocation(request);
      if (!isEdit) {
        isShowLoader.value = false;
      }
      res.when(
        success: (data) async {
          if (isClosed || (listNearCancelToken?.isCancelled ?? true)) return;
          final dummyList = <StopLocation>[];
          for (final stock in data.stockLocation) {
            if (!dummyList.any((element) => element.name == stock.name) &&
                !dummyList.any((element) => element.id == stock.id)) {
              dev.log(jsonEncode(stock.toJson()));
              dummyList.add(stock);
            }
          }
          completer.complete(dummyList);
          if (isDrop) {
            // selectedDropStockLocation.value = null;
            selectedDropStockLocation.clearDropDown();
            dropStockLocationList.value = dummyList;
          } else {
            // selectedOriginStockLocation.value = null;
            selectedOriginStockLocation.clearDropDown();
            originStockLocationList.value = dummyList;
          }
          if (dummyList.isEmpty) {
            l10n.noStockLocationFoundPleaseChooseAnotherAddress
                .showErrorAlert(duration: const Duration(seconds: 2));
          } else {
            // AppNavigationService.pop(context);
          }
        },
        error: (exception) {
          if (!isEdit) {
            isShowLoader.value = false;
          } else {}
          completer.complete([]);
          if (isClosed || (listNearCancelToken?.isCancelled ?? true)) return [];
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (!isEdit) {
        isShowLoader.value = false;
      } else {}
      completer.complete([]);
      if (isClosed || (listNearCancelToken?.isCancelled ?? true)) return [];
      e.toString().logE;
    }
    return completer.future;
  }

  CancelToken? getSavedTripToken;
  Future<void> getSavedTripList({bool isFromRefresh = false}) async {
    if (isClosed) return;
    try {
      getSavedTripToken?.cancel();
      getSavedTripToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.getSavedTripList,
        cancelToken: getSavedTripToken,
      );
      isShowLoader.value = true;
      final res =
          await Injector.instance<TripRepository>().getSavedTripList(request);
      if (isClosed) return;
      isShowLoader.value = false;
      await res.when(
        success: (data) async {
          if (isClosed || (getSavedTripToken?.isCancelled ?? false)) {
            return;
          }
          if ((data.results?.isEmpty ?? true) && !isFromRefresh) {
            rootNavKey.currentContext!.l10n.noSavedRouteYet.showErrorAlert();
          }
          // clear the list before adding data
          savedRouteList.value.clear();
          selectedSavedRouteDropDownController.clearDropDown();
          for (final datas in data.results ?? <SavedRoute>[]) {
            if (!savedRouteList.value.any((e) => e.id == datas.id)) {
              savedRouteList.value.add(datas);
            }
          }
          savedRouteList.notifyListeners();
        },
        error: (exception) async {
          if (isClosed || (getSavedTripToken?.isCancelled ?? false)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getSavedTripToken?.isCancelled ?? false)) {
        return;
      }
      e.toString().logE;
    }
  }

  /// get stop cities
  CancelToken? listCityCancelToken;
  Future<void> listStopLocation({
    required LatLngBounds bounds,
    required String startLocation,
    required String endLocation,
  }) async {
    if (isClosed) return;
    try {
      if (!isEdit) {
        isShowLoader.value = true;
      }
      listCityCancelToken?.cancel();
      listCityCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        'lat1': bounds.southwest.latitude.toString().doubletToShort(),
        'long1': bounds.southwest.longitude.toString().doubletToShort(),
        'lat2': bounds.northeast.latitude.toString().doubletToShort(),
        'long2': bounds.northeast.longitude.toString().doubletToShort(),
        'start_location': startLocation,
        'end_location': endLocation,
      };
      final request = ApiRequest(
        path: EndPoints.listStopLocation,
        params: data,
        cancelToken: listCityCancelToken,
      );
      final res =
          await Injector.instance<TripRepository>().listStopLocation(request);
      if (!isEdit) {
        isShowLoader.value = false;
      }
      await res.when(
        success: (data) async {
          if (isClosed || (listCityCancelToken?.isCancelled ?? true)) {
            return;
          }
          final dummyList = <StopLocation>[];
          for (final stock in data.stockLocation) {
            if (!dummyList.any((element) => element.id == stock.id) &&
                !dummyList.any((element) => element.name == stock.name)) {
              dummyList.add(stock);
            }
          }

          stopCities.value = dummyList;
          notify();
        },
        error: (exception) {
          if (isClosed || (listCityCancelToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (!isEdit) {
        isShowLoader.value = false;
      } else {}
      if (isClosed || (listCityCancelToken?.isCancelled ?? true)) return;
      if (kDebugMode) e.toString().logE;
    }
  }

  Future<void> clearSelectedCity() async {
    if (isClosed) return;
    try {
      selectedStopCities.value.clear();
      polyline.clear();
      markers.removeWhere(
        (e) =>
            e.markerId.value != ApiKeys.start &&
            e.markerId.value != ApiKeys.end &&
            !e.markerId.value.startsWith('custom_'),
      );

      if (markers.length >= 2) {
        if (!isEdit) {
          isShowLoader.value = true;
        }

        try {
          // Get start and end points
          final startMarker = markers.firstWhere(
            (m) => m.markerId.value == ApiKeys.start,
            orElse: () => markers[0],
          );

          final endMarker = markers.firstWhere(
            (m) => m.markerId.value == ApiKeys.end,
            orElse: () => markers[1],
          );
          if (isClosed) return;
          final result = await PlaceApiProvider().getRouteWithWaypoints(
            origin: startMarker.position,
            destination: endMarker.position,
            waypoints: customWaypoints.value
                .map(
                  (e) => LatLng(
                    double.parse(e.latitude?.toString() ?? '0'),
                    double.parse(e.longitude?.toString() ?? '0'),
                  ),
                )
                .toList(),
          );

          if (result.routeList.isNotEmpty && !isClosed) {
            distance.value = result.totalDistance;
            polyline.add(
              Polyline(
                polylineId: const PolylineId('polyline'),
                points: result.routeList
                    .map((e) => LatLng(e.latitude, e.longitude))
                    .toList(),
                color: AppColors.ff0087C7,
              ),
            );
          }
        } catch (e) {
          dev.log('Error calculating direct route: $e');
        } finally {
          if (!isEdit && !isClosed) {
            isShowLoader.value = false;
          }
        }
      }

      if (kmPerController.text.isNotEmpty) {
        if (isClosed) return;
        totalTripController.text = ((double.tryParse(kmPerController.text) ??
                    double.parse(kmPerController.text)) *
                (distance.value / 1000))
            .toStringAsFixed(2);
      }

      notify();
    } catch (e) {
      if (!isEdit && !isClosed) {
        isShowLoader.value = false;
      }
      '==>> clearSelectedCity error $e'.logE;
      e.toString().logE;
    }
  }

  Future<void> addAndRemoveStopCitiesOrCustomWayPoints(
    StopLocation data, {
    required bool isSelected,
    bool isCustomWaypoint = false,
  }) async {
    if (isClosed) return;
    try {
      data.toJson().logD;
      isSelected.logD;
      polyline.clear();
      final lat = LatLng(
        double.parse(data.latitude ?? data.address?.latitude ?? '0'),
        double.parse(data.longitude ?? data.address?.longitude ?? '0'),
      );
      // final stopLocationsList =
      //     isCustomWaypoint ? customWaypoints.value : selectedStopCities.value;
      if (isSelected) {
        await addGoogleMapMarker(
          lat,
          isCustomWaypoint
              ? 'custom_${data.latitude}_${data.longitude}'
              : data.id?.toString() ?? '',
          isRemove: true,
          isCustomWaypoint: isCustomWaypoint,
        );

        selectedStopCities.value.removeWhere(
          (e) =>
              e.latitude?.doubletToShort() == data.latitude?.doubletToShort() &&
              e.longitude?.doubletToShort() == data.longitude?.doubletToShort(),
        );

        if (isCustomWaypoint) {
          customWaypoints.value.removeWhere(
            (e) =>
                e.latitude?.doubletToShort() ==
                    data.latitude?.doubletToShort() &&
                e.longitude?.doubletToShort() ==
                    data.longitude?.doubletToShort(),
          );
        }
      } else {
        await addGoogleMapMarker(
          lat,
          isCustomWaypoint
              ? 'custom_${data.latitude}_${data.longitude}'
              : data.id?.toString() ?? '',
          isCustomWaypoint: isCustomWaypoint,
        );
        selectedStopCities.value.add(data);
        if (isCustomWaypoint) {
          customWaypoints.value.add(data);
        }
      }
      print(
        '==BOOM : ${selectedStopCities.value.length} :: ${customWaypoints.value.length}',
      );
      // markers = sortMarkersWithStartEnd();

      // Use the optimized waypoints method
      await calculateRouteWithWaypoints(
        selectedStopCities.value,
      );
    } catch (e) {
      if (!isEdit) {
        isShowLoader.value = false;
      }
      '==>> addStopCities error $e'.logE;
      e.toString().logE;
    }
  }

  /// Calculate route using waypoints optimization
  /// This method uses a single API call to calculate the entire route with all waypoints
  Future<void> calculateRouteWithWaypoints(
    List<StopLocation> stopLocations,
  ) async {
    if (isClosed) return;
    if (markers.length < 2) return; // Need at least start and end points

    if (!isEdit) {
      isShowLoader.value = true;
    }

    try {
      // Get start and end points
      final startMarker = markers.firstWhere(
        (m) => m.markerId.value == ApiKeys.start,
        orElse: () => const Marker(markerId: MarkerId('')),
      );

      final endMarker = markers.firstWhere(
        (m) => m.markerId.value == ApiKeys.end,
        orElse: () => const Marker(markerId: MarkerId('')),
      );

      if (startMarker.markerId.value.isEmpty ||
          endMarker.markerId.value.isEmpty) {
        if (!isEdit) {
          isShowLoader.value = false;
        }
        return;
      }

      // Collect all waypoints (intermediate points)
      final waypoints = <LatLng>[];
      final waypointMarkers = markers
          .where(
            (m) =>
                m.markerId.value != ApiKeys.start &&
                m.markerId.value != ApiKeys.end,
          )
          .toList();

      for (final marker in waypointMarkers) {
        waypoints.add(marker.position);
      }

      print('==BOOM 2  : ${waypoints.length} :: ${stopLocations.length}');

      // If no waypoints, just calculate direct route
      if (waypoints.isEmpty && stopLocations.isEmpty) {
        if (isClosed) return;
        final result = await PlaceApiProvider().getRouteList(
          origin: startMarker.position,
          destination: endMarker.position,
        );

        distance.value = result.distance;
        polyline.add(
          Polyline(
            polylineId: const PolylineId('polyline'),
            points: result.routeList
                .map((e) => LatLng(e.latitude, e.longitude))
                .toList(),
            color: AppColors.ff0087C7,
          ),
        );

        selectedStopCities.value = [];
        customWaypoints.value = [];
      } else {
        if (isClosed) return;
        // Calculate route with waypoints
        final result = await PlaceApiProvider().getRouteWithWaypoints(
          origin: startMarker.position,
          destination: endMarker.position,
          waypoints: waypoints,
        );

        // Process the result
        distance.value = result.totalDistance;

        final sortedMarkers = sortMarkersByOptimizedWaypoints(
          markers: markers,
          optimizedWaypoints: result.optimizedWaypoints,
        );

        markers
          ..clear()
          ..addAll(sortedMarkers);

        final dummyTempSelectionCities = <StopLocation>[];
        // Process each leg and update stop locations with distances
        final dummyList = <StopLocation>[];
        var sortPosition = 1;
        for (var i = 0; i < (markers.length); i++) {
          final marker = markers[i];
          final stopLocation = stopLocations
              .where(
                (e) =>
                    e.latitude?.doubletToShort() ==
                        marker.position.latitude.toStringAsFixed(5) &&
                    e.longitude?.doubletToShort() ==
                        marker.position.longitude.toStringAsFixed(5),
              )
              .toList()
              .firstOrNull;
          if (stopLocation != null) {
            if (stopLocation.id != null) {
              stopLocation.stopLocationIndex = sortPosition;
              sortPosition++;
            } else {
              stopLocation.stopLocationIndex = null;
            }
            dummyList.add(stopLocation);
          }
        }

        // Add the main polyline
        polyline.add(
          Polyline(
            polylineId: const PolylineId('polyline'),
            points: result.routeList, // This is the full route polyline
            color: AppColors.ff0087C7,
          ),
        );

        // final legRoutes = result.legRoutes; // List of leg routes
        final legDistances = result.legDistances; // List of leg distances

        // Process waypoints if we have leg routes and stop locations
        if (legDistances.isNotEmpty && dummyList.isNotEmpty) {
          // We should have legRoutes.length = waypoints.length + 1
          // (one leg from start to first waypoint, then legs between waypoints, then last leg to destination)

          for (var i = 0; i < dummyList.length && i < waypoints.length; i++) {
            final stopLocation = dummyList[i];

            // Calculate distances for each leg
            if (i < legDistances.length - 1) {
              // Distance from previous point to this waypoint
              // final legDistance = calculatePolylineDistance(legRoutes[i]);
              final legDistance = legDistances[i];
              stopLocation.preDistance =
                  (legDistance / 1000).round(); // Convert to km

              // If this is the last waypoint, set the next distance (to destination)
              stopLocation.preDistance =
                  (result.legDistances[i] / 1000).round();

              stopLocation.nextDistance = (legDistances[i + 1] / 1000).round();

              if (i == stopLocations.length - 1 &&
                  i + 1 < legDistances.length) {
                stopLocation.nextDistance =
                    (legDistances[i + 1] / 1000).round();
              }
              // if (i == stopLocations.length - 1 && i + 1 < legRoutes.length) {
              //   final nextLegDistance =
              //       calculatePolylineDistance(legRoutes[i + 1]);
              //   stopLocation.nextDistance =
              //       (nextLegDistance / 1000).round(); // Convert to km
              // }
            }

            // stopLocation.stopLocationIndex =
            //     i + 1; // +1 because index 0 is the start point
            dummyTempSelectionCities.add(stopLocation);
          }
        }

        // selectedStopCities.value = dummyList2;
        // var sortPosition = 0;
        // for (var i = 0; i < (markers.length); i++) {
        //   final marker = markers[i];
        //   final stopLocation = dummyTempSelectionCities
        //       .where(
        //         (e) =>
        //             e.latitude?.doubletToShort() ==
        //                 marker.position.latitude.toStringAsFixed(5) &&
        //             e.longitude?.doubletToShort() ==
        //                 marker.position.longitude.toStringAsFixed(5),
        //       )
        //       .toList()
        //       .firstOrNull;
        //   if (stopLocation != null) {
        //     if (stopLocation.id != null) {
        //       stopLocation.stopLocationIndex = sortPosition;
        //       sortPosition++;
        //     } else {
        //       stopLocation.stopLocationIndex = null;
        //     }
        //   }
        // }
        selectedStopCities.value = dummyTempSelectionCities;
      }
    } catch (e) {
      '==>> calculateRouteWithWaypoints error $e'.logE;
      e.toString().logE;
    } finally {
      if (!isEdit && !isClosed) {
        isShowLoader.value = false;
      }

      if (kmPerController.text.isNotEmpty && !isClosed) {
        try {
          totalTripController.text = ((num.tryParse(kmPerController.text) ??
                      double.parse(kmPerController.text)) *
                  (distance.value / 1000))
              .toStringAsFixed(2);
        } catch (e) {
          '==>> calculateRouteWithWaypoints totalTripController error $e'.logE;
        }
      }
      notify();
    }
  }

  // This method will update the pre and next distance of the selected stop cities by merge the custom way point pre and next distance
  List<StopLocation> getUpdatedIntermediatePoints(
    List<StopLocation> originalList,
  ) {
    final updatedList = <StopLocation>[];

    num accumulatedPreDistance = 0;
    StopLocation? previousRealStop;

    for (var i = 0; i < originalList.length; i++) {
      final point = originalList[i];

      if (point.id == null) {
        // It's a custom point: accumulate its distance
        accumulatedPreDistance += point.preDistance ?? 0;
        continue;
      }

      // It's a real stop
      // Add the accumulated preDistance to this point's preDistance
      point.preDistance = (point.preDistance ?? 0) + accumulatedPreDistance;
      accumulatedPreDistance = 0;

      // Set the nextDistance of the previous real stop
      if (previousRealStop != null) {
        previousRealStop.nextDistance = point.preDistance ?? 0;
      }

      updatedList.add(point);
      previousRealStop = point;
    }

    // If custom points were at the end, add their distance to the last real stop
    // if (accumulatedPreDistance > 0 && previousRealStop != null) {
    //   previousRealStop.nextDistance =
    //       (previousRealStop.nextDistance ?? 0) + accumulatedPreDistance;
    // }
    num lastDistance = 0;
    for (var k = originalList.length - 1; k >= 0; k--) {
      final upperPoint = originalList[k];

      lastDistance += upperPoint.nextDistance ?? 0;

      if (upperPoint.id != null) {
        originalList[k].nextDistance =
            lastDistance == 0 ? originalList[k].nextDistance : lastDistance;
        break;
      }
    }
    return updatedList;
  }

  // WORKING
  // List<StopLocation> getUpdatedIntermediatePoints(
  //     List<StopLocation> originalList) {
  //   final updatedList = <StopLocation>[];

  //   num accumulatedPreDistance = 0;
  //   num accumulatedNextDistance = 0;

  //   for (int i = 0; i < originalList.length; i++) {
  //     final point = originalList[i];

  //     if (point.id == null) {
  //       // Custom point: accumulate distances
  //       accumulatedPreDistance += point.preDistance ?? 0;
  //       accumulatedNextDistance += point.nextDistance ?? 0;
  //     } else {
  //       // Real stop: add accumulated distances and reset
  //       point.preDistance = (point.preDistance ?? 0) + accumulatedPreDistance;
  //       point.nextDistance =
  //           (point.nextDistance ?? 0) + accumulatedNextDistance;

  //       accumulatedPreDistance = 0;
  //       accumulatedNextDistance = 0;

  //       updatedList.add(point); // Keep only real stops
  //     }
  //   }

  //   return updatedList;
  // }

  // List<StopLocation> getUpdatedIntermediatePoints(
  //   List<StopLocation> allWaypoints,
  // ) {
  //   final updatedList = <StopLocation>[...allWaypoints];
  //   // num accumulatedDistance = 0;
  //   // StopLocation? lastIntermediate;

  //   for (var i = 0; i < updatedList.length; i++) {
  //     num preDistance = 0;
  //     final upperPoint = updatedList[i];

  //     /// is not null means this is custom waypoint
  //     if (upperPoint.customWaypointId != null) continue;

  //     num nextForLoopLength = 0;
  //     for (var j = 0; j < updatedList.length; j++) {
  //       final lowerPoint = updatedList[j];
  //       if ((lowerPoint.id != upperPoint.id) && nextForLoopLength == 0) {
  //         continue;
  //       }
  //       if (lowerPoint.id != null && (lowerPoint.id != upperPoint.id)) break;
  //       nextForLoopLength++;
  //     }

  //     /// second for loop
  //     for (var j = 0; j < nextForLoopLength; j++) {
  //       final lowerPoint = updatedList[j + i];

  //       // /// both id is same means this is intermediate point
  //       // if (lowerPoint.id == upperPoint.id && lowerPoint.customWaypointId == null) continue;

  //       /// for else point sum custom point distance
  //       preDistance += lowerPoint.preDistance ?? 0;
  //     }
  //     updatedList[i].preDistance =
  //         preDistance == 0 ? updatedList[i].preDistance : preDistance;
  //   }

  //   for (var i = 0; i < updatedList.length; i++) {
  //     num lastDistance = 0;
  //     final upperPoint = updatedList[i];
  //     lastDistance += upperPoint.nextDistance ?? 0;
  //     if (upperPoint.id != null) {
  //       updatedList[i].nextDistance =
  //           lastDistance == 0 ? updatedList[i].nextDistance : lastDistance;
  //     }
  //     if (upperPoint.id != null) break;
  //   }

  //   // for (final point in updatedList) {
  //   //   final isCustom = point.customWaypointId != null;

  //   //   if (!isCustom) {
  //   //     // This is an intermediate point
  //   //     if (lastIntermediate == null) {
  //   //       // First intermediate point
  //   //       point.preDistance = point.preDistance ?? 0;
  //   //     } else {
  //   //       // Update previous intermediate's nextDistance
  //   //       lastIntermediate.nextDistance = accumulatedDistance;
  //   //       point.preDistance = accumulatedDistance;
  //   //     }

  //   //     accumulatedDistance = 0;
  //   //     updatedList.add(point);
  //   //     lastIntermediate = point;
  //   //   } else {
  //   //     // This is a custom point, accumulate distances
  //   //     accumulatedDistance +=
  //   //         (point.preDistance ?? 0) + (point.nextDistance ?? 0);
  //   //   }
  //   // }

  //   // // Update nextDistance of last intermediate
  //   // if (lastIntermediate != null) {
  //   //   lastIntermediate.nextDistance = accumulatedDistance;
  //   // }

  //   return updatedList;
  // }

  /// Calculate the distance of a polyline by summing the distances between consecutive points
  double calculatePolylineDistance(List<LatLng> polyline) {
    if (isClosed) return 0;
    try {
      double distance = 0;

      for (var i = 0; i < polyline.length - 1; i++) {
        distance += calculateDistance(polyline[i], polyline[i + 1]) *
            1000; // Convert km to meters
      }

      return distance;
    } catch (e) {
      '==>> calculatePolylineDistance error $e'.logE;
      return 0;
    }
  }

  /// Clear intermediate points and custom way points
  Future<void> clearIntermediatePoints() async {
    if (isClosed) return;
    try {
      // Remove custom waypoint markers
      markers.removeWhere(
        (e) =>
            e.markerId.value != ApiKeys.start &&
            e.markerId.value != ApiKeys.end,
      );
      // customWaypoints.value.clear();
      stopCities.value.clear();
      selectedStopCities.value.clear();

      // Recalculate route
      await calculateRouteWithWaypoints(
        selectedStopCities.value,
      );
      notify();
    } catch (e) {
      '==>> clearIntermediatePoints error $e'.logE;
      e.toString().logE;
    }
  }

  /// Process all stop cities at once using a single API call
  /// This is more efficient than calling addStopCities for each point
  Future<void> addAllStopCities(List<StopLocation> stopLocations) async {
    if (isClosed) return;
    if (stopLocations.isEmpty && customWaypoints.value.isEmpty) return;

    try {
      polyline.clear();

      // Add all markers first
      for (final location in stopLocations) {
        final lat = LatLng(
          double.parse(location.latitude ?? location.address?.latitude ?? '0'),
          double.parse(
            location.longitude ?? location.address?.longitude ?? '0',
          ),
        );
        await addGoogleMapMarker(lat, location.id?.toString() ?? '');
      }

      // // Sort markers to ensure proper order
      // markers = sortMarkersWithStartEnd();

      // Now calculate the route with all waypoints at once
      if (!isEdit) {
        isShowLoader.value = true;
      }

      try {
        // Get start and end points
        final startMarker = markers.firstWhere(
          (m) => m.markerId.value == ApiKeys.start,
          orElse: () => const Marker(markerId: MarkerId('')),
        );

        final endMarker = markers.firstWhere(
          (m) => m.markerId.value == ApiKeys.end,
          orElse: () => const Marker(markerId: MarkerId('')),
        );

        if (startMarker.markerId.value.isEmpty ||
            endMarker.markerId.value.isEmpty) {
          if (!isEdit) {
            isShowLoader.value = false;
          }
          return;
        }

        // Collect all waypoints (intermediate points)
        final waypoints = <LatLng>[];
        final waypointMarkers = markers
            .where(
              (m) =>
                  m.markerId.value != ApiKeys.start &&
                  m.markerId.value != ApiKeys.end,
            )
            .toList();

        for (final marker in waypointMarkers) {
          waypoints.add(marker.position);
        }

        // Calculate route with all waypoints
        final result = await PlaceApiProvider().getRouteWithWaypoints(
          origin: startMarker.position,
          destination: endMarker.position,
          waypoints: waypoints,
        );

        // Process the result
        distance.value = result.totalDistance;

        final sortedMarkers = sortMarkersByOptimizedWaypoints(
          markers: markers,
          optimizedWaypoints: result.optimizedWaypoints,
        );

        markers
          ..clear()
          ..addAll(sortedMarkers);

        final dummyTempSelectionCities = <StopLocation>[];
        // Process each leg and update stop locations with distances
        final dummyList = <StopLocation>[];
        var sortPosition = 1;
        for (var i = 0; i < (markers.length); i++) {
          final marker = markers[i];
          final stopLocation = stopLocations
              .where(
                (e) =>
                    e.latitude?.doubletToShort() ==
                        marker.position.latitude.toStringAsFixed(5) &&
                    e.longitude?.doubletToShort() ==
                        marker.position.longitude.toStringAsFixed(5),
              )
              .toList()
              .firstOrNull;
          if (stopLocation != null) {
            if (stopLocation.id != null) {
              stopLocation.stopLocationIndex = sortPosition;
              sortPosition++;
            } else {
              stopLocation.stopLocationIndex = null;
            }
            dummyList.add(stopLocation);
          }
        }

        // Add the main polyline
        polyline.add(
          Polyline(
            polylineId: const PolylineId('polyline'),
            points: result.routeList,
            color: AppColors.ff0087C7,
          ),
        );

        // final legRoutes = result.legRoutes;
        final legDistances = result.legDistances;

        // Map waypoint markers to stop locations
        final markerToStopLocation = <String, StopLocation>{};
        for (final location in stopLocations) {
          markerToStopLocation[location.id.toString()] = location;
        }

        // Process waypoints if we have leg routes and stop locations
        if (legDistances.isNotEmpty && dummyList.isNotEmpty) {
          // We should have legRoutes.length = waypoints.length + 1
          // (one leg from start to first waypoint, then legs between waypoints, then last leg to destination)

          for (var i = 0;
              i < waypointMarkers.length && i < waypoints.length;
              i++) {
            final marker = waypointMarkers[i];
            final stopLocation = markerToStopLocation[marker.markerId.value];

            // Calculate distances for each leg
            if (i < legDistances.length - 1) {
              // Distance from previous point to this waypoint
              // final legDistance = calculatePolylineDistance(legRoutes[i]);
              final legDistance = legDistances[i];
              stopLocation?.preDistance =
                  (legDistance / 1000).round(); // Convert to km

              // If this is the last waypoint, set the next distance (to destination)
              stopLocation?.preDistance =
                  (result.legDistances[i] / 1000).round();

              stopLocation?.nextDistance = (legDistances[i + 1] / 1000).round();

              if (i == waypointMarkers.length - 1 &&
                  i + 1 < legDistances.length) {
                stopLocation?.nextDistance =
                    (legDistances[i + 1] / 1000).round();
              }
              // if (i == stopLocations.length - 1 && i + 1 < legRoutes.length) {
              //   final nextLegDistance =
              //       calculatePolylineDistance(legRoutes[i + 1]);
              //   stopLocation.nextDistance =
              //       (nextLegDistance / 1000).round(); // Convert to km
              // }
            }

            // stopLocation.stopLocationIndex =
            //     i + 1; // +1 because index 0 is the start point
            if (stopLocation != null) {
              dummyTempSelectionCities.add(stopLocation);
            }
          }
        }

        // // Process waypoints if we have leg routes
        // if (legDistances.isNotEmpty) {
        //   // We should have legRoutes.length = waypoints.length + 1

        //   for (var i = 0;
        //       i < waypointMarkers.length && i < waypoints.length;
        //       i++) {
        //     final marker = waypointMarkers[i];
        //     final stopLocation = markerToStopLocation[marker.markerId.value];

        //     if (stopLocation != null) {
        //       // Calculate distances for each leg
        //       if (i < legDistances.length - 1) {
        //         // Distance from previous point to this waypoint
        //         // final legDistance = calculatePolylineDistance(legRoutes[i]);
        //         final legDistance = legDistances[i];
        //         legDistance.logE;
        //         stopLocation.preDistance =
        //             (legDistance / 1000).round(); // Convert to km

        //         // If this is the last waypoint, set the next distance (to destination)
        //         // if (i == waypointMarkers.length - 1 &&
        //         //     i + 1 < legRoutes.length) {
        //         //   final nextLegDistance =
        //         //       calculatePolylineDistance(legRoutes[i + 1]);
        //         //   stopLocation.nextDistance =
        //         //       (nextLegDistance / 1000).round(); // Convert to km
        //         // }

        //         if (i == waypointMarkers.length - 1 &&
        //             i + 1 < result.legDistances.length) {
        //           stopLocation.nextDistance =
        //               (legDistances[i + 1] / 1000).round();
        //         }
        //       }

        //       stopLocation.stopLocationIndex =
        //           i + 1; // +1 because index 0 is the start point
        //       dummyList.add(stopLocation);
        //     }
        //   }
        // }

        // selectedStopCities.value = dummyList;
        // final dummyTempSelectionCities = <StopLocation>[];
        // for (var i = 0; i < (markers.length); i++) {
        //   final marker = markers[i];
        //   final stopLocation = dummyList
        //       .where(
        //         (e) =>
        //             e.latitude?.doubletToShort() ==
        //                 marker.position.latitude.toStringAsFixed(5) &&
        //             e.longitude?.doubletToShort() ==
        //                 marker.position.longitude.toStringAsFixed(5),
        //       )
        //       .toList()
        //       .firstOrNull;
        //   if (stopLocation != null) {
        //     stopLocation.stopLocationIndex = i;
        //     dummyTempSelectionCities.add(stopLocation);
        //   }
        // }
        selectedStopCities.value = dummyTempSelectionCities;
      } catch (e) {
        '==>> addAllStopCities inner error $e'.logE;
        e.toString().logE;
      } finally {
        if (!isEdit) {
          isShowLoader.value = false;
        }

        if (kmPerController.text.isNotEmpty) {
          try {
            totalTripController.text = ((num.tryParse(kmPerController.text) ??
                        double.parse(kmPerController.text)) *
                    (distance.value / 1000))
                .toStringAsFixed(2);
          } catch (e) {
            '==>> addAllStopCities totalTripController error $e'.logE;
          }
        }

        notify();
      }
    } catch (e) {
      // if (!isEdit) {
      isShowLoader.value = false;
      // }
      '==>> addAllStopCities error $e'.logE;
      e.toString().logE;
    }
  }

  /// create routes
  CancelToken? createRouteCancelToken;
  Future<void> createUpdateRoute(
    BuildContext context,
  ) async {
    if (isClosed) return;
    try {
      if (originLocationController.value != null &&
          originLocationController.value!.address.isEmptyOrNull &&
          dropLocationController.value != null &&
          dropLocationController.value!.address.isEmptyOrNull &&
          selectedOriginStockLocation.dropDownValue == null &&
          selectedDropStockLocation.dropDownValue == null) {
        context.l10n.pleaseChooseOriginDropLocation.showErrorAlert();
      } else if (selectedOriginStockLocation.dropDownValue == null) {
        context.l10n.chooseOriginStockLocation.showErrorAlert();
      } else if (selectedDropStockLocation.dropDownValue == null) {
        context.l10n.chooseDropStockLocation.showErrorAlert();
      } else if (selectedEquipment.dropDownValue == null) {
        context.l10n.selectEquipment.showErrorAlert();
      } else if (availableSlot.text.isEmpty) {
        context.l10n.pleaseEnterAvailableSlot.showErrorAlert();
      } else if (int.parse(availableSlot.text) >
          ((selectedEquipment.dropDownValue?.value as EquipmentDropDownModel?)
                  ?.slot ??
              0)) {
        context.l10n
            .pleaseEnterValidSlot(
              (selectedEquipment.dropDownValue?.value
                          as EquipmentDropDownModel?)
                      ?.slot ??
                  0,
            )
            .showErrorAlert();
      } else if (selectedDriver.dropDownValue == null) {
        context.l10n.selectDriver.showErrorAlert();
      } else if (tripeStartDate.value == null) {
        context.l10n.pleaseEnterTripStartDate.showErrorAlert();
      } else if (tripeEndDate.value == null) {
        context.l10n.pleaseEnterTripEndDate.showErrorAlert();
      } else if (allowIntermediatePickup.value &&
          selectedStopCities.value.isEmpty) {
        context.l10n.pleaseSelectAtLeastLocation.showErrorAlert();
      } else if (selectedStopCities.value
          .any((e) => e.id != null && e.date == null)) {
        '${context.l10n.pleaseEnterEstimatedDateFor} ${selectedStopCities.value.firstWhere((e) => e.id != null && e.date == null).name}'
            .showErrorAlert();
      } else if (selectedStopCities.value
          .any((e) => e.id != null && e.time == null)) {
        '${context.l10n.pleaseEnterEstimatedTimeFor} ${selectedStopCities.value.firstWhere((e) => e.id != null && e.time == null).name}'
            .showErrorAlert();
      } else if (kmPerController.text.isEmpty) {
        context.l10n.pleaseEnterKmCharge.showErrorAlert();
      } else if (totalTripController.text.isEmpty) {
        context.l10n.pleaseEnterTotalCost.showErrorAlert();
      } else {
        createRouteCancelToken?.cancel();
        createRouteCancelToken = CancelToken();

        //Merge the custom way point pre and next distance with selected stop cities
        /// This list will only contain selected stop city with updated pre and next distance
        var finalSelectedCitiesList = <StopLocation>[];

        if (customWaypoints.value.isNotEmpty) {
          finalSelectedCitiesList = getUpdatedIntermediatePoints(
            selectedStopCities.value,
          );

          for (var i = 0; i < finalSelectedCitiesList.length; i++) {
            //updated next distance
            if (i == finalSelectedCitiesList.length - 1) {
              //Do not update next distance for last point
            } else {
              finalSelectedCitiesList[i].nextDistance =
                  finalSelectedCitiesList[i + 1].preDistance;
            }
          }
        }

        if (finalSelectedCitiesList.isNotEmpty) {
          selectedStopCities.value
            ..clear()
            ..addAll(finalSelectedCitiesList);
        }

        double originalTotalDistance = 0;
        if (selectedStopCities.value.isNotEmpty) {
          for (final i in selectedStopCities.value) {
            originalTotalDistance += i.preDistance ?? 0;
          }
          originalTotalDistance +=
              selectedStopCities.value.last.nextDistance ?? 0;
        } else {
          originalTotalDistance = distance.value.toDouble() / 1000;
        }

        Map<String, dynamic> data;
        data = {
          ApiKeys.equipment: (selectedEquipment.dropDownValue?.value
                  as EquipmentDropDownModel?)
              ?.id,
          ApiKeys.startStopLocation: (selectedOriginStockLocation
                  .dropDownValue!.value as StopLocation?)
              ?.id,
          ApiKeys.endStopLocation:
              (selectedDropStockLocation.dropDownValue!.value as StopLocation?)
                  ?.id,
          ApiKeys.driver:
              (selectedDriver.dropDownValue?.value as DriverDropDownModel?)?.id,
          ApiKeys.spotAvail: availableSlot.text,
          ApiKeys.tripStartDate:
              tripeStartDate.value?.toUtc().toIso8601String(),
          ApiKeys.tripEndDate: tripeEndDate.value?.toUtc().toIso8601String(),
          ApiKeys.allowIntermediate: selectedStopCities.value.isNotEmpty,
          // ? allowIntermediatePickup.value
          // : false,
          ApiKeys.costPerKm: kmPerController.text,
          ApiKeys.totalTripDistance: originalTotalDistance.round(),
          ApiKeys.saveRoute: saveRoute.value,
          ApiKeys.customPoints: customWaypoints.value.map(
            (e) {
              final mapData = {
                ApiKeys.latitude: e.latitude?.doubletToShort(),
                ApiKeys.longitude: e.longitude?.doubletToShort(),
              };
              if (e.address != null && e.address!.street.isNotEmptyAndNotNull) {
                mapData[ApiKeys.street] = e.address!.street;
              }
              if (e.address != null &&
                  e.address!.postalCode.isNotEmptyAndNotNull) {
                mapData[ApiKeys.postalCode] = e.address!.postalCode;
              }
              return mapData;
            },
          ).toList(),
          if (selectedStopCities.value.isNotEmpty)
            ApiKeys.lastSegmentDistance:
                selectedStopCities.value.last.nextDistance,
          if (allowIntermediatePickup.value)
            ApiKeys.intermediatePoint: selectedStopCities.value
                .map(
                  (e) => {
                    ApiKeys.stopLocation: e.id,
                    ApiKeys.estimatedArrival:
                        e.date?.setTimeOfDay(e.time!).toUtc().toIso8601String(),
                    ApiKeys.stopLocationIndex: e.stopLocationIndex,
                    ApiKeys.distance: e.preDistance,
                  },
                )
                .toList(),
        }..logFatal;
        data.logD;
        // if (!isEdit) {
        isShowLoader.value = true;
        // }
        final request = ApiRequest(
          path: tripId.value != null
              ? EndPoints.updateTrip(tripId.value!)
              : EndPoints.createRoute,
          cancelToken: getDriversCancelToken,
          data: data,
        );
        final res = tripId.value != null
            ? await Injector.instance<TripRepository>().updateRoute(request)
            : await Injector.instance<TripRepository>().createRoute(request);
        // if (!isEdit) {
        isShowLoader.value = false;
        // }
        res.when(
          success: (data) {
            if (isClosed || (createRouteCancelToken?.isCancelled ?? true)) {
              return;
            }
            (tripId.value != null
                    ? context.l10n.tripUpdatedSuccess
                    : context.l10n.tripCreatedSuccess)
                .showSuccessAlert();
            AppNavigationService.pop(context, true);
          },
          error: (exception) {
            if (isClosed || (createRouteCancelToken?.isCancelled ?? true)) {
              return;
            }
            exception.message.showErrorAlert();
          },
        );
      }
    } catch (e) {
      // if (!isEdit) {
      isShowLoader.value = false;
      // }
      '==>> createUpdateRoute error $e'.logE;
      e.toString().logE;
    }
  }

  /// assign data
  Future<void> assignData(SavedRoute data, {bool isFromSaved = false}) async {
    if (isClosed) return;
    try {
      // if (!isEdit) {
      isShowLoader.value = true;
      // }
      clearData(isFromSaved: isFromSaved);
      if (isClosed) return;
      if (!isFromSaved) tripId.value = data.id?.toString();
      allowIntermediatePickup.value =
          (data.intermediatePickUpPoint?.length ?? 0) > 2;

      /// add intermediate cities
      for (final intermediatePoint
          in (data.intermediatePickUpPoint ?? <IntermediatePickUpPoint>[])) {
        if (isClosed) return;

        if (intermediatePoint.stopLocation != null) {
          if (data.startStopLocation?.id !=
                  intermediatePoint.stopLocation?.id &&
              data.endStopLocation?.id != intermediatePoint.stopLocation?.id) {
            stopCities.value.add(intermediatePoint.stopLocation!);
          }
        }
      }

      if (data.startStopLocation != null) {
        if (isClosed) return;
        originStockLocationList.value.add(data.startStopLocation!);
        selectedOriginStockLocation.setDropDown(
          DropDownValueModel(
            value: data.startStopLocation,
            name: data.startStopLocation?.name ?? '',
          ),
        );
        if (isClosed) return;
        final startLng = LatLng(
          double.parse(data.startStopLocation?.address?.latitude ?? '0'),
          double.parse(data.startStopLocation?.address?.longitude ?? '0'),
        );
        await addGoogleMapMarker(startLng, ApiKeys.start);
        await fitBounds(
          startLng,
          isFromSaved: isFromSaved,
        );
        if (data.endStopLocation != null) {
          if (isClosed) return;
          dropStockLocationList.value.add(data.endStopLocation!);
          selectedDropStockLocation.setDropDown(
            DropDownValueModel(
              value: data.endStopLocation,
              name: data.endStopLocation?.name ?? '',
            ),
          );
          final endLng = LatLng(
            double.parse(data.endStopLocation?.address?.latitude ?? '0'),
            double.parse(data.endStopLocation?.address?.longitude ?? '0'),
          );
          await addGoogleMapMarker(endLng, ApiKeys.end);
          await fitBounds(
            endLng,
            isFromSaved: isFromSaved,
          );
        }
      }

      if (data.equipment != null &&
          equipmentListModelList.value.any((e) => e.id == data.equipment?.id)) {
        if (isClosed) return;
        selectedEquipment.setDropDown(
          DropDownValueModel(
            name: data.equipment?.name ?? '',
            value: data.equipment,
          ),
        );
      }
      if (data.driver != null &&
          driverListModelList.value.any((e) => e.id == data.driver?.id)) {
        if (isClosed) return;
        // selectedDriver = data.driver;
        selectedDriver.setDropDown(
          DropDownValueModel(
            name: '${data.driver?.firstName} ${data.driver?.lastName}',
            value: data.driver,
          ),
        );
      }
      if (isClosed) return;

      // availableSlot.text = data.spotAvailableForReservation?.toString() ?? '';
      if (isClosed) return;
      kmPerController.text =
          data.costPerKilometer?.toDouble().toStringAsFixed(2) ?? '0';
      if (isEdit) {
        tripeStartDate.value = data.tripStartDate;
        tripeEndDate.value = data.tripEndDate;
      }
      try {
        if (isClosed) return;
        totalTripController.text =
            (double.parse(kmPerController.text) * (distance.value / 1000))
                .toStringAsFixed(2);
      } catch (e) {
        '==>> assignData totalTripController error $e'.logE;
        totalTripController.text = '0';
      }

      if (data.intermediatePickUpPoint != null) {
        if (isClosed) return;
        final intermediatePoints = <StopLocation>[];

        // Collect all intermediate points first
        for (final city
            in data.intermediatePickUpPoint ?? <IntermediatePickUpPoint>[]) {
          if (city.stopLocation?.id != data.startStopLocation?.id &&
              city.stopLocation?.id != data.endStopLocation?.id) {
            dev.log('===>>>>> here cities ${city.toJson()}');

            intermediatePoints.add(
              StopLocation(
                id: city.stopLocation?.id,
                latitude: city.stopLocation?.address?.latitude ??
                    city.stopLocation?.latitude,
                longitude: city.stopLocation?.address?.longitude ??
                    city.stopLocation?.longitude,
                name: city.stopLocation?.name,
                date: city.estimatedArrivalDate,
                time: TimeOfDay.fromDateTime(
                  city.estimatedArrivalDate ?? DateTime.now(),
                ),
                stopLocationIndex: city.stopLocationIndex,
                preDistance: city.distance,
              ),
            );
          }
        }

        // Process all intermediate points at once if there are any
        if (intermediatePoints.isNotEmpty) {
          await addAllStopCities(intermediatePoints);
        }
      }
      // kmPerController.text = data.?.toString() ?? '';
      notify();
    } catch (e) {
      '==>> assignData error $e'.logE;
      // e.toString().showErrorAlert();
    } finally {
      if (
          // !isEdit &&
          !isClosed) {
        isShowLoader.value = false;
      }
    }
  }

  void clearData({bool isFromSaved = false}) {
    if (isClosed) return;
    try {
      // Clear map-related data
      markers.clear();
      polyline.clear();

      // Clear location data
      originStockLocationList.value.clear();
      tripId.value = null;
      dropStockLocationList.value.clear();
      stopCities.value.clear();
      selectedStopCities.value.clear();
      selectedOriginStockLocation.clearDropDown();
      selectedDropStockLocation.clearDropDown();

      //* This should not be cleared if it is from saved route
      if (!isFromSaved) {
        selectedSavedRouteDropDownController.clearDropDown();
      }

      // Clear other form data
      selectedEquipment.clearDropDown();
      selectedDriver.clearDropDown();
      saveRoute.value = false;
      tripeStartDate.value = null;
      tripeEndDate.value = null;
      distance.value = 0;
      originLocationController.value = null;
      dropLocationController.value = null;
      kmPerController.clear();
      totalTripController.clear();
      availableSlot.clear();

      notify();
    } catch (e) {
      '==>> clearData error $e'.logE;
      e.toString().logE;
    }
  }

  /// Google map functions
  CameraPosition googleMapInitial = const CameraPosition(
    target: LatLng(37.42796133580664, -122.085749655962),
    zoom: 14.4746,
  );
  late GoogleMapController googleMapController;
  List<Marker> markers = <Marker>[];
  List<Polyline> polyline = <Polyline>[];

  /// to add google map marker
  Future<void> addGoogleMapMarker(
    LatLng latLng,
    String id, {
    bool isRemove = false,
    bool isCustomWaypoint = false,
  }) async {
    if (isClosed) return;
    try {
      if (isRemove) {
        markers.removeWhere((element) => element.markerId.value == id);
      } else {
        markers
          ..removeWhere((element) => element.markerId.value == id)
          ..add(
            Marker(
              markerId: MarkerId(id),
              position: latLng,
              icon: isCustomWaypoint
                  ? BitmapDescriptor.defaultMarkerWithHue(
                      BitmapDescriptor.hueViolet,
                    )
                  : BitmapDescriptor.defaultMarker,
            ),
          );
      }
      notify();
    } catch (e) {
      '==>> addGoogleMapMarker error $e'.logE;
      e.toString().logE;
    }
  }

  Future<void> fitBounds(
    LatLng lat, {
    bool isFromSaved = false,
  }) async {
    if (isClosed) return;
    try {
      if (markers.length < 2 && !isFromSaved) {
        await googleMapController
            .moveCamera(CameraUpdate.newLatLngZoom(lat, 10));
        return;
      }

      final bounds = PlaceApiProvider().getLatLngBounds(
        markers.map((e) => e.position).toList(),
      );
      distance.value =
          calculateDistance(markers.first.position, markers.last.position);
      // if (!isFromSaved) {
      await googleMapController
          .animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
      // }
      await clearSelectedCity();
      LatLng? maxLatLng;
      LatLng? minLat;
      for (final poly in polyline) {
        final list = [...poly.points];
        final list2 = [...poly.points];
        list.sort((a, b) => a.latitude.compareTo(b.latitude));
        list2.sort((a, b) => a.longitude.compareTo(b.longitude));
        minLat = LatLng(list.first.latitude, list2.first.longitude);
        maxLatLng = LatLng(list.last.latitude, list2.last.longitude);
      }

      if (minLat == null ||
          maxLatLng == null ||
          (allowIntermediatePickup.value)) {
        return;
      }
      final newBounds = PlaceApiProvider().getNewBound(
        minLatLng: minLat,
        maxLatLng: maxLatLng,
      );

      await listStopLocation(
        bounds: newBounds,
        startLocation:
            (selectedOriginStockLocation.dropDownValue?.value as StopLocation?)
                    ?.id
                    ?.toString() ??
                '',
        endLocation:
            (selectedDropStockLocation.dropDownValue?.value as StopLocation?)
                    ?.id
                    ?.toString() ??
                '',
      );
    } catch (e) {
      // if (!isEdit) {
      isShowLoader.value = false;
      // } else {}
      '==>> fitBounds error $e'.logE;
      e.toString().logE;
    }
  }

  /// to calculate distance from one lat to another
  double calculateDistance(LatLng p1, LatLng p2) {
    if (isClosed) return 0;
    try {
      const R = 6371; // Radius of Earth in km
      final dLat = (p2.latitude - p1.latitude) * pi / 180;
      final dLon = (p2.longitude - p1.longitude) * pi / 180;
      final a = sin(dLat / 2) * sin(dLat / 2) +
          cos(p1.latitude * pi / 180) *
              cos(p2.latitude * pi / 180) *
              sin(dLon / 2) *
              sin(dLon / 2);
      final c = 2 * atan2(sqrt(a), sqrt(1 - a));
      return R * c;
    } catch (e) {
      '==>> calculateDistance error $e'.logE;
      return 0;
    }
  }

  List<Marker> sortMarkersByOptimizedWaypoints({
    required List<Marker> markers,
    required List<LatLng> optimizedWaypoints,
  }) {
    try {
      final startMarker = markers.firstWhere(
        (m) => m.markerId.value == ApiKeys.start,
        orElse: () => const Marker(markerId: MarkerId('')),
      );

      final endMarker = markers.firstWhere(
        (m) => m.markerId.value == ApiKeys.end,
        orElse: () => const Marker(markerId: MarkerId('')),
      );

      final waypointMarkers = markers.where((m) {
        final id = m.markerId.value;
        return id != ApiKeys.start && id != ApiKeys.end;
      }).toList();

      // Match optimized waypoint positions to the correct Marker
      final sortedWaypointMarkers = <Marker>[];
      for (final latLng in optimizedWaypoints) {
        final match = waypointMarkers.firstWhere(
          (m) =>
              m.position.latitude.toStringAsFixed(5) ==
                  latLng.latitude.toStringAsFixed(5) &&
              m.position.longitude.toStringAsFixed(5) ==
                  latLng.longitude.toStringAsFixed(5),
          orElse: () => const Marker(markerId: MarkerId('')),
        );
        if (match.markerId.value.isNotEmpty) {
          sortedWaypointMarkers.add(match);
        }
      }

      return [
        startMarker,
        ...sortedWaypointMarkers,
        endMarker,
      ];
    } catch (e) {
      '==>> sortMarkersByOptimizedWaypoints error $e'.logE;
      return [];
    }
  }

  // List<Marker> sortMarkersWithStartEnd() {
  //   if (isClosed || markers.isEmpty) return [];

  //   (markers).logD;

  //   try {
  //     final startMarker = markers.firstWhere(
  //       (m) => m.markerId.value == ApiKeys.start,
  //       orElse: () => const Marker(markerId: MarkerId('')),
  //     );
  //     final endMarker = markers.firstWhere(
  //       (m) => m.markerId.value == ApiKeys.end,
  //       orElse: () => const Marker(markerId: MarkerId('')),
  //     );
  //     if (startMarker.markerId.value.isEmpty ||
  //         endMarker.markerId.value.isEmpty) {
  //       return [];
  //     }
  //     final remaining = List<Marker>.from(markers)
  //       ..remove(startMarker)
  //       ..remove(endMarker);

  //     final sortedList = <Marker>[startMarker];

  //     (sortedList).logD;

  //     (remaining).logD;

  //     while (remaining.isNotEmpty) {
  //       final last = sortedList.last;

  //       final nearest = remaining.reduce((a, b) {
  //         final distA = calculateDistance(last.position, a.position);
  //         final distB = calculateDistance(last.position, b.position);
  //         return distA < distB ? a : b;
  //       });

  //       sortedList.add(nearest);
  //       remaining.remove(nearest);
  //     }

  //     sortedList.add(endMarker);
  //     return sortedList;
  //   } catch (e) {
  //     '==>> sortMarkersWithStartEnd error $e'.logE;
  //     return [];
  //   }
  // }

  void onMapCreated(
    GoogleMapController controller, {
    SavedRoute? newAssignData,
  }) {
    if (isClosed) return;
    try {
      googleMapController = controller;
      if (newAssignData != null) assignData(newAssignData);
    } catch (e) {
      '==>> onMapCreated error $e'.logE;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    clearData();
    getSavedTripToken?.cancel();
    listNearCancelToken?.cancel();
    getEquipmentCancelToken?.cancel();
    getDriversCancelToken?.cancel();
    listCityCancelToken?.cancel();
    createRouteCancelToken?.cancel();
    isShowLoader.dispose();
    refreshController.dispose();
    savedRouteList.value.clear();
    savedRouteList.dispose();
    tripId.dispose();
    originStockLocationList.dispose();
    dropStockLocationList.dispose();
    stopCities.dispose();
    selectedStopCities.dispose();
    selectedOriginStockLocation.dispose();
    selectedDropStockLocation.dispose();
    selectedSavedRouteDropDownController
        .removeListener(chosenSavedRouteFromDropDownListener);
    selectedSavedRouteDropDownController.dispose();
    selectedEquipment.dispose();
    selectedDriver.dispose();
    saveRoute.dispose();
    allowIntermediatePickup.dispose();
    tripeStartDate.dispose();
    tripeEndDate.dispose();
    distance.dispose();
    originLocationController.dispose();
    dropLocationController.dispose();
    kmPerController.dispose();
    totalTripController.dispose();
    availableSlot.dispose();
    selectedRoute.dispose();
    markers.clear();
    polyline.clear();
    googleMapController.dispose();
    driverListModelList.dispose();
    equipmentListModelList.dispose();
    super.dispose();
  }

  // Add a method to add custom waypoint
  Future<void> addCustomWaypoint(StopLocation location) async {
    if (isClosed) return;
    try {
      if (customWaypoints.value.any(
        (e) =>
            e.latitude == location.latitude &&
            e.longitude == location.longitude,
      )) {
        rootNavKey.currentContext!.l10n.thisLocationIsAlreadyAddedAsStop
            .showErrorAlert();
        return;
      }
      // customWaypoints.value = [...customWaypoints.value, location];
      // final lat = LatLng(
      //   double.parse(location.latitude ?? location.address?.latitude ?? '0'),
      //   double.parse(location.longitude ?? location.address?.longitude ?? '0'),
      // );

      await addAndRemoveStopCitiesOrCustomWayPoints(
        location,
        isSelected: false,
        isCustomWaypoint: true,
      );
      for (final e in markers) {
        '${e.position}'.logD;
      }
      for (final e in selectedStopCities.value) {
        '${e.latitude} : ${e.longitude}'.logD;
      }

      // // Add marker with grey color for custom waypoint
      // await addGoogleMapMarker(
      //   lat,
      //   'custom_${location.latitude}',
      //   isCustomWaypoint: true,
      // );
      // print(
      //   '======>>>addCustomWaypoint :: ${customWaypoints.value.length} :: ${markers.length} :: ${location.address?.toJson()}',
      // );

      // // Recalculate route with all waypoints
      // await calculateRouteWithAllWaypoints();

      // notify();
    } catch (e) {
      '==>> addCustomWaypoint error $e'.logE;
      e.toString().logE;
    }
  }

  // Clear All custom waypoints
  Future<void> clearCustomWaypoints() async {
    if (isClosed) return;
    try {
      // Remove custom waypoint markers
      markers.removeWhere((e) => e.markerId.value.startsWith('custom_'));
      customWaypoints.value = [];

      // Recalculate route
      await calculateRouteWithWaypoints(
        selectedStopCities.value,
      );

      notify();
    } catch (e) {
      '==>> clearCustomWaypoints error $e'.logE;
      e.toString().logE;
    }
  }

  // Clear single custom waypoint
  Future<void> removeCustomWaypoints(StopLocation location, int index) async {
    if (isClosed) return;
    try {
      // // Remove custom waypoint markers
      // markers.removeWhere(
      //   (e) => e.markerId.value == 'custom_${location.latitude}',
      // );
      // customWaypoints.value.removeWhere(
      //   (e) =>
      //       e.latitude == location.latitude &&
      //       e.longitude == location.longitude,
      // );

      // // Recalculate route
      // await calculateRouteWithAllWaypoints(
      //   isFromCustomWaypoint: true,
      // );

      // notify();

      await addAndRemoveStopCitiesOrCustomWayPoints(
        location,
        isSelected: true,
        isCustomWaypoint: true,
      );
    } catch (e) {
      '==>> clearCustomWaypoints error $e'.logE;
      e.toString().logE;
    }
  }
}
