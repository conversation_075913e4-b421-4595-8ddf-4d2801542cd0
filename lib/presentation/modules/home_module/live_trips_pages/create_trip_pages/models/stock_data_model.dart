// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';

class StockLocationModel {
  StockLocationModel({
    required this.stockLocation,
  });

  factory StockLocationModel.fromJson(List<dynamic> json) {
    return StockLocationModel(
      stockLocation: json
          .map((brand) => StopLocation.fromJson(brand as Map<String, dynamic>))
          .toList(),
    );
  }
  final List<StopLocation> stockLocation;

  List<Map<String, dynamic>> toJson() {
    return stockLocation.map((brand) => brand.toJson()).toList();
  }
}

class Address {
  Address({
    this.street,
    this.neighborhood,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.countryCode,
    this.latitude,
    this.longitude,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        street: json['street'] as String?,
        neighborhood: json['neighborhood'] as String?,
        city: json['city'] as String?,
        state: json['state'] as String?,
        postalCode: json['postal_code'] as String?,
        country: json['country'] as String?,
        countryCode: json['country_code'] as String?,
        latitude: json['latitude']?.toString(),
        longitude: json['longitude']?.toString(),
      );
  final String? street;
  final String? neighborhood;
  final String? city;
  final String? state;
  final String? postalCode;
  final String? country;
  final String? countryCode;
  final String? latitude;
  final String? longitude;

  Map<String, dynamic> toJson() => {
        'street': street,
        'neighborhood': neighborhood,
        'city': city,
        'state': state,
        'postal_code': postalCode,
        'country': country,
        'country_code': countryCode,
        'latitude': latitude,
        'longitude': longitude,
      };
}

class StopLocationModel {
  StopLocationModel({
    required this.stockLocation,
  });

  factory StopLocationModel.fromJson(List<dynamic> json) {
    return StopLocationModel(
      stockLocation: json
          .map((brand) => StopLocation.fromJson(brand as Map<String, dynamic>))
          .toList(),
    );
  }
  final List<StopLocation> stockLocation;

  List<Map<String, dynamic>> toJson() {
    return stockLocation.map((brand) => brand.toJson()).toList();
  }
}

class StopLocation {
  StopLocation({
    this.id,
    this.name,
    this.latitude,
    this.longitude,
    this.date,
    this.time,
    this.stopLocationIndex,
    this.preDistance,
    this.nextDistance,
    this.address,
    this.stopLocationCharge,
    this.customWaypointId,
  });

  factory StopLocation.fromJson(Map<String, dynamic> json) => StopLocation(
        id: json['id'] as num?,
        name: json['name'] as String?,
        latitude: json['latitude']?.toString(),
        longitude: json['longitude']?.toString(),
        address: json['address'] == null
            ? null
            : Address.fromJson(json['address'] as Map<String, dynamic>),
        stopLocationCharge: json['stop_location_charge'] as num?,
      );
  final num? id;
  final String? name;
  final String? latitude;
  final String? longitude;
  final Address? address;
  DateTime? date;
  TimeOfDay? time;
  int? stopLocationIndex;
  num? preDistance;
  num? nextDistance;
  final num? stopLocationCharge;
  final String? customWaypointId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'address': address?.toJson(),
        'latitude': latitude,
        'longitude': longitude,
        'stop_location_charge': stopLocationCharge,
      };
}

extension Add on StopLocation {
  String? get fullAddress {
    final addressVal = AppCommonFunctions.cleanUpAddress(
      '$name${AppCommonFunctions.addComma(address?.street)}'
      '${AppCommonFunctions.addComma(address?.neighborhood)}'
      '${AppCommonFunctions.addComma(address?.city)}'
      '${AppCommonFunctions.addComma(address?.state)}'
      '${AppCommonFunctions.addComma(address?.country)}'
      '${AppCommonFunctions.addComma(address?.postalCode)}',
    );
    return addressVal;
  }
}
