// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';

/// Parameters for the NewRouteScreen
class CreateTripParams {
  /// Constructor
  CreateTripParams({
    this.savedRouteData,
    this.isSetSavedRoute = false,
  });

  // /// From JSON
  // factory CreateTripParams.fromJson(Map<String, dynamic> json) {
  //   return CreateTripParams(
  //     isSetSavedRoute: json['isSetSavedRoute'] as bool? ?? false,
  //     savedRouteData: json['savedRouteData'] != null
  //         ? SavedRoute.fromJson(json['savedRouteData'] as Map<String, dynamic>)
  //         : null,
  //   );
  // }

  /// Whether this is setting a saved route
  final bool isSetSavedRoute;

  /// The saved route data
  final SavedRoute? savedRouteData;

  // /// To JSON
  // Map<String, dynamic> toJ<PERSON>() {
  //   return {
  //     'isSetSavedRoute': isSetSavedRoute,
  //     'savedRouteData': savedRouteData?.toJson(),
  //   };
  // }
}
