// ignore_for_file: public_member_api_docs, avoid_dynamic_calls, unintended_html_in_doc_comment

import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:io';
import 'dart:math';

// import 'package:google_directions_api/google_directions_api.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:transportmatch_provider/env/env.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/suggestion_model.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class PlaceApiProvider {
  final client = http.Client();
  final String apiKey = Platform.isAndroid
      ? AppEnv().googleMapAndroidKey
      : AppEnv().googleMapIosKey;

  Future<List<SuggestionModel>> fetchSuggestions({
    required String input,
    required String lang,
  }) async {
    final request =
        'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$input&language=$lang&key=$apiKey';
    final response = await client.get(Uri.parse(request));

    if (response.statusCode == 200) {
      final result = json.decode(response.body) as Map<String, dynamic>;
      if (result['status'] == 'OK') {
        return (result['predictions'] as List)
            .map<SuggestionModel>(
              (p) => SuggestionModel(
                p['place_id'].toString(),
                p['description'].toString(),
              ),
            )
            .toList();
      }
      if (result['status'] == 'ZERO_RESULTS') {
        return [];
      }
      throw Exception(result['error_message']);
    } else {
      throw Exception('Failed to fetch suggestion');
    }
  }

  /// Fetch place details
  Future<LatLng?> fetchPlaceDetails({
    required String description,
    required List<SuggestionModel> addressList,
  }) async {
    final placeId = addressList
        .firstWhere((element) => element.description == description)
        .placeId;

    final request =
        'https://maps.googleapis.com/maps/api/place/details/json?placeid=$placeId&key=$apiKey';
    final response = await client.get(Uri.parse(request));

    if (response.statusCode == 200) {
      final result = json.decode(response.body) as Map<String, dynamic>;
      if (result['status'] == 'OK') {
        return LatLng(
          result['result']['geometry']['location']['lat'] as double,
          result['result']['geometry']['location']['lng'] as double,
        );
      }
      if (result['status'] == 'ZERO_RESULTS') {
        return null;
      }
      throw Exception(result['error_message']);
    } else {
      throw Exception('Failed to fetch suggestion');
    }
  }

  /// Get route list
  Future<({List<LatLng> routeList, double distance})> getRouteList({
    required LatLng origin,
    required LatLng destination,
  }) async {
    final routeList = <LatLng>[];
    double distance = 0;
    final request =
        'https://maps.googleapis.com/maps/api/directions/json?origin='
        '${origin.latitude},${origin.longitude}&destination=${destination.latitude}'
        ',${destination.longitude}&mode=driving&key=$apiKey';

    final response = await client.get(
      Uri.parse(request),
    );

    if (response.statusCode == 200) {
      /// Extract route coordinates
      final routeCoordinates = _extractRouteCoordinates(
        json.decode(response.body) as Map<String, dynamic>,
      );
      // await getBoundingBox(routeCoordinates);
      for (final coordinate in routeCoordinates) {
        routeList.add(LatLng(coordinate.latitude, coordinate.longitude));
      }

      /// Extract distance
      final result = json.decode(response.body) as Map<String, dynamic>;
      final list = result['routes'];
      if (list is List) {
        if (list.isNotEmpty) {
          if (list.first['legs'] is List) {
            final legs = list.first['legs'] as List;
            if (legs.isNotEmpty) {
              if (legs.first['steps'] is List) {
                final steps = legs.first['steps'] as List;
                if (steps.isNotEmpty) {
                  for (final step in steps) {
                    if (step['start_location'] is Map) {
                      final startLocation =
                          step['start_location'] as Map<String, dynamic>;
                      if (startLocation['lat'] is double &&
                          startLocation['lng'] is double) {
                        if (step['distance']['value'] is num) {
                          distance += step['distance']['value'] as num;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    return (routeList: routeList, distance: distance);
  }

  /// Get route with waypoints
  /// This method allows calculating a route with multiple intermediate points in a single API call
  /// [origin] - Starting point of the route
  /// [destination] - End point of the route
  /// [waypoints] - List of intermediate points (waypoints) to include in the route
  /// Returns a tuple with:
  /// - routeList: List<LatLng> - The complete route polyline
  /// - totalDistance: double - The total distance of the route in meters
  /// - legRoutes: List<List<LatLng>> - Individual leg routes between waypoints
  Future<
      ({
        List<LatLng> routeList,
        double totalDistance,
        List<double> legDistances,
        List<LatLng> optimizedWaypoints,
        String url,
      })> getRouteWithWaypoints({
    required LatLng origin,
    required LatLng destination,
    required List<LatLng> waypoints,
  }) async {
    final routeList = <LatLng>[];
    // final legRoutes = <List<LatLng>>[];
    final legDistances = <double>[];
    final optimizedWaypoints = <LatLng>[];
    double totalDistance = 0;

    // Build waypoints string with optimize:true
    final waypointsBuffer = StringBuffer();
    if (waypoints.isNotEmpty) {
      waypointsBuffer.write('&waypoints=optimize:true');
      for (final wp in waypoints) {
        waypointsBuffer.write('|${wp.latitude},${wp.longitude}');
      }
    }

    final url = 'origin=${origin.latitude},${origin.longitude}'
        '&destination=${destination.latitude},${destination.longitude}';

    final request =
        'https://maps.googleapis.com/maps/api/directions/json?$url$waypointsBuffer&mode=driving&key=$apiKey';

    dev.log('API Request: $request');

    final response = await client.get(Uri.parse(request));

    if (response.statusCode == 200) {
      final result = json.decode(response.body) as Map<String, dynamic>;

      if (result['status'] == 'OK' &&
          result['routes'] is List &&
          (result['routes'] as List).isNotEmpty) {
        final route = result['routes'][0];

        // 🔁 Get optimized waypoint order
        final waypointOrderRaw = route['waypoint_order'] as List<dynamic>?;
        waypointOrderRaw.logD;
        if (waypointOrderRaw != null && waypointOrderRaw.isNotEmpty) {
          for (final index in waypointOrderRaw) {
            if (index is int && index >= 0 && index < waypoints.length) {
              optimizedWaypoints.add(waypoints[index]);
            }
          }
        } else {
          // If not present, fallback to original order
          optimizedWaypoints.addAll(waypoints);
        }

        // Extract overall route coordinates
        final routeCoordinates = _extractRouteCoordinates(result);
        for (final coordinate in routeCoordinates) {
          routeList.add(LatLng(coordinate.latitude, coordinate.longitude));
        }

        // Process each leg separately
        if (route['legs'] is List) {
          final legs = route['legs'] as List;

          for (final leg in legs) {
            //   final legCoordinates = <LatLng>[];
            //   double legDistance = 0;

            //   // Extract leg distance
            //   if (leg['distance'] != null && leg['distance']['value'] is num) {
            //     legDistance = (leg['distance']['value'] as num).toDouble();
            //     totalDistance += legDistance;
            //   }

            //   // Extract leg polyline points
            //   if (leg['steps'] is List) {
            //     for (final step in leg['steps'] as List) {
            //       if (step['polyline'] != null &&
            //           step['polyline']['points'] is String) {
            //         final stepPoints =
            //             _decodePolyline(step['polyline']['points'] as String);
            //         legCoordinates.addAll(stepPoints);
            //       }
            //     }
            //   }

            //   legRoutes.add(legCoordinates);
            double legDistance = 0;

            if (leg['distance'] != null && leg['distance']['value'] is num) {
              legDistance = (leg['distance']['value'] as num).toDouble();
              totalDistance += legDistance;
            }

            legDistances.add(legDistance);
          }
        }
      } else {
        dev.log('API Error: ${result['status']}');
      }
    }

    optimizedWaypoints.logD;
    // routeList.logD;
    return (
      routeList: routeList,
      totalDistance: totalDistance,
      // legRoutes: legRoutes,
      legDistances: legDistances,
      optimizedWaypoints: optimizedWaypoints,
      url: url,
    );
  }

  // /// Get bounding box of the route
  // Future<void> getBoundingBox(List<LatLng> routePoints) async {
  //   // Compute min/max lat/lng
  //   var minLat = routePoints.first.latitude;
  //   var maxLat = routePoints.first.latitude;
  //   var minLng = routePoints.first.longitude;
  //   var maxLng = routePoints.first.longitude;

  //   for (final point in routePoints) {
  //     if (point.latitude < minLat) minLat = point.latitude;
  //     if (point.latitude > maxLat) maxLat = point.latitude;
  //     if (point.longitude < minLng) minLng = point.longitude;
  //     if (point.longitude > maxLng) maxLng = point.longitude;
  //   }

  //   // Define bounding box corners
  //   final topLeft = LatLng(maxLat, minLng);
  //   final topRight = LatLng(maxLat, maxLng);
  //   final bottomLeft = LatLng(minLat, minLng);
  //   final bottomRight = LatLng(minLat, maxLng);
  // }

  /// Below function is used to decode polyline
  List<LatLng> _decodePolyline(String encoded) {
    final points = <LatLng>[];
    var index = 0;
    final len = encoded.length;
    var lat = 0;
    var lng = 0;

    while (index < len) {
      var shift = 0;
      var result = 0;
      int b;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1F) << shift;
        shift += 5;
      } while (b >= 0x20);
      final deltaLat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1F) << shift;
        shift += 5;
      } while (b >= 0x20);
      final deltaLng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }
    return points;
  }

  /// below function is used to extract route coordinates
  List<LatLng> _extractRouteCoordinates(
    Map<String, dynamic> route,
  ) {
    final coordinates = <LatLng>[];

    if (route.containsKey('routes') && (route['routes'] as List).isNotEmpty) {
      for (final leg in route['routes'][0]['legs'] as List) {
        for (final step in leg['steps'] as List) {
          coordinates
              .addAll(_decodePolyline(step['polyline']['points'] as String));
        }
      }
    }

    return coordinates;
  }

  LatLngBounds getLatLngBounds(List<LatLng> markers) {
    var southWestLat = markers.first.latitude;
    var southWestLng = markers.first.longitude;
    var northEastLat = markers.first.latitude;
    var northEastLng = markers.first.longitude;

    for (final marker in markers) {
      if (marker.latitude < southWestLat) southWestLat = marker.latitude;
      if (marker.longitude < southWestLng) southWestLng = marker.longitude;
      if (marker.latitude > northEastLat) northEastLat = marker.latitude;
      if (marker.longitude > northEastLng) northEastLng = marker.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(southWestLat, southWestLng),
      northeast: LatLng(northEastLat, northEastLng),
    );
  }

  double getDistance({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
  }) {
    const double R = 6371; // Earth's radius in km
    final dLat = _degreesToRadians(lat2 - lat1);
    final dLon = _degreesToRadians(lon2 - lon1);

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c; // Distance in km
  }

  double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  LatLngBounds getNewBound({
    required LatLng minLatLng,
    required LatLng maxLatLng,
  }) {
    final centerLatLng = LatLng(
      (minLatLng.latitude + maxLatLng.latitude) / 2,
      (minLatLng.longitude + maxLatLng.longitude) / 2,
    );

    final distance = getDistance(
      lat1: centerLatLng.latitude,
      lon1: centerLatLng.longitude,
      lat2: minLatLng.latitude,
      lon2: minLatLng.longitude,
    );

    LatLng? centerLeft;
    LatLng? centerRight;
    LatLng? centerTop;
    LatLng? centerBottom;

    final minusDegree = distance / 111.320;

    if (distance > 0) {
      centerLeft = LatLng(
        centerLatLng.latitude,
        centerLatLng.longitude - minusDegree,
      );
      centerRight = LatLng(
        centerLatLng.latitude,
        centerLatLng.longitude + minusDegree,
      );
    }

    if (distance > 0) {
      centerTop = LatLng(
        centerLatLng.latitude + minusDegree,
        centerLatLng.longitude,
      );
      centerBottom = LatLng(
        centerLatLng.latitude - minusDegree,
        centerLatLng.longitude,
      );
    }

    dev.log('===>>> centerLeft ===>> $centerLeft');
    dev.log('===>>> centerRight ===>> $centerRight');
    dev.log('===>>> centerTop ===>> $centerTop');
    dev.log('===>>> centerBottom ===>> $centerBottom');

    final newBounds = LatLngBounds(
      southwest:
          LatLng(centerBottom?.latitude ?? 0, centerLeft?.longitude ?? 0),
      northeast: LatLng(centerTop?.latitude ?? 0, centerRight?.longitude ?? 0),
    );

    dev.log('===>>> newBounds ===>> ==...>>>  $newBounds');

    return newBounds;
  }
}
