// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/address_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';

/// Parameters for the AddressSearch screen
class SearchAddressParams {
  /// Constructor
  SearchAddressParams({
    required this.createTripProvider,
    required this.title,
    this.isDrop = false,
    this.isCustomWaypoint = false,
    this.onTap,
  });

  /// The create trip provider
  final CreateTripProvider createTripProvider;

  /// The title
  final String title;

  /// Whether this is a drop location
  final bool isDrop;

  /// Whether this is a custom waypoint
  final bool isCustomWaypoint;

  /// The callback when an address is selected
  final Function(AddressModel address)? onTap;
}
