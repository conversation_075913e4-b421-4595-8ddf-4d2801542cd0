import 'package:transportmatch_provider/extensions/ext_string.dart';

class AddressModel {
  AddressModel({
    this.street,
    // this.neighborhood,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.countryCode,
    this.latitude,
    this.longitude,
    this.address,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) => AddressModel(
        street: json['street'] as String?,
        // neighborhood: json['neighborhood'] as String?,
        city: json['city'] as String?,
        state: json['state'] as String?,
        postalCode: json['postal_code'] as String?,
        country: json['country'] as String?,
        countryCode: json['country_code'] as String?,
        latitude: json['latitude']?.toString(),
        longitude: json['longitude']?.toString(),
      );
  String? address;
  final String? street;
  // final String? neighborhood;
  final String? city;
  final String? state;
  final String? postalCode;
  final String? country;
  final String? countryCode;
  final String? latitude;
  final String? longitude;

  Map<String, dynamic> toJson() => {
        if (street != null) 'street': street,
        // if (neighborhood != null) 'neighborhood': neighborhood,
        if (city != null) 'city': city,
        if (state != null) 'state': state,
        if (postalCode != null) 'postal_code': postalCode,
        if (country != null) 'country': country,
        if (countryCode != null) 'country_code': countryCode,
        if (latitude != null) 'latitude': latitude?.doubletToShort(),
        if (longitude != null) 'longitude': longitude?.doubletToShort(),
      };
}
