//Copy this CustomPainter code to the Bottom of the File
import 'package:flutter/material.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';

class RPSCustomPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final path_0 = Path()
      ..moveTo(-3.125, 0)
      ..lineTo(21.875, 0)
      ..lineTo(8.875, 10)
      ..lineTo(-3.125, 0)
      ..close();

    final paint0Fill = Paint()
      ..style = PaintingStyle.fill
      ..color = AppColors.primaryColor;
    canvas.drawPath(path_0, paint0Fill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
