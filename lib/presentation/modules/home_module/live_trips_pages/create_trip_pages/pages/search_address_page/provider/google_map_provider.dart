import 'dart:async';
import 'dart:developer';

import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/places_api_provider_class.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/address_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/widgets/triangle_painter.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/suggestion_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/logger.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/marqee_widget.dart';

class GoogleMapProvider extends ChangeNotifier {
  GoogleMapProvider() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        focusNode.requestFocus();
      },
    );
  }

  /// Controller for custom info window
  final customInfoWindowController = CustomInfoWindowController();

  /// Loading state flag
  final isShowLoader = ValueNotifier(false);

  /// List of address suggestions
  List<SuggestionModel> addressList = [];

  /// Flag to check if map is created
  bool isMapCreated = false;

  /// Center position of the map
  LatLng center = const LatLng(19.432608, -99.133209);

  /// Set of markers on the map
  final Set<Marker> markers = {};

  /// Set of stop location markers
  final Set<Marker> stopMarkers = {};

  /// Selected address text
  String? selectedAddress;

  /// Flag to check if provider is closed
  bool isClosed = false;

  final FocusNode focusNode = FocusNode();

  /// Called when map is created
  /// [controller] is the GoogleMapController instance
  void onMapCreated(GoogleMapController controller) {
    if (isClosed) return;

    if (!isMapCreated) {
      customInfoWindowController.googleMapController = controller;
      isMapCreated = true;
      notify();
    }
  }

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Called when camera position changes
  /// [position] is the new camera position
  void onCameraMove(CameraPosition position) {
    if (isClosed) return;

    customInfoWindowController.onCameraMove?.call();
    notify();
  }

  /// Called when map is tapped
  /// [position] is the tapped position
  /// [isSearch] flag to search for stop locations
  /// [isDrop] flag to indicate if this is a drop location
  /// [homeProvider] is the HomeProvider instance
  /// [context] is the BuildContext
  Future<void> onMapTap(
    LatLng position, {
    required bool isSearch,
    required bool isDrop,
    required CreateTripProvider createTripProvider,
    required BuildContext context,
    bool isCustomWaypoint = false,
  }) async {
    if (isClosed) return;

    try {
      customInfoWindowController.hideInfoWindow!();
      updateMarker(
        position: position,
        isWaypoint: isCustomWaypoint,
        context: context,
      );

      if (isCustomWaypoint) {
        // // For custom waypoints, just return the coordinates
        if (context.mounted) {
          // final address = await getAddressData(
          //   context,
          //   isBack: false,
          // );
          // if (address != null && context.mounted) {
          //   AppNavigationService.pop(context, address);
          // }
        }
      } else if (isSearch) {
        await _findStopLocation(
          context: context,
          createTripProvider: createTripProvider,
          place: position,
          isDrop: isDrop,
        );
      }
      selectedAddress = null;
      notify();
    } catch (e) {
      'onMapTap error: $e'.logE;
    }
  }

  /// Get address suggestions from Google Places API
  /// [search] is the search text
  Future<List<SuggestionModel>> getAddress(String search) async {
    if (isClosed) return [];

    try {
      return search.isEmpty
          ? []
          : await PlaceApiProvider().fetchSuggestions(
              input: search,
              lang: 'en',
            );
    } catch (e) {
      'getAddress error: $e'.logE;
      return [];
    }
  }

  /// Update marker on the map
  /// [position] is the marker position
  void updateMarker({
    required LatLng position,
    bool isWaypoint = false,
    required BuildContext context,
  }) {
    if (isClosed) return;

    try {
      markers
        ..clear()
        ..add(
          Marker(
            markerId: const MarkerId('selected_location'),
            position: position,
            icon: BitmapDescriptor.defaultMarkerWithHue(
              isWaypoint ? BitmapDescriptor.hueViolet : BitmapDescriptor.hueRed,
            ),
            onTap: !isWaypoint
                ? null
                : () {
                    customInfoWindowController.addInfoWindow!(
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(AppSize.r10),
                            ),
                            // width: double.infinity,
                            // height: double.infinity,
                            padding: EdgeInsets.all(AppSize.sp10),

                            child: GestureDetector(
                              onTap: () async {
                                if (context.mounted) {
                                  isShowLoader.value = true;
                                  final address = await getAddressData(
                                    context,
                                    latitude: position.latitude,
                                    longitude: position.longitude,
                                    isBack: false,
                                  );
                                  isShowLoader.value = false;
                                  AppNavigationService.pop(
                                    context,
                                    address,
                                  );
                                }
                              },
                              child: DecoratedBox(
                                decoration: BoxDecoration(
                                  color: AppColors.white,
                                  borderRadius:
                                      BorderRadius.circular(AppSize.r10),
                                ),
                                child: AppPadding.symmetric(
                                  horizontal: AppSize.w10,
                                  child: Text(
                                    context.l10n.select,
                                    textAlign: TextAlign.center,
                                    style:
                                        context.textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          CustomPaint(
                            size: const Size(20, 10),
                            painter: RPSCustomPainter(),
                          ),
                        ],
                      ),
                      position,
                    );
                  },
          ),
        );
      center = position;
    } catch (e) {
      'updateMarker error: $e'.logE;
    }
  }

  /// Add stop location markers on the map
  /// [list] is the list of stop locations
  /// [position] is the current position
  /// [context] is the BuildContext
  Future<void> addMarker(
    List<StopLocation> list,
    LatLng position,
    BuildContext context,
  ) async {
    if (isClosed) return;

    try {
      stopMarkers.clear();
      if (isClosed) return;
      for (final element in list) {
        if (isClosed) break;

        final lat = double.tryParse(element.address?.latitude ?? '');
        final long = double.tryParse(element.address?.longitude ?? '');
        if (lat != null && long != null) {
          final latLng = LatLng(lat, long);
          stopMarkers.add(
            Marker(
              markerId: MarkerId(element.id.toString()),
              position: latLng,
              icon: await BitmapDescriptor.asset(
                ImageConfiguration.empty,
                'assets/icons/pin.png',
                height: AppSize.sp50,
              ),
              onTap: () {
                customInfoWindowController.addInfoWindow!(
                  Column(
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(AppSize.r10),
                          ),
                          width: double.infinity,
                          height: double.infinity,
                          padding: EdgeInsets.all(AppSize.sp10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    MarqueeWidget(
                                      child: Text(
                                        element.name ?? '',
                                        maxLines: 1,
                                        style: context.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: AppColors.white,
                                          fontSize: AppSize.sp14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    MarqueeWidget(
                                      child: Text(
                                        element.fullAddress ?? '',
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: context.textTheme.bodySmall
                                            ?.copyWith(
                                          color: AppColors.white,
                                          fontSize: AppSize.sp12,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Gap(AppSize.w6),
                              GestureDetector(
                                onTap: () async {
                                  if (context.mounted) {
                                    isShowLoader.value = true;
                                    await getAddressData(
                                      context,
                                      locationId: element.id.toString(),
                                    );
                                    isShowLoader.value = false;
                                  }
                                },
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: AppColors.white,
                                    borderRadius:
                                        BorderRadius.circular(AppSize.r10),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSize.h8,
                                      vertical: AppSize.h2,
                                    ),
                                    child: Text(
                                      context.l10n.select,
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.black,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CustomPaint(
                        size: const Size(20, 10),
                        painter: RPSCustomPainter(),
                      ),
                    ],
                  ),
                  latLng,
                );
              },
            ),
          );
        }
      }
      notify();
    } catch (e) {
      'addMarker error: $e'.logE;
    }
  }

  /// below function is used to move the map to the current location
  /// and to get the current location
  /// [context] is the context of the screen
  /// [isExclusive] is the boolean value to check if the screen is exclusive trip screen
  Future<void> moveToCurrentLocation(BuildContext context) async {
    if (isClosed) return;
    try {
      // Check if location services are enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (isClosed) return;
      if (!serviceEnabled) {
        if (context.mounted) {
          context.l10n.locationServicesDisabled.showErrorAlert();
        }
        return;
      }

      // Check location permission
      var permission = await Geolocator.checkPermission();
      if (isClosed) return;
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (context.mounted) {
            context.l10n.locationPermissionsDenied.showErrorAlert();
          }
          return;
        }
      }

      if (isClosed) return;
      if (permission == LocationPermission.deniedForever) {
        if (context.mounted) {
          context.l10n.locationPermissionsDeniedForever.showErrorAlert();
        }
        return;
      }

      final position = await Geolocator.getCurrentPosition();
      if (isClosed) return;

      if (customInfoWindowController.googleMapController != null &&
          context.mounted) {
        unawaited(
          customInfoWindowController.googleMapController!.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(position.latitude, position.longitude),
                zoom: 14,
              ),
            ),
          ),
        );

        if (isClosed) return;
        center = LatLng(position.latitude, position.longitude);
        updateMarker(position: center, context: context);
      }
    } catch (e) {
      if (isClosed) return;
      log('Error getting current location: $e');
      if (context.mounted) {
        context.l10n.failedToGetLocation.showErrorAlert();
      }
    }
  }

  /// this function is used when search address text filed element is tapped
  /// and to get stop location around the selected address
  /// [value] is the selected address from the search list
  Future<void> onAddressTap(
    String value,
    BuildContext context, {
    required bool isDrop,
    required CreateTripProvider createTripProvider,
    bool isCustomWaypoint = false,
  }) async {
    isShowLoader.value = true;

    try {
      selectedAddress = value;
      final place = await PlaceApiProvider().fetchPlaceDetails(
        description: value,
        addressList: addressList,
      );

      if (isClosed) return;
      if (place != null) {
        if (customInfoWindowController.googleMapController != null &&
            context.mounted) {
          await customInfoWindowController.googleMapController!.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(
                  place.latitude,
                  place.longitude,
                ),
                zoom: 11,
              ),
            ),
          );
          updateMarker(
            position: LatLng(
              place.latitude,
              place.longitude,
            ),
            isWaypoint: isCustomWaypoint,
            context: context,
          );

          if (isCustomWaypoint) {
            // For custom waypoints, just return the address
            // if (context.mounted) {
            //   AppNavigationService.pop(
            //     context,
            //     AddressModel(
            //       address: value,
            //       latitude: place.latitude.toString(),
            //       longitude: place.longitude.toString(),
            //     ),
            //   );
            // }
          } else if (context.mounted) {
            // For regular waypoints, find stop locations
            await _findStopLocation(
              context: context,
              createTripProvider: createTripProvider,
              place: place,
              isDrop: isDrop,
            );
          }
        }
      } else {
        if (isClosed) return;
        if (context.mounted) {
          context.l10n.selectAnotherAddress.showErrorAlert();
        }
      }
    } catch (e) {
      if (isClosed) return;
      log('Error: $e');
      if (context.mounted) {
        context.l10n.somethingWentWrong.showErrorAlert();
      }
    } finally {
      isShowLoader.value = false;
    }
  }

  /// Find stop locations near the selected position
  /// [context] is the BuildContext
  /// [place] is the selected position
  /// [isDrop] flag to indicate if this is a drop location
  Future<void> _findStopLocation({
    required BuildContext context,
    required CreateTripProvider createTripProvider,
    required LatLng place,
    required bool isDrop,
  }) async {
    if (isClosed) return;

    try {
      final val = await createTripProvider.listNearByLocation(
        context: context,
        isDrop: isDrop,
        latLng: center,
      );

      if (!isClosed && val.isNotEmpty) {
        await addMarker(
          val,
          LatLng(
            place.latitude,
            place.longitude,
          ),
          // ignore: use_build_context_synchronously
          context,
        );
      }
    } catch (e) {
      '_findStopLocation error: $e'.logE;
    }
  }

  /// Get address data from coordinates
  /// [context] is the BuildContext
  /// [onTap] callback when address is selected
  /// [locationId] optional location ID
  /// [isBack] flag to navigate back
  Future<AddressModel?> getAddressData(
    BuildContext context, {
    Function(AddressModel address)? onTap,
    String? locationId,
    double? latitude,
    double? longitude,
    bool isBack = true,
  }) async {
    if (isClosed) return null;

    try {
      final placeMarks = await placemarkFromCoordinates(
        latitude ?? center.latitude,
        longitude ?? center.longitude,
      );

      if (isClosed) return null;
      AddressModel? address;

      if (placeMarks.isNotEmpty) {
        final addressText = selectedAddress ??
            '${placeMarks.first.name}, ${placeMarks.first.locality},'
                ' ${placeMarks.first.administrativeArea}, ${placeMarks.first.country}';
        final placemark = placeMarks.first;
        address = AddressModel(
          latitude: latitude?.toString() ?? center.latitude.toString(),
          longitude: longitude?.toString() ?? center.longitude.toString(),
          address: addressText,
          city: placemark.locality,
          country: placemark.country,
          postalCode: placemark.postalCode,
          state: placemark.administrativeArea,
          countryCode: placemark.isoCountryCode,
          street: addressText,
        );

        if (isClosed) return null;
        if (!isClosed) onTap?.call(address);
        if (isBack && context.mounted && !isClosed) {
          AppNavigationService.pop(context, (address, locationId));
        }
      } else {
        if (isClosed) return null;
        if (context.mounted) {
          context.l10n.failedToGetLocationDetails.showErrorAlert();
        }
      }
      return address;
    } catch (e) {
      if (isClosed) return null;
      'getAddressData error: $e'.logE;
      if (context.mounted) {
        context.l10n.failedToGetLocationDetails.showErrorAlert();
      }
      return null;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    customInfoWindowController.dispose();
    focusNode.dispose();
    super.dispose();
  }
}
