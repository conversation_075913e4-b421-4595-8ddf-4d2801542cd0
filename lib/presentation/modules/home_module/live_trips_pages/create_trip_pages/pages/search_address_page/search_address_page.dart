// ignore_for_file: use_build_context_synchronously

import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/models/search_address_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/provider/google_map_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class AddressSearchPage extends StatelessWidget {
  const AddressSearchPage({
    required this.searchAddressParams,
    super.key,
  });
  final SearchAddressParams searchAddressParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => GoogleMapProvider(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: AppColors.pageBGColor,
        appBar: CustomAppBar(title: searchAddressParams.title),
        body: Consumer<GoogleMapProvider>(
          builder: (context, googleMapProvider, child) {
            return ValueListenableBuilder(
              valueListenable:
                  searchAddressParams.createTripProvider.isShowLoader,
              builder: (context, value, mainChild) {
                return ValueListenableBuilder(
                  valueListenable:
                      context.read<GoogleMapProvider>().isShowLoader,
                  builder: (context, isShowLoader, child) {
                    return AppLoader(
                      isShowLoader: value || isShowLoader,
                      child: mainChild!,
                    );
                  },
                );
              },
              child: Stack(
                children: [
                  /// google map widget here
                  GoogleMap(
                    initialCameraPosition: CameraPosition(
                      target: googleMapProvider.center,
                      zoom: 14,
                    ),
                    zoomControlsEnabled: false,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: false,
                    markers: (googleMapProvider.markers.toList() +
                            googleMapProvider.stopMarkers.toList())
                        .toSet(),
                    // mapType: MapType.hybrid,
                    onMapCreated: googleMapProvider.onMapCreated,
                    onCameraMove: googleMapProvider.onCameraMove,
                    onTap: (argument) async => googleMapProvider.onMapTap(
                      argument,
                      isSearch: true,
                      isDrop: searchAddressParams.isDrop,
                      createTripProvider:
                          searchAddressParams.createTripProvider,
                      context: context,
                      isCustomWaypoint: searchAddressParams.isCustomWaypoint,
                    ),
                  ),
                  CustomInfoWindow(
                    controller: googleMapProvider.customInfoWindowController,
                    height: searchAddressParams.isCustomWaypoint
                        ? AppSize.h50
                        : AppSize.h80,
                    width: AppSize.w230,
                  ),

                  /// search address text filed widget here
                  Positioned(
                    top: AppSize.h10,
                    left: AppSize.appPadding,
                    right: AppSize.appPadding,
                    child: SizedBox(
                      width: MediaQuery.sizeOf(context).width,
                      height: AppSize.h60,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Flexible(
                            child: TypeAheadField<String>(
                              focusNode: googleMapProvider.focusNode,
                              builder: (context, controller, focusNode) =>
                                  AppTextFormField(
                                controller: controller,
                                focusNode: focusNode,
                                hintText: context.l10n.searchAddress,
                              ),
                              itemBuilder: (context, value) => Padding(
                                padding: EdgeInsets.all(AppSize.sp10),
                                child: Text(value),
                              ),
                              // offset: const Offset(0, -10),
                              decorationBuilder: (context, child) =>
                                  DecoratedBox(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  color: AppColors.pageBGColor,
                                ),
                                child: child,
                              ),
                              onSelected: (value) async =>
                                  googleMapProvider.onAddressTap(
                                value,
                                context,
                                isDrop: searchAddressParams.isDrop,
                                createTripProvider:
                                    searchAddressParams.createTripProvider,
                                isCustomWaypoint:
                                    searchAddressParams.isCustomWaypoint,
                              ),
                              emptyBuilder: (context) => const SizedBox(),
                              suggestionsCallback: (search) async {
                                googleMapProvider.addressList =
                                    await googleMapProvider.getAddress(search);
                                return googleMapProvider.addressList
                                    .map((e) => e.description)
                                    .toList();
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  /// select location button widget here
                  // Positioned(
                  //   bottom: MediaQuery.paddingOf(context).bottom / 2 + 10,
                  //   left: 20,
                  //   right: 20,
                  //   child: AppButton(
                  //     text: context.l10n.selectLocation,
                  //     onPressed: () => googleMapProvider.onBtnTap(
                  //       context,
                  //       onTap: widget.onTap,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
