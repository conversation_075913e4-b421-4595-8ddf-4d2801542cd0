// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/models/equipments_list_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/driver_list_page_for_provider/models/driver_list_model.dart';

class StockLocationData {
  StockLocationData({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory StockLocationData.fromJson(Map<String, dynamic> json) =>
      StockLocationData(
        count: json['count'] as int?,
        next: json['next'],
        previous: json['previous'],
        results: json['results'] == null
            ? []
            : List<SavedRoute>.from(
                (json['results'] as List?)?.map(
                      (x) => SavedRoute.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
      );
  final int? count;
  final dynamic next;
  final dynamic previous;
  final List<SavedRoute>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results == null
            ? <dynamic>[]
            : List<dynamic>.from(results!.map((x) => x.toJson())),
      };
}

class SavedRoute {
  SavedRoute({
    this.id,
    this.equipment,
    this.startStopLocation,
    this.endStopLocation,
    this.spotAvailableForReservation,
    this.driver,
    this.tripStartDate,
    this.tripEndDate,
    this.allowIntermediatePickup,
    this.status,
    this.costPerKilometer,
    this.deadlineDate,
    this.completedAt,
    this.totalTripDistance,
    this.intermediatePickUpPoint,
    this.tripType,
    this.sharedTripData,
    this.exclusiveTripData,
    this.waitingListTripData,
    this.reports,
  });

  factory SavedRoute.fromJson(Map<String, dynamic> json) => SavedRoute(
        id: json['id'] as int?,
        allowIntermediatePickup: json['allow_intermediate_pickup'] as bool?,
        intermediatePickUpPoint: json['intermediate_pick_up_point'] == null
            ? []
            : List<IntermediatePickUpPoint>.from(
                (json['intermediate_pick_up_point'] as List?)?.map(
                      (x) => IntermediatePickUpPoint.fromJson(
                        x as Map<String, dynamic>,
                      ),
                    ) ??
                    [],
              ),
        startStopLocation: json['start_stop_location'] == null
            ? null
            : StopLocation.fromJson(
                json['start_stop_location'] as Map<String, dynamic>,
              ),
        endStopLocation: json['end_stop_location'] == null
            ? null
            : StopLocation.fromJson(
                json['end_stop_location'] as Map<String, dynamic>,
              ),
        equipment: json['equipment'] == null
            ? null
            : EquipmentDataModel.fromJson(
                json['equipment'] as Map<String, dynamic>,
              ),
        spotAvailableForReservation:
            json['spot_available_for_reservation'] as int?,
        driver: json['driver'] == null
            ? null
            : DriverData.fromJson(json['driver'] as Map<String, dynamic>),
        tripStartDate: json['trip_start_date'] == null
            ? null
            : DateTime.parse(json['trip_start_date'] as String),
        tripEndDate: json['trip_end_date'] == null
            ? null
            : DateTime.parse(json['trip_end_date'] as String),
        status: json['status'] as String?,
        costPerKilometer: json['cost_per_kilometer'] as num?,
        deadlineDate: json['deadline_date'] == null
            ? null
            : DateTime.parse(json['deadline_date'] as String),
        completedAt: json['completed_at'],
        totalTripDistance: json['total_trip_distance'] as num?,
        tripType: json['trip_type'] as String?,
        sharedTripData: json['shared_trip_data'] == null
            ? null
            : SharedTripData.fromJson(
                json['shared_trip_data'] as Map<String, dynamic>,
              ),
        exclusiveTripData: json['exclusive_trip_data'] == null
            ? null
            : ExclusiveTripData.fromJson(
                json['exclusive_trip_data'] as Map<String, dynamic>,
              ),
        waitingListTripData: json['waiting_list_trip_data'],
        reports: json['reports'] == null
            ? []
            : List<dynamic>.from(
                (json['reports'] as List?)?.map((x) => x) ?? [],
              ),
      );
  final int? id;
  final bool? allowIntermediatePickup;
  final List<IntermediatePickUpPoint>? intermediatePickUpPoint;
  final StopLocation? startStopLocation;
  final StopLocation? endStopLocation;
  final EquipmentDataModel? equipment;
  final int? spotAvailableForReservation;
  final DriverData? driver;
  final DateTime? tripStartDate;
  final DateTime? tripEndDate;
  final String? status;
  final num? costPerKilometer;
  final DateTime? deadlineDate;
  final dynamic completedAt;
  final num? totalTripDistance;
  final String? tripType;
  final SharedTripData? sharedTripData;
  final ExclusiveTripData? exclusiveTripData;
  final dynamic waitingListTripData;
  final List<dynamic>? reports;
  Map<String, dynamic> toJson() => {
        'id': id,
        'equipment': equipment?.toJson(),
        'start_stop_location': startStopLocation?.toJson(),
        'end_stop_location': endStopLocation?.toJson(),
        'spot_available_for_reservation': spotAvailableForReservation,
        'driver': driver?.toJson(),
        'trip_start_date': tripStartDate?.toIso8601String(),
        'trip_end_date': tripEndDate?.toIso8601String(),
        'allow_intermediate_pickup': allowIntermediatePickup,
        'status': status,
        'cost_per_kilometer': costPerKilometer,
        'deadline_date': deadlineDate?.toIso8601String(),
        'completed_at': completedAt,
        'total_trip_distance': totalTripDistance,
        'intermediate_pick_up_point': intermediatePickUpPoint == null
            ? <dynamic>[]
            : List<dynamic>.from(
                intermediatePickUpPoint!.map((x) => x.toJson()),
              ),
        'trip_type': tripType,
        'shared_trip_data': sharedTripData?.toJson(),
        'exclusive_trip_data': exclusiveTripData?.toJson(),
        'waiting_list_trip_data': waitingListTripData,
        'reports': reports == null
            ? <dynamic>[]
            : List<dynamic>.from(reports!.map((x) => x)),
      };
}

class IntermediatePickUpPoint {
  IntermediatePickUpPoint({
    this.id,
    this.stopLocation,
    this.estimatedArrivalDate,
    this.stopLocationIndex,
    this.distance,
    this.isArrived,
    this.arrivedAt,
    this.isMovedToNextStop,
    this.movedAt,
    this.startStopLocationPerDayCharge,
    this.endStopLocationPerDayCharge,
  });

  factory IntermediatePickUpPoint.fromJson(Map<String, dynamic> json) =>
      IntermediatePickUpPoint(
        id: json['id'] as int?,
        stopLocation:
            json['stop_location'] == null || json['stop_location'] is String
                ? null
                : StopLocation.fromJson(
                    json['stop_location'] as Map<String, dynamic>,
                  ),
        estimatedArrivalDate: json['estimated_arrival_date'] == null
            ? null
            : DateTime.parse(json['estimated_arrival_date'] as String),
        stopLocationIndex: json['stop_location_index'] as int?,
        distance: json['distance'] as num?,
        isArrived: json['is_arrived'] as bool?,
        arrivedAt: json['arrived_at'],
        startStopLocationPerDayCharge:
            json['start_stop_location_per_day_charge'] as num?,
        endStopLocationPerDayCharge:
            json['end_stop_location_per_day_charge'] as num?,
        isMovedToNextStop: json['is_moved_to_next_stop'] as bool?,
        movedAt: json['moved_at'],
      );
  final int? id;
  final StopLocation? stopLocation;
  final DateTime? estimatedArrivalDate;
  final int? stopLocationIndex;
  final num? distance;
  final bool? isArrived;
  final dynamic arrivedAt;
  final bool? isMovedToNextStop;
  final num? startStopLocationPerDayCharge;
  final num? endStopLocationPerDayCharge;
  final dynamic movedAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'stop_location': stopLocation?.toJson(),
        'estimated_arrival_date': estimatedArrivalDate?.toIso8601String(),
        'stop_location_index': stopLocationIndex,
        'distance': distance,
        'is_arrived': isArrived,
        'arrived_at': arrivedAt,
        'is_moved_to_next_stop': isMovedToNextStop,
        'start_stop_location_per_day_charge': startStopLocationPerDayCharge,
        'end_stop_location_per_day_charge': endStopLocationPerDayCharge,
        'moved_at': movedAt,
      };
}

// class Equipment {
//   Equipment({
//     this.id,
//     this.name,
//     this.plateNumber,
//     this.slot,
//     this.economicNumber,
//     this.insurancePolicyNumber,
//     this.winch,
//   });

//   factory Equipment.fromJson(Map<String, dynamic> json) => Equipment(
//         id: json['id'] as int?,
//         name: json['name'] as String?,
//         plateNumber: json['plate_number'] as String?,
//         slot: json['slot'] as int?,
//         economicNumber: json['economic_number'] as String?,
//         insurancePolicyNumber: json['insurance_policy_number'] as String?,
//         winch: json['winch'] as bool?,
//       );
//   final int? id;
//   final String? name;
//   final String? plateNumber;
//   final int? slot;
//   final String? economicNumber;
//   final String? insurancePolicyNumber;
//   final bool? winch;

//   Map<String, dynamic> toJson() => {
//         'id': id,
//         'name': name,
//         'plate_number': plateNumber,
//         'slot': slot,
//         'economic_number': economicNumber,
//         'insurance_policy_number': insurancePolicyNumber,
//         'winch': winch,
//       };
// }

class ExclusiveTripData {
  ExclusiveTripData({
    this.id,
    this.userStartLocation,
    this.userEndLocation,
    this.offerStatus,
    this.booking,
    this.exclusiveTripId,
  });

  factory ExclusiveTripData.fromJson(Map<String, dynamic> json) =>
      ExclusiveTripData(
        id: json['id'] as int?,
        userStartLocation: json['user_start_location'] == null
            ? null
            : Address.fromJson(
                json['user_start_location'] as Map<String, dynamic>,
              ),
        userEndLocation: json['user_end_location'] == null
            ? null
            : Address.fromJson(
                json['user_end_location'] as Map<String, dynamic>,
              ),
        offerStatus: json['offer_status'] as String?,
        booking: json['booking'] as int?,
        exclusiveTripId: json['exclusive_trip_id'] as String?,
      );
  final int? id;
  final Address? userStartLocation;
  final Address? userEndLocation;
  final String? offerStatus;
  final int? booking;
  final String? exclusiveTripId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'user_start_location': userStartLocation?.toJson(),
        'user_end_location': userEndLocation?.toJson(),
        'offer_status': offerStatus,
        'booking': booking,
        'exclusive_trip_id': exclusiveTripId,
      };
}

class SharedTripData {
  SharedTripData({
    this.id,
    this.isSaved,
    this.sharedTripId,
  });

  factory SharedTripData.fromJson(Map<String, dynamic> json) => SharedTripData(
        id: json['id'] as int?,
        isSaved: json['is_saved'] as bool?,
        sharedTripId: json['shared_trip_id'] as String?,
      );
  final int? id;
  final bool? isSaved;
  final String? sharedTripId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'is_saved': isSaved,
        'shared_trip_id': sharedTripId,
      };
}

// class Driver {
//   Driver({
//     this.id,
//     this.firstName,
//     this.lastName,
//   });

//   factory Driver.fromJson(Map<String, dynamic> json) => Driver(
//         id: json['id'] as int?,
//         firstName: json['first_name'] as String?,
//         lastName: json['last_name'] as String?,
//       );
//   final int? id;
//   final String? firstName;
//   final String? lastName;

//   Map<String, dynamic> toJson() => {
//         'id': id,
//         'first_name': firstName,
//         'last_name': lastName,
//       };
// }
