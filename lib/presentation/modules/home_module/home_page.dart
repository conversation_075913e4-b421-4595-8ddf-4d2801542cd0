// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/home_provider/home_provider.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/widgets/trip_card_widget.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  late final TabController tabController;
  final ValueNotifier<int> activeTabTitleBuilder = ValueNotifier(0);

  void tabListener() {
    activeTabTitleBuilder.value = tabController.index;
  }

  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(tabListener);
    super.initState();
  }

  @override
  void dispose() {
    tabController
      ..removeListener(tabListener)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isProvider =
        Injector.instance<AppDB>().userModel?.user?.role?.toLowerCase() ==
            UserType.Provider.name.toLowerCase();
    return ChangeNotifierProvider<HomeProvider>(
      create: (con) => HomeProvider()..getActiveTrips(isWantShowLoader: true),
      child: Scaffold(
        backgroundColor: AppColors.pageBGColor,
        appBar: CustomAppBar(
          canPop: false,
          horizontalPadding: AppSize.w10,
          title: context.l10n.homeTitle,
          actions: [
            if (isProvider)
              TextButton(
                onPressed: () {
                  AppNavigationService.pushNamed(
                    context,
                    AppRoutes.homeLiveTripsScreen,
                  );
                },
                child: Text(
                  context.l10n.viewRoutes,
                  style: context.textTheme.titleMedium?.copyWith(
                    fontSize: AppSize.sp16,
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              )
            else
              Padding(
                padding: EdgeInsets.only(right: AppSize.appPadding),
                child: GestureDetector(
                  onTap: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.driverProfileScreen,
                  ),
                  child: CircleAvatar(
                    radius: AppSize.r18,
                    backgroundColor: AppColors.primaryColor,
                    child: const Icon(
                      Icons.person_rounded,
                      color: AppColors.white,
                    ),
                  ),
                ),
              ),
          ],
        ),
        body: Builder(
          builder: (con) {
            return AppPadding.symmetric(
              horizontal: AppSize.appPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppPadding.symmetric(
                    vertical: AppSize.sp10,
                    child: Text(context.l10n.upcomingTrips),
                  ),
                  Flexible(
                    child: ValueListenableBuilder<bool>(
                      valueListenable: con.read<HomeProvider>().isLoading,
                      builder: (context, isLoading, child) {
                        return AppLoader(
                          isShowLoader: isLoading &&
                              context.read<HomeProvider>().activeTrips.isEmpty,
                          child: Consumer<HomeProvider>(
                            builder: (context, homeProvider, _) {
                              return SmartRefresher(
                                controller: homeProvider.refreshController,
                                enablePullUp: homeProvider.hasMoreData,
                                onRefresh: () {
                                  homeProvider.getActiveTrips().whenComplete(
                                        homeProvider
                                            .refreshController.refreshCompleted,
                                      );
                                },
                                onLoading: () {
                                  homeProvider
                                      .getActiveTrips(isPagination: true)
                                      .whenComplete(
                                        homeProvider
                                            .refreshController.loadComplete,
                                      );
                                },
                                child: homeProvider.activeTrips.isEmpty &&
                                        !isLoading
                                    ? Center(
                                        child: Text(
                                          context
                                              .l10n.noUpcomingTripAvailableYet,
                                          style: context.textTheme.bodyMedium,
                                        ),
                                      )
                                    : ListView.builder(
                                        itemCount:
                                            homeProvider.activeTrips.length,
                                        padding: EdgeInsets.only(
                                          bottom: AppSize.h16,
                                        ),
                                        itemBuilder: (context, index) {
                                          final tripData =
                                              homeProvider.activeTrips[index];
                                          return TripCard(
                                            tripData: tripData,
                                            onTap: () =>
                                                AppNavigationService.pushNamed(
                                              context,
                                              AppRoutes.tripsAllShipmentsScreen,
                                              extra: AllShipmentsParams(
                                                tripId: tripData.id,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
