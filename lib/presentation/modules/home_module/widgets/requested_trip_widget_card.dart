// // ignore_for_file: public_member_api_docs

// import 'package:flutter/material.dart';
// import 'package:transportmatch_provider/extensions/ext_build_context.dart';
// import 'package:transportmatch_provider/utils/app_colors.dart';
// import 'package:transportmatch_provider/utils/app_size.dart';
// import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
// import 'package:transportmatch_provider/widgets/app_image.dart';
//

// class RequestedTripWidgetCard extends StatelessWidget {
//   const RequestedTripWidgetCard({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: EdgeInsets.only(top: AppSize.sp16),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(AppSize.r6),
//         color: AppColors.white,
//       ),
//       child: Padding(
//         padding: EdgeInsets.all(AppSize.sp16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   'John Doe',
//                   style: context.textTheme.titleMedium?.copyWith(
//                     fontSize: AppSize.sp16,
//                     color: AppColors.ff343A40,
//                     fontWeight: FontWeight.w400,
//                   ),
//                 ),
//                 Text(
//                   r'$20,000',
//                   style: context.textTheme.titleMedium?.copyWith(
//                     fontSize: AppSize.sp16,
//                     fontWeight: FontWeight.w600,
//                     color: AppColors.ff67509C,
//                   ),
//                 ),
//               ],
//             ),
//             Gap(AppSize.h14),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'No. Of Cars',
//                       style: context.textTheme.titleMedium?.copyWith(
//                         fontSize: AppSize.sp12,
//                         fontWeight: FontWeight.w500,
//                         color: AppColors.ffADB5BD,
//                       ),
//                     ),
//                     Gap(AppSize.h4),
//                     Text(
//                       '06',
//                       style: context.textTheme.titleMedium?.copyWith(
//                         fontSize: AppSize.sp18,
//                         color: AppColors.ff343A40,
//                         fontWeight: FontWeight.w600,
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//             Gap(AppSize.h14),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'Otay Mesa',
//                       style: context.textTheme.titleMedium?.copyWith(
//                         fontSize: AppSize.sp14,
//                         color: AppColors.ff343A40,
//                         fontWeight: FontWeight.w400,
//                       ),
//                     ),
//                     Gap(AppSize.h6),
//                     Row(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         AppImage.asset(
//                           AppAssets.iconsCalender.path,
//                           color: AppColors.ff6C757D,
//                           height: AppSize.sp16,
//                           width: AppSize.sp16,
//                         ),
//                         Gap(AppSize.w5),
//                         Text(
//                           'Nov 21',
//                           style: context.textTheme.titleMedium?.copyWith(
//                             fontSize: AppSize.sp12,
//                             color: AppColors.ff6C757D,
//                             fontWeight: FontWeight.w500,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//                 Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     AppImage.asset(
//                       AppAssets.iconsPinAndMap.path,
//                       color: AppColors.ff495057,
//                       // height: AppSize.sp45,
//                       width: AppSize.sp100,
//                     ),
//                     Text(
//                       r'$2.00 per km',
//                       style: context.textTheme.titleMedium?.copyWith(
//                         fontSize: AppSize.sp12,
//                         color: AppColors.ff6C757D,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ],
//                 ),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'Otay Mesa',
//                       style: context.textTheme.titleMedium?.copyWith(
//                         fontSize: AppSize.sp14,
//                         color: AppColors.ff343A40,
//                         fontWeight: FontWeight.w400,
//                       ),
//                     ),
//                     Gap(AppSize.h6),
//                     Row(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         AppImage.asset(
//                           AppAssets.iconsCalender.path,
//                           color: AppColors.ff6C757D,
//                           height: AppSize.sp16,
//                           width: AppSize.sp16,
//                         ),
//                         Gap(AppSize.w5),
//                         Text(
//                           'Nov 21',
//                           style: context.textTheme.titleMedium?.copyWith(
//                             fontSize: AppSize.sp12,
//                             color: AppColors.ff6C757D,
//                             fontWeight: FontWeight.w500,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
