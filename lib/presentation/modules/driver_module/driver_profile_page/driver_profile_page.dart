import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/driver_profile_page/provider/driver_profile_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:transportmatch_provider/widgets/language_change_sheet.dart';

class DriverProfilePage extends StatelessWidget {
  const DriverProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => DriverProfileProvider(),
      child: Builder(
        builder: (context) {
          return Scaffold(
            backgroundColor: AppColors.pageBGColor,
            appBar: CustomAppBar(title: context.l10n.myProfile),
            body: ValueListenableBuilder(
              valueListenable:
                  Provider.of<DriverProfileProvider>(context, listen: false)
                      .isShowLoader,
              builder: (context, isShowLoader, child) => AppLoader(
                isShowLoader: isShowLoader,
                child: child!,
              ),
              child: SafeArea(
                child: AppPadding.symmetric(
                  horizontal: AppSize.appPadding,
                  child: Column(
                    children: [
                      // Profile Header Section
                      Container(
                        width: context.width,
                        padding: EdgeInsets.all(AppSize.sp10),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .primaryColor
                              .withValues(alpha: .1),
                          borderRadius: BorderRadius.circular(AppSize.r10),
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: AppSize.r22,
                              backgroundColor: AppColors.primaryColor,
                              child: AppAssets.iconsProfileFill.image(
                                height: AppSize.sp22,
                                width: AppSize.sp22,
                                color: AppColors.white,
                              ),
                            ),
                            Gap(AppSize.w10),
                            Text(
                              Injector.instance<AppDB>()
                                      .userModel
                                      ?.user
                                      ?.firstName ??
                                  '',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontSize: AppSize.sp15,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Gap(AppSize.h24),

                      // Settings Section
                      Column(
                        children: [
                          ListTile(
                            leading: const Icon(Icons.language),
                            title: Text(context.l10n.language),
                            trailing: Icon(
                              Icons.arrow_forward_ios,
                              size: AppSize.sp16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSize.r10),
                            ),
                            tileColor: Colors.grey.withValues(alpha: .1),
                            onTap: () {
                              showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                backgroundColor: Colors.transparent,
                                builder: (context) =>
                                    const LanguageChangeSheet(),
                              );
                            },
                          ),
                          Gap(AppSize.h10),
                          ListTile(
                            leading: const Icon(Icons.notifications_rounded),
                            title: Text(context.l10n.allNotifications),
                            trailing: Icon(
                              Icons.arrow_forward_ios,
                              size: AppSize.sp16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSize.r10),
                            ),
                            tileColor: Colors.grey.withValues(alpha: .1),
                            onTap: () => AppNavigationService.pushNamed(
                              context,
                              AppRoutes.driverNotificationScreen,
                            ),
                          ),
                          Gap(AppSize.h10),
                          ListTile(
                            leading:
                                const Icon(Icons.logout, color: Colors.red),
                            title: Text(
                              context.l10n.signOut,
                              style: const TextStyle(color: Colors.red),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios,
                              size: AppSize.sp16,
                              color: Colors.red,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSize.r10),
                            ),
                            tileColor: Colors.red.withValues(alpha: .1),
                            onTap: () async {
                              await context.showAlertDialog(
                                title: l10n.signOut,
                                content: l10n.signOutContent,
                                defaultActionText: l10n.signOut,
                                cancelActionText: l10n.cancel,
                                cancelActionBgColor: AppColors.white,
                                onCancelActionPressed: Navigator.pop,
                                onDefaultActionPressed: (dialogContext) {
                                  Navigator.pop(dialogContext);
                                  Provider.of<DriverProfileProvider>(
                                    context,
                                    listen: false,
                                  ).logout();
                                },
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
