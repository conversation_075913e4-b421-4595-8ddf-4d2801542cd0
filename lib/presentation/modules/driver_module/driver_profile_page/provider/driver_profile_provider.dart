import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class DriverProfileProvider extends ChangeNotifier {
  bool isClosed = false;
  CancelToken? logoutToken;
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  
  Future<void> logout() async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      logoutToken?.cancel();
      logoutToken = CancelToken();
      final deviceId = Injector.instance<AppProvider>().deviceId;
      Map<String, dynamic> data;
      data = {
        ApiKeys.deviceId: deviceId,
        'refresh_token': Injector.instance<AppDB>().refreshToken,
      };
      final request = ApiRequest(
        path: EndPoints.logout,
        data: data,
        cancelToken: logoutToken,
      );
      final res = await Injector.instance<AccountRepository>().logout(request);
      await res.when(
        success: (data) async {
          if (isClosed || (logoutToken?.isCancelled ?? true)) {
            return;
          }
          await Injector.instance<AppDB>().logoutUser();
          isShowLoader.value = false;
          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            rootNavKey.currentContext!,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
        },
        error: (exception) async {
          if (isClosed || (logoutToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          await Injector.instance<AppDB>().logoutUser();
          isShowLoader.value = false;
          // Replace all instances of:
          // await AppNavigationService.pushAndRemoveAllScreen(
          //   context,
          //   const LoginScreen(),
          // );

          // With:
          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            rootNavKey.currentContext!,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (logoutToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    logoutToken?.cancel();
    isShowLoader.dispose();
    super.dispose();
  }
}
