import 'package:firebase_notifications_handler/firebase_notifications_handler.dart';
import 'package:flutter/material.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/accepted_trip_page.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/services/notification_service/notification_helper.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:transportmatch_provider/widgets/gap.dart';

// This page is used to show the module which only driver can see
class DriverPage extends StatelessWidget {
  const DriverPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return FirebaseNotificationsHandler(
      onTap: (details) =>
          NotificationHelper.notificationOnTapHandler(details.firebaseMessage),
      shouldHandleNotification: (p0) => true,
      child: Scaffold(
        backgroundColor: AppColors.pageBGColor,
        appBar: CustomAppBar(
          title: l10n.allTrips,
          canPop: false,
          actions: [
            Row(
              children: [
                InkWell(
                  onTap: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.driverChecklistListScreen,
                  ),
                  child: Icon(
                    Icons.pending_actions_outlined,
                    size: AppSize.sp24,
                  ),
                ),
                Gap(AppSize.w10),
                InkWell(
                  onTap: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.driverProfileScreen,
                  ),
                  child: AppAssets.iconsProfileFill
                      .image(height: AppSize.h24, width: AppSize.w24),
                ),
              ],
            ),
          ],
        ),
        body: const AcceptedTripPage(),
      ),
    );
  }
}
