import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/provider/driver_checklist_provider.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/widgets/checklist_card_widget.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/models/add_checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class DriverChecklistPage extends StatelessWidget {
  const DriverChecklistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => DriverChecklistProvider(),
      child: Scaffold(
        backgroundColor: AppColors.pageBGColor,
        appBar: const CustomAppBar(
          title: 'Pending Verification',
        ),
        body: Consumer<DriverChecklistProvider>(
          builder: (context, provider, child) {
            return AppLoader(
              isShowLoader: provider.isLoading,
              child: SmartRefresher(
                controller: provider.refreshController,
                enablePullUp: provider.hasMoreData,
                onRefresh: provider.onRefresh,
                onLoading: provider.onLoading,
                child: provider.isLoading
                    ? const SizedBox.expand()
                    : provider.pendingChecklists.isEmpty
                        ? _buildEmptyState(context)
                        : _buildChecklistList(context, provider),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.checklist_outlined,
              size: AppSize.sp40,
              color: AppColors.ff6C757D,
            ),
            SizedBox(height: AppSize.h16),
            Text(
              'No Pending Checklists Found',
              style: context.textTheme.titleMedium?.copyWith(
                color: AppColors.ff495057,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppSize.h8),
            Text(
              'All checklists have been verified or there are no checklists to review.',
              style: context.textTheme.bodyMedium?.copyWith(
                color: AppColors.ff6C757D,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChecklistList(
    BuildContext context,
    DriverChecklistProvider provider,
  ) {
    return ListView.builder(
      padding: EdgeInsets.all(AppSize.sp16),
      itemCount: provider.pendingChecklists.length,
      itemBuilder: (context, index) {
        final item = provider.pendingChecklists[index];
        return ChecklistCardWidget(
          item: item,
          provider: provider,
          onTap: () {
            AppNavigationService.pushNamed(
              context,
              AppRoutes.tripsAddChecklistScreen,
              extra: AddChecklistParams(
                checkListId: item.id,
                isFromNotification: true,
                checkListProvider: CheckListProvider(
                  carId: item.bookedCar?.id?.toString() ?? '',
                ),
                isVerify: true,
              ),
            );
          },
        );
      },
    );
  }
}
