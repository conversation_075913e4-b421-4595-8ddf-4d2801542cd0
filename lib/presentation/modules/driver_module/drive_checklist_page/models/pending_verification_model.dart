// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/auth_module/models/signup_and_signin_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/checklist_api_model.dart';

/// Model for pending verification checklist response
class PendingVerificationModel {
  PendingVerificationModel({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory PendingVerificationModel.fromJson(Map<String, dynamic> json) =>
      PendingVerificationModel(
        count: json['count'] as int?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<PendingChecklistItem>.from(
                (json['results'] as List?)?.map(
                      (x) => PendingChecklistItem.fromJson(
                        x as Map<String, dynamic>,
                      ),
                    ) ??
                    [],
              ),
      );

  int? count;
  String? next;
  String? previous;
  List<PendingChecklistItem>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results == null
            ? []
            : List<dynamic>.from(results!.map((x) => x.toJson())),
      };
}

/// Individual pending checklist item model
class PendingChecklistItem {
  PendingChecklistItem({
    this.id,
    this.user,
    this.checklistType,
    this.bookedCar,
    this.performedDuring,
    this.mileage,
    this.fuelLevel,
    this.other,
    this.mainLights = false,
    this.mediumLights = false,
    this.stopLightOrTurnSignals = false,
    this.radioAntenna = false,
    this.pairOfWindShieldsWipers = false,
    this.rightSideMirror = false,
    this.sideWindows = false,
    this.windshield = false,
    this.rearWindow = false,
    this.fourWheelCaps = false,
    this.bodyWithoutDents = false,
    this.frontBumper = false,
    this.rearBumper = false,
    this.frontLicensePlate = false,
    this.rearLicensePlate = false,
    this.heating = false,
    this.radio = false,
    this.speakers = false,
    this.lighter = false,
    this.rearviewMirror = false,
    this.ashtrays = false,
    this.seatBelts = false,
    this.windowHandles = false,
    this.rubberFloors = false,
    this.floorMats = false,
    this.seatCovers = false,
    this.doorHandles = false,
    this.holder = false,
    this.engine = false,
    this.jack = false,
    this.wheelWrench = false,
    this.toolKit = false,
    this.triangle = false,
    this.spareTire = false,
    this.fireExtinguisher = false,
    this.scratchedPaint = false,
    this.brokenWindows = false,
    this.dents = false,
    this.suspension = false,
    this.images,
    this.createdAt,
    this.awsImageKeys,
    this.carId,
    this.commentList,
    this.customerChecklistVerificationStatus,
  });

  factory PendingChecklistItem.fromJson(Map<String, dynamic> json) =>
      PendingChecklistItem(
        id: json['id'] as int?,
        user: json['user'] == null
            ? null
            : User.fromJson(json['user'] as Map<String, dynamic>),
        checklistType: json['checklist_type'] as String?,
        bookedCar: json['booked_car'] == null
            ? null
            : BookedCar.fromJson(json['booked_car'] as Map<String, dynamic>),
        performedDuring: json['performed_during'] as String?,
        mileage: json['mileage'] as num?,
        fuelLevel: json['fuel_level'] as String?,
        other: json['other'] as String?,
        mainLights: (json['main_lights'] as bool?) ?? false,
        mediumLights: (json['medium_lights'] as bool?) ?? false,
        stopLightOrTurnSignals:
            (json['stop_light_or_turn_signals'] as bool?) ?? false,
        radioAntenna: (json['radio_antenna'] as bool?) ?? false,
        pairOfWindShieldsWipers:
            (json['pair_of_wind_shields_wipers'] as bool?) ?? false,
        rightSideMirror: (json['right_side_mirror'] as bool?) ?? false,
        sideWindows: (json['side_windows'] as bool?) ?? false,
        windshield: (json['windshield'] as bool?) ?? false,
        rearWindow: (json['rear_window'] as bool?) ?? false,
        fourWheelCaps: (json['four_wheel_caps'] as bool?) ?? false,
        bodyWithoutDents: (json['body_without_dents'] as bool?) ?? false,
        frontBumper: (json['front_bumper'] as bool?) ?? false,
        rearBumper: (json['rear_bumper'] as bool?) ?? false,
        frontLicensePlate: (json['front_license_plate'] as bool?) ?? false,
        rearLicensePlate: (json['rear_license_plate'] as bool?) ?? false,
        heating: (json['heating'] as bool?) ?? false,
        radio: (json['radio'] as bool?) ?? false,
        speakers: (json['speakers'] as bool?) ?? false,
        lighter: (json['lighter'] as bool?) ?? false,
        rearviewMirror: (json['rearview_mirror'] as bool?) ?? false,
        ashtrays: (json['ashtrays'] as bool?) ?? false,
        seatBelts: (json['seat_belts'] as bool?) ?? false,
        windowHandles: (json['window_handles'] as bool?) ?? false,
        rubberFloors: (json['rubber_floors'] as bool?) ?? false,
        floorMats: (json['floor_mats'] as bool?) ?? false,
        seatCovers: (json['seat_covers'] as bool?) ?? false,
        doorHandles: (json['door_handles'] as bool?) ?? false,
        holder: (json['holder'] as bool?) ?? false,
        engine: (json['engine'] as bool?) ?? false,
        jack: (json['jack'] as bool?) ?? false,
        wheelWrench: (json['wheel_wrench'] as bool?) ?? false,
        toolKit: (json['tool_kit'] as bool?) ?? false,
        triangle: (json['triangle'] as bool?) ?? false,
        spareTire: (json['spare_tire'] as bool?) ?? false,
        fireExtinguisher: (json['fire_extinguisher'] as bool?) ?? false,
        scratchedPaint: (json['scratched_paint'] as bool?) ?? false,
        brokenWindows: (json['broken_windows'] as bool?) ?? false,
        dents: (json['dents'] as bool?) ?? false,
        suspension: (json['suspension'] as bool?) ?? false,
        images: json['images'] == null
            ? []
            : List<ImageModel>.from(
                (json['images'] as List?)?.map(
                      (x) => ImageModel.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
        commentList: json['comment_list'] == null
            ? []
            : List<CommentsModel>.from(
                (json['comment_list'] as List?)?.map(
                      (x) => CommentsModel.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
        awsImageKeys: json['aws_image_keys'] as Map<String, dynamic>?,
        carId: json['car_id'] as String?,
        customerChecklistVerificationStatus:
            json['customer_checklist_verification_status'] as String?,
      );

  int? id;
  User? user;
  String? checklistType;
  BookedCar? bookedCar;
  String? performedDuring;
  num? mileage;
  String? fuelLevel;
  String? carId;
  String? other;
  bool mainLights;
  bool mediumLights;
  bool stopLightOrTurnSignals;
  bool radioAntenna;
  bool pairOfWindShieldsWipers;
  bool rightSideMirror;
  bool sideWindows;
  bool windshield;
  bool rearWindow;
  bool fourWheelCaps;
  bool bodyWithoutDents;
  bool frontBumper;
  bool rearBumper;
  bool frontLicensePlate;
  bool rearLicensePlate;
  bool heating;
  bool radio;
  bool speakers;
  bool lighter;
  bool rearviewMirror;
  bool ashtrays;
  bool seatBelts;
  bool windowHandles;
  bool rubberFloors;
  bool floorMats;
  bool seatCovers;
  bool doorHandles;
  bool holder;
  bool engine;
  bool jack;
  bool wheelWrench;
  bool toolKit;
  bool triangle;
  bool spareTire;
  bool fireExtinguisher;
  bool scratchedPaint;
  bool brokenWindows;
  bool dents;
  bool suspension;
  List<ImageModel>? images;
  List<CommentsModel>? commentList;
  DateTime? createdAt;
  Map<String, dynamic>? awsImageKeys;
  String? customerChecklistVerificationStatus;

  Map<String, dynamic> toJson() => {
        if (id != null) 'id': id,
        if (user != null) 'user': user?.toJson(),
        if (checklistType != null) 'checklist_type': checklistType,
        'booked_car': bookedCar?.toJson() ?? carId,
        'performed_during': performedDuring,
        'mileage': mileage,
        'fuel_level': fuelLevel,
        'other': other,
        'main_lights': mainLights,
        'medium_lights': mediumLights,
        'stop_light_or_turn_signals': stopLightOrTurnSignals,
        'radio_antenna': radioAntenna,
        'pair_of_wind_shields_wipers': pairOfWindShieldsWipers,
        'right_side_mirror': rightSideMirror,
        'side_windows': sideWindows,
        'windshield': windshield,
        'rear_window': rearWindow,
        'four_wheel_caps': fourWheelCaps,
        'body_without_dents': bodyWithoutDents,
        'front_bumper': frontBumper,
        'rear_bumper': rearBumper,
        'front_license_plate': frontLicensePlate,
        'rear_license_plate': rearLicensePlate,
        'heating': heating,
        'radio': radio,
        'speakers': speakers,
        'lighter': lighter,
        'rearview_mirror': rearviewMirror,
        'ashtrays': ashtrays,
        'seat_belts': seatBelts,
        'window_handles': windowHandles,
        'rubber_floors': rubberFloors,
        'floor_mats': floorMats,
        'seat_covers': seatCovers,
        'door_handles': doorHandles,
        'holder': holder,
        'engine': engine,
        'jack': jack,
        'wheel_wrench': wheelWrench,
        'tool_kit': toolKit,
        'triangle': triangle,
        'spare_tire': spareTire,
        'fire_extinguisher': fireExtinguisher,
        'scratched_paint': scratchedPaint,
        'broken_windows': brokenWindows,
        'dents': dents,
        'suspension': suspension,
        if (images != null)
          'images': List<dynamic>.from(images!.map((x) => x.toJson())),
        if (commentList != null)
          'comment_list':
              List<dynamic>.from(commentList!.map((x) => x.toJson())),
        if (createdAt != null) 'created_at': createdAt?.toIso8601String(),
        if (awsImageKeys != null) 'aws_image_keys': awsImageKeys,
        if (carId != null) 'car_id': carId,
        if (customerChecklistVerificationStatus != null)
          'customer_checklist_verification_status':
              customerChecklistVerificationStatus,
      };
}
