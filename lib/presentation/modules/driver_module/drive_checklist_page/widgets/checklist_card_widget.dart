import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/models/pending_verification_model.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/provider/driver_checklist_provider.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/widgets/checklist_status_chip.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

/// Widget for displaying individual checklist card
class ChecklistCardWidget extends StatelessWidget {
  /// Constructor
  const ChecklistCardWidget({
    required this.item,
    required this.provider,
    this.onTap,
    super.key,
  });

  /// The checklist item to display
  final PendingChecklistItem item;

  /// The provider for accessing helper methods
  final DriverChecklistProvider provider;

  /// Callback when card is tapped
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final locale = Localizations.localeOf(context).languageCode;

    final statusColor = provider.getCustomerVerificationStatusColor(
      item.customerChecklistVerificationStatus,
    );
    final statusText = provider.getCustomerVerificationStatusText(
      item.customerChecklistVerificationStatus,
      locale,
    );

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: AppSize.h16),
        padding: EdgeInsets.all(AppSize.sp16),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(AppSize.r8),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: AppSize.h4,
          children: [
            // Header with status and type
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    provider.getChecklistTypeText(item.checklistType, locale),
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: AppSize.sp16,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ),
                ChecklistStatusChip(
                  status: statusText,
                  color: statusColor,
                ),
              ],
            ),
            // Vehicle information
            if (item.bookedCar != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.directions_car,
                    size: AppSize.sp16,
                    color: AppColors.ff6C757D,
                  ),
                  Gap(AppSize.w8),
                  Expanded(
                    child: Text(
                      '${item.bookedCar?.car?.brand ?? ''} ${item.bookedCar?.car?.model ?? ''}'
                          .trim(),
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: AppColors.ff495057,
                        fontSize: AppSize.sp14,
                      ),
                    ),
                  ),
                ],
              ),
              Gap(AppSize.h8),
            ],

            // Performed during and mileage info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: TitleInfoWidget(
                    title: l10n.performedDuring,
                    subTitle: provider.getPerformedDuringText(
                      item.performedDuring,
                      locale,
                    ),
                    spacing: AppSize.h4,
                    titleColor: AppColors.ff6C757D,
                    titleFontSize: AppSize.sp12,
                    subTitleColor: AppColors.primaryColor,
                    subTitleFontSize: AppSize.sp14,
                    subTitleFontWeight: FontWeight.w500,
                  ),
                ),
                if (item.mileage != null) ...[
                  Gap(AppSize.w16),
                  TitleInfoWidget(
                    title: 'Mileage',
                    subTitle: '${item.mileage} km',
                    spacing: AppSize.h4,
                    titleColor: AppColors.ff6C757D,
                    titleFontSize: AppSize.sp12,
                    subTitleColor: AppColors.primaryColor,
                    subTitleFontSize: AppSize.sp14,
                    subTitleFontWeight: FontWeight.w500,
                  ),
                ],
              ],
            ),
            Gap(AppSize.h12),

            // Created date and fuel level
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: AppSize.sp14,
                        color: AppColors.ff6C757D,
                      ),
                      Gap(AppSize.w4),
                      Text(
                        item.createdAt?.mmmDdYyyy ?? '',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: AppColors.ff6C757D,
                          fontSize: AppSize.sp12,
                        ),
                      ),
                    ],
                  ),
                ),
                if (item.fuelLevel != null) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.local_gas_station,
                        size: AppSize.sp14,
                        color: AppColors.ff6C757D,
                      ),
                      Gap(AppSize.w4),
                      Text(
                        item.fuelLevel!,
                        style: context.textTheme.bodySmall?.copyWith(
                          color: AppColors.ff6C757D,
                          fontSize: AppSize.sp12,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),

            // Images count if available
            if (item.images != null && item.images!.isNotEmpty) ...[
              Gap(AppSize.h8),
              Row(
                children: [
                  Icon(
                    Icons.photo_library,
                    size: AppSize.sp14,
                    color: AppColors.primaryColor,
                  ),
                  Gap(AppSize.w4),
                  Text(
                    '${item.images!.length} ${item.images!.length == 1 ? (locale.startsWith('es') ? 'imagen' : 'image') : (locale.startsWith('es') ? 'imágenes' : 'images')}',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: AppColors.primaryColor,
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
