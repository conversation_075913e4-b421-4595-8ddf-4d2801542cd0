import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// Widget for displaying checklist verification status as a chip
class ChecklistStatusChip extends StatelessWidget {
  /// Constructor
  const ChecklistStatusChip({
    required this.status,
    required this.color,
    super.key,
  });

  /// The status text to display
  final String status;

  /// The color for the chip
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w8,
        vertical: AppSize.h4,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSize.r12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        status,
        style: context.textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
          fontSize: AppSize.sp10,
        ),
      ),
    );
  }
}
