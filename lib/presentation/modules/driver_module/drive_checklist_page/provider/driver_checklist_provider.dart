import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/models/pending_verification_model.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Provider for managing driver checklist page state
class DriverChecklistProvider extends ChangeNotifier {
  /// Constructor
  DriverChecklistProvider() {
    _loadPendingChecklists(isShowLoader: true);
  }

  bool _isClosed = false;
  bool _isLoading = false;
  String? _nextUrl;
  PendingVerificationModel? _pendingVerificationModel;
  List<PendingChecklistItem> _pendingChecklists = [];
  CancelToken? _getPendingChecklistsToken;
  final RefreshController _refreshController = RefreshController();

  /// Getters
  bool get isLoading => _isLoading;
  List<PendingChecklistItem> get pendingChecklists => _pendingChecklists;
  RefreshController get refreshController => _refreshController;
  bool get hasMoreData => _nextUrl != null;
  int get totalCount => _pendingVerificationModel?.count ?? 0;

  /// Notify listeners if not closed
  void _notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// Load pending checklists from API
  Future<void> _loadPendingChecklists({
    bool isPagination = false,
    bool isShowLoader = false,
  }) async {
    if (_isClosed) return;

    if (!isPagination) {
      if (isShowLoader) {
        _isLoading = true;
        _notify();
      }
      _pendingChecklists.clear();
      _nextUrl = null;
    }

    _getPendingChecklistsToken?.cancel();
    _getPendingChecklistsToken = CancelToken();

    try {
      final res = await Injector.instance<TripRepository>()
          .getPendingVerificationChecklists(
        ApiRequest(
          path: _nextUrl ?? EndPoints.getPendingVerificationChecklists,
          cancelToken: _getPendingChecklistsToken,
        ),
      );

      if (_isClosed || (_getPendingChecklistsToken?.isCancelled ?? true)) {
        return;
      }
      if (isShowLoader) {
      _isLoading = false;
      }

      res.when(
        success: (data) {
          _pendingVerificationModel = data;
          _nextUrl = data.next;

          if (data.results != null) {
            if (isPagination) {
              _pendingChecklists.addAll(data.results!);
            } else {
              _pendingChecklists = data.results!;
            }
          }

          _notify();
        },
        error: (exception) {
          _notify();
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (_getPendingChecklistsToken?.isCancelled ?? true)) {
        return;
      }
      if (isShowLoader) {
        _isLoading = false;
      }
      _notify();
      '==>> load pending checklists error $e'.logE;
    }
  }

  /// Refresh the checklist data
  Future<void> onRefresh() async {
    await _loadPendingChecklists();
    _refreshController.refreshCompleted();
  }

  /// Load more data for pagination
  Future<void> onLoading() async {
    if (_nextUrl != null) {
      await _loadPendingChecklists(isPagination: true);
    }
    _refreshController.loadComplete();
  }

  /// Get verification status color
  Color getVerificationStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'pending':
        return const Color(0xffFFC107); // Warning yellow
      case 'approved':
        return const Color(0xff28A745); // Success green
      case 'rejected':
        return const Color(0xffDC3545); // Error red
      default:
        return const Color(0xff6C757D); // Gray
    }
  }

  /// Get verification status text
  String getVerificationStatusText(String? status) {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  /// Get checklist type display text
  String getChecklistTypeText(String? type) {
    switch (type?.toLowerCase()) {
      case 'pickup':
        return 'Pickup Checklist';
      case 'delivery':
        return 'Delivery Checklist';
      case 'inspection':
        return 'Inspection Checklist';
      default:
        return type ?? 'Checklist';
    }
  }

  /// Get performed during display text
  String getPerformedDuringText(String? performedDuring) {
    switch (performedDuring?.toLowerCase()) {
      case 'pickup':
        return 'During Pickup';
      case 'delivery':
        return 'During Delivery';
      case 'inspection':
        return 'During Inspection';
      default:
        return performedDuring ?? 'Unknown';
    }
  }
  
  @override
  void dispose() {
    _isClosed = true;
    _getPendingChecklistsToken?.cancel();
    _refreshController.dispose();
    super.dispose();
  }
}
