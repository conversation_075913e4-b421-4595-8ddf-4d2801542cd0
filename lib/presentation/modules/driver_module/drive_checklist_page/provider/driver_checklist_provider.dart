import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/enums/driver_checklist_enums.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/models/pending_verification_model.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Provider for managing driver checklist page state
class DriverChecklistProvider extends ChangeNotifier {
  /// Constructor
  DriverChecklistProvider() {
    _loadPendingChecklists(isShowLoader: true);
  }

  bool _isClosed = false;
  bool _isLoading = false;
  String? _nextUrl;
  PendingVerificationModel? _pendingVerificationModel;
  List<PendingChecklistItem> _pendingChecklists = [];
  CancelToken? _getPendingChecklistsToken;
  final RefreshController _refreshController = RefreshController();

  /// Getters
  bool get isLoading => _isLoading;
  List<PendingChecklistItem> get pendingChecklists => _pendingChecklists;
  RefreshController get refreshController => _refreshController;
  bool get hasMoreData => _nextUrl != null;
  int get totalCount => _pendingVerificationModel?.count ?? 0;

  /// Notify listeners if not closed
  void _notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// Load pending checklists from API
  Future<void> _loadPendingChecklists({
    bool isPagination = false,
    bool isShowLoader = false,
  }) async {
    if (_isClosed) return;

    if (!isPagination) {
      if (isShowLoader) {
        _isLoading = true;
        _notify();
      }
      _pendingChecklists.clear();
      _nextUrl = null;
    }

    _getPendingChecklistsToken?.cancel();
    _getPendingChecklistsToken = CancelToken();

    try {
      final res = await Injector.instance<TripRepository>()
          .getPendingVerificationChecklists(
        ApiRequest(
          path: _nextUrl ?? EndPoints.getPendingVerificationChecklists,
          cancelToken: _getPendingChecklistsToken,
        ),
      );

      if (_isClosed || (_getPendingChecklistsToken?.isCancelled ?? true)) {
        return;
      }
      if (isShowLoader) {
        _isLoading = false;
      }

      res.when(
        success: (data) {
          _pendingVerificationModel = data;
          _nextUrl = data.next;

          if (data.results != null) {
            if (isPagination) {
              _pendingChecklists.addAll(data.results!);
            } else {
              _pendingChecklists = data.results!;
            }
          }

          _notify();
        },
        error: (exception) {
          _notify();
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (_getPendingChecklistsToken?.isCancelled ?? true)) {
        return;
      }
      if (isShowLoader) {
        _isLoading = false;
      }
      _notify();
      '==>> load pending checklists error $e'.logE;
    }
  }

  /// Refresh the checklist data
  Future<void> onRefresh() async {
    await _loadPendingChecklists();
    _refreshController.refreshCompleted();
  }

  /// Load more data for pagination
  Future<void> onLoading() async {
    if (_nextUrl != null) {
      await _loadPendingChecklists(isPagination: true);
    }
    _refreshController.loadComplete();
  }

  /// Get customer verification status color
  Color getCustomerVerificationStatusColor(String? status) {
    final enumStatus = CustomerVerificationStatusExtension.fromString(status);
    if (enumStatus == null) return const Color(0xff6C757D); // Gray

    switch (enumStatus) {
      case CustomerVerificationStatus.PENDING:
        return const Color(0xffFFC107); // Warning yellow
      case CustomerVerificationStatus.ACCEPTED:
        return const Color(0xff28A745); // Success green
    }
  }

  /// Get customer verification status text with localization
  String getCustomerVerificationStatusText(String? status, String locale) {
    final enumStatus = CustomerVerificationStatusExtension.fromString(status);
    return enumStatus?.getLocalizedText(locale) ?? 'Unknown';
  }

  /// Get verification status color (for backward compatibility)
  Color getVerificationStatusColor(String? status) {
    return getCustomerVerificationStatusColor(status);
  }

  /// Get verification status text (for backward compatibility)
  String getVerificationStatusText(String? status) {
    return getCustomerVerificationStatusText(status, 'en');
  }

  /// Get checklist type display text with localization
  String getChecklistTypeText(String? type, String locale) {
    final enumType = DriverCheckListTypeExtension.fromString(type);
    return enumType?.getLocalizedText(locale) ?? (type ?? 'Checklist');
  }

  /// Get performed during display text with localization
  String getPerformedDuringText(String? performedDuring, String locale) {
    final enumPerformed = PerformedByExtension.fromString(performedDuring);
    return enumPerformed?.getLocalizedText(locale) ??
        (performedDuring ?? 'Unknown');
  }

  /// Get checklist type display text (for backward compatibility)
  String getChecklistTypeTextLegacy(String? type) {
    return getChecklistTypeText(type, 'en');
  }

  /// Get performed during display text (for backward compatibility)
  String getPerformedDuringTextLegacy(String? performedDuring) {
    return getPerformedDuringText(performedDuring, 'en');
  }
  
  @override
  void dispose() {
    _isClosed = true;
    _getPendingChecklistsToken?.cancel();
    _refreshController.dispose();
    super.dispose();
  }
}
