// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

class OtpPinPutWidget extends StatefulWidget {
  const OtpPinPutWidget({required this.otpTextController, super.key});

  final TextEditingController otpTextController;

  @override
  State<OtpPinPutWidget> createState() => _OtpPinPutWidgetState();
}

class _OtpPinPutWidgetState extends State<OtpPinPutWidget> {
  final focusNode = FocusNode();

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  bool showError = false;

  @override
  Widget build(BuildContext context) {
    const borderColor = AppColors.primaryColor;
    const errorColor = Color.fromRGBO(255, 234, 238, 1);
    const fillColor = Color.fromRGBO(222, 231, 240, .57);
    final defaultPinTheme = PinTheme(
      width: AppSize.h52,
      height: AppSize.h52,
      margin: EdgeInsets.only(right: AppSize.w8),
      textStyle: context.textTheme.bodySmall?.copyWith(
        fontSize: AppSize.sp18,
        fontWeight: FontWeight.w600,
      ),
      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.circular(AppSize.r4),
        border: Border.all(color: AppColors.ffDEE2E6, width: 1.5),
      ),
    );

    return SizedBox(
      height: AppSize.h58,
      child: Pinput(
        controller: widget.otpTextController,
        focusNode: focusNode,
        defaultPinTheme: defaultPinTheme,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        preFilledWidget: Text(
          '0',
          style: context.textTheme.bodySmall?.copyWith(
            color: AppColors.ffADB5BD,
            fontSize: AppSize.sp16,
          ),
        ),
        onCompleted: (pin) {},
        focusedPinTheme: defaultPinTheme.copyWith(
          height: AppSize.h54,
          width: AppSize.h54,
          decoration: defaultPinTheme.decoration!.copyWith(
            border: Border.all(color: borderColor, width: 1.5),
          ),
        ),
        submittedPinTheme: defaultPinTheme.copyWith(
          decoration: defaultPinTheme.decoration!.copyWith(
            border: Border.all(color: borderColor, width: 1.5),
          ),
        ),
        errorPinTheme: defaultPinTheme.copyWith(
          decoration: BoxDecoration(
            color: errorColor,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
