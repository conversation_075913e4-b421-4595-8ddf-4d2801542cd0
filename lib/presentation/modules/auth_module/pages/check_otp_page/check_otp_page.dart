// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/models/check_otp_params.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/provider/check_otp_provider.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/widgets/otp_pinput_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class CheckOtpPage extends StatelessWidget {
  const CheckOtpPage({
    required this.checkOtpParams,
    super.key,
  });
  final CheckOtpParams checkOtpParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '',
        backgroundColor: AppColors.white,
      ),
      body: ChangeNotifierProvider(
        create: (context) => CheckOtpProvider(),
        child: Consumer<CheckOtpProvider>(
          builder: (context, checkOtpProvider, _) {
            return GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: checkOtpProvider.isShowLoader,
                builder: (context, isLoading, child) {
                  return AppLoader(
                    isShowLoader: isLoading,
                    child: child!,
                  );
                },
                child: AppPadding.symmetric(
                  horizontal: AppSize.appPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Gap(AppSize.h30),
                      Text(
                        context.l10n.checkYourEmail,
                        style: context.textTheme.headlineLarge?.copyWith(
                          fontSize: AppSize.sp24,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Gap(AppSize.h8),
                      RichText(
                        strutStyle: const StrutStyle(height: 1.8),
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: context.l10n.sentVerificationCode,
                              style: context.textTheme.bodySmall?.copyWith(
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            TextSpan(
                              text: checkOtpParams.email,
                              style: context.textTheme.bodySmall?.copyWith(
                                color: AppColors.ff67509C,
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      AppPadding.symmetric(
                        vertical: AppSize.h30,
                        child: OtpPinPutWidget(
                          otpTextController: checkOtpProvider.otpTextController,
                        ),
                      ),
                      AppButton(
                        text: context.l10n.verifyOtp,
                        onPressed: () {
                          if (checkOtpProvider.otpTextController.text
                              .trim()
                              .isEmpty) {
                            context.l10n.pleaseEnterOtp.showErrorAlert();
                          } else if (checkOtpProvider.otpTextController.text
                                  .trim()
                                  .length ==
                              4) {
                            checkOtpProvider.verifyOtpApiCall(
                              email: checkOtpParams.email,
                              context: context,
                              isFromForgotPassword:
                                  checkOtpParams.isFromForgotPassword,
                              isFromSignup: checkOtpParams.isFromSignup,
                            );
                          } else {
                            context.l10n.pleaseEnterValidOtp.showErrorAlert();
                          }
                        },
                      ),
                      AppPadding.symmetric(
                        vertical: AppSize.h16,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: ValueListenableBuilder(
                                valueListenable: checkOtpProvider.otpTimerValue,
                                builder: (context, otpTimerValueV, _) {
                                  return RichText(
                                    text: TextSpan(
                                      text: context.l10n.didNotReceiveEmail,
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        fontSize: AppSize.sp14,
                                        color: AppColors.black,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      children: <TextSpan>[
                                        TextSpan(
                                          text: (checkOtpProvider
                                                      .otpTimer?.isActive ??
                                                  false)
                                              ? '00:${(checkOtpProvider.otpTimeout - (checkOtpProvider.otpTimerValue.value)).toString().padLeft(2, '0')}'
                                              : context.l10n.clickToResend,
                                          style: context.textTheme.bodySmall
                                              ?.copyWith(
                                            color: AppColors.primaryColor,
                                            fontWeight: FontWeight.w700,
                                            fontSize: AppSize.sp14,
                                          ),
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = (checkOtpProvider
                                                        .otpTimer?.isActive ??
                                                    false)
                                                ? null
                                                : () async {
                                                    await checkOtpProvider
                                                        .resendOtpApiCall(
                                                      context: context,
                                                      email:
                                                          checkOtpParams.email,
                                                      isFromForgotPassword:
                                                          checkOtpParams
                                                              .isFromForgotPassword,
                                                    );
                                                    checkOtpProvider.otpTimer =
                                                        Timer.periodic(
                                                      const Duration(
                                                        seconds: 1,
                                                      ),
                                                      checkOtpProvider
                                                          .otpTimerCallback,
                                                    );
                                                  },
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
