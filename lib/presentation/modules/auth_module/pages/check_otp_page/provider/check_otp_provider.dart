// ignore_for_file: public_member_api_docs

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class CheckOtpProvider extends ChangeNotifier {
  CheckOtpProvider() {
    otpTimer = Timer.periodic(
      const Duration(seconds: 1),
      otpTimerCallback,
    );
  }

  bool isClosed = false;
  final TextEditingController otpTextController = TextEditingController();
  final ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? verifyOtpCancelToken;
  CancelToken? resendCancelToken;

  final int otpTimeout = 59;
  Timer? otpTimer;
  final ValueNotifier<int> otpTimerValue = ValueNotifier(0);

  void otpTimerCallback(Timer timer) {
    if (isClosed) return;
    try {
      if (timer.tick >= otpTimeout) {
        otpTimer?.cancel();
        otpTimerValue.value = 0;
      } else {
        otpTimerValue.value = timer.tick;
      }
    } catch (e) {
      e.toString().logE;
    }
  }

  void resetTheTimer() {
    if (isClosed) return;
    try {
      otpTimer?.cancel();
      otpTimerValue.value = 0;
      otpTextController.clear();
    } catch (e) {
      e.toString().logE;
    }
  }

  Future<void> verifyOtpApiCall({
    required String email,
    required BuildContext context,
    required bool isFromForgotPassword,
    required bool isFromSignup,
  }) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      verifyOtpCancelToken?.cancel();
      verifyOtpCancelToken = CancelToken();
      final deviceId = Injector.instance<AppProvider>().deviceId;
      final registrationId = await AppCommonFunctions.getFcmToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: email.trim().toLowerCase(),
        ApiKeys.verificationCode: otpTextController.text.trim(),
        ApiKeys.deviceId: deviceId,
        ApiKeys.deviceType: Platform.isAndroid
            ? DeviceType.ANDROID.name.toUpperCase()
            : Platform.isIOS
                ? DeviceType.IOS.name.toUpperCase()
                : 'UNKNOWN',
        ApiKeys.registrationId: registrationId,
      };
      final request = ApiRequest(
        path: EndPoints.verifyOtp,
        data: data,
        cancelToken: verifyOtpCancelToken,
      );
      final res =
          await Injector.instance<AccountRepository>().verifyOtp(request);
      await res.when(
        success: (data) async {
          if (isClosed || (verifyOtpCancelToken?.isCancelled ?? true)) {
            return;
          }
          final db = Injector.instance<AppDB>();
          isShowLoader.value = false;
          db.refreshToken = data.refresh ?? '';
          final user = db.userModel?.user;
          resetTheTimer();
          if (isFromForgotPassword) {
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.authResetPasswordScreen,
            );
          } else if (isFromSignup ||
              (data.access.isNotEmptyAndNotNull &&
                  (db.userModel != null &&
                      user != null &&
                      user.userDetailData != null &&
                      (user.userDetailData!.commercialName.isEmptyOrNull ||
                          user.userDetailData!.companyName.isEmptyOrNull ||
                          user.userDetailData!.taxId.isEmptyOrNull)))) {
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.authSignupProviderInfoScreen,
            );
          } else {
            if (data.user?.role == UserType.Driver.name) {
              await AppNavigationService.pushNamed(
                context,
                AppRoutes.authResetPasswordScreen,
              );
            } else {
              db.token = data.access ?? '';
              await AppNavigationService.pushAndRemoveAllPreviousRoute(
                context,
                AppRoutes.homeBase,
                isBaseRoute: true,
              );
            }
          }
        },
        error: (exception) {
          if (isClosed || (verifyOtpCancelToken?.isCancelled ?? true)) {
            return;
          }
          resetTheTimer();
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (verifyOtpCancelToken?.isCancelled ?? true)) {
        return;
      }
      resetTheTimer();
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  Future<void> resendOtpApiCall({
    required String email,
    required BuildContext context,
    required bool isFromForgotPassword,
  }) async {
    if (isClosed) return;
    try {
      resetTheTimer();
      isShowLoader.value = true;
      resendCancelToken?.cancel();
      resendCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: email.trim().toLowerCase(),
      };
      final request = ApiRequest(
        path: isFromForgotPassword
            ? EndPoints.sendForgotPasswordOtp
            : EndPoints.resendOtp,
        data: data,
        cancelToken: resendCancelToken,
      );
      final res =
          await Injector.instance<AccountRepository>().resendOtp(request);
      await res.when(
        success: (data) async {
          if (isClosed || (resendCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          context.l10n.oTPSentSuccessfully.showSuccessAlert();
        },
        error: (exception) {
          if (isClosed || (resendCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (resendCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  @override
  void dispose() {
    isClosed = true;
    otpTimer?.cancel();
    verifyOtpCancelToken?.cancel();
    resendCancelToken?.cancel();
    otpTextController.dispose();
    isShowLoader.dispose();
    otpTimerValue.dispose();
    super.dispose();
  }
}
