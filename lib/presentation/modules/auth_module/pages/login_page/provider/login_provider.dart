// ignore_for_file: public_member_api_docs

import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/add_stripe_web_view_page/models/add_stripe_webview_params.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/models/check_otp_params.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/signup_successfully_page/models/signup_success_params.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/enums.dart';

/// Login Provider
class LoginProvider extends ChangeNotifier {
  bool isClosed = false;
  final ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  final emailController =
      TextEditingController(text: kDebugMode ? '<EMAIL>' : '');
  final passwordController =
      TextEditingController(text: kDebugMode ? 'Test@123' : '');
  final GlobalKey<FormState> formKeyLogin = GlobalKey<FormState>();

  final ValueNotifier<bool> isPasswordShow = ValueNotifier(false);
  CancelToken? loginCancelToken;

  Future<void> loginAPICall({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      loginCancelToken?.cancel();
      loginCancelToken = CancelToken();
      final deviceId = Injector.instance<AppProvider>().deviceId;
      final registrationId = Platform.isIOS && kDebugMode
          ? 'registrationId'
          : await AppCommonFunctions.getFcmToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: emailController.text.trim().toLowerCase(),
        ApiKeys.password: passwordController.text.trim(),
        ApiKeys.deviceId: deviceId,
        ApiKeys.deviceType: Platform.isAndroid
            ? DeviceType.ANDROID.name.toUpperCase()
            : Platform.isIOS
                ? DeviceType.IOS.name.toUpperCase()
                : 'UNKNOWN',
        ApiKeys.registrationId: registrationId,
      };
      final request = ApiRequest(
        path: EndPoints.signin,
        data: data,
        cancelToken: loginCancelToken,
      );
      final res = await Injector.instance<AccountRepository>().login(request);
      await res.when(
        success: (data) async {
          if (isClosed || (loginCancelToken?.isCancelled ?? true)) {
            return;
          }
          log('--->> data: ${data.toJson()}');
          isShowLoader.value = false;
          Injector.instance<AppDB>().userModel = data;
          if (data.access != null && data.refresh != null) {
            Injector.instance<AppDB>().token = data.access ?? '';
            Injector.instance<AppDB>().refreshToken = data.refresh ?? '';
            if (data.user?.role != UserType.Driver.name) {
              if (data.user?.userDetailData?.isProfileCompleted ?? false) {
                if (data.user?.userDetailData?.isAccountActivated ?? false) {
                  if (data.user?.userDetailData?.accountVerificationStatus
                          ?.toLowerCase() ==
                      'pending') {
                    await AppNavigationService.pushNamed(
                      context,
                      AppRoutes.authSignupSuccessScreen,
                      extra: SignupSuccessParams(
                        isFromLogin: true,
                      ),
                    );
                  } else {
                    await AppNavigationService.pushAndRemoveAllPreviousRoute(
                      context,
                      AppRoutes.homeBase,
                      isBaseRoute: true,
                    );
                  }
                } else {
                  await activateStripeAccount(context);
                }
              } else {
                await AppNavigationService.pushNamed(
                  context,
                  AppRoutes.authSignupProviderInfoScreen,
                );
              }
            } else {
              if (data.user?.isFirstTimeLogin ?? false) {
                await AppNavigationService.pushNamed(
                  context,
                  AppRoutes.authResetPasswordScreen,
                );
              } else {
                await AppNavigationService.pushAndRemoveAllPreviousRoute(
                  context,
                  AppRoutes.homeBase,
                  isBaseRoute: true,
                );
              }
            }
          } else {
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.authCheckOtpScreen,
              extra: CheckOtpParams(
                email: emailController.text.trim().toLowerCase(),
              ),
              //  {
              //   'email': emailController.text.trim().toLowerCase(),
              //   'isFromForgotPassword': false,
              //   'isFromSignup': false,
              // },
            );
          }
        },
        error: (exception) {
          if (isClosed || (loginCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (loginCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  CancelToken? activateStripeAccountToken;
  Future<void> activateStripeAccount(BuildContext context) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      activateStripeAccountToken?.cancel();
      activateStripeAccountToken = CancelToken();

      final request = ApiRequest(
        path: EndPoints.activateStripeAccount,
        cancelToken: activateStripeAccountToken,
      );

      final res = await Injector.instance<AccountRepository>()
          .activateStripeAccount(request);
      res.when(
        success: (data) {
          if (isClosed || (activateStripeAccountToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          final url = data['onboarding_url']['url'] as String?;
          if (url.isNotEmptyAndNotNull) {
            AppNavigationService.pushNamed(
              context,
              AppRoutes.authAddStripeWebview,
              extra: AddStripeWebviewParams(url: url ?? ''),
            );
          }
        },
        error: (exception) {
          if (isClosed || (activateStripeAccountToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (activateStripeAccountToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  @override
  void dispose() {
    isClosed = true;
    loginCancelToken?.cancel();
    activateStripeAccountToken?.cancel();
    isShowLoader.dispose();
    isPasswordShow.dispose();
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }
}
