import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/signup_successfully_page/models/signup_success_params.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';

/// Signup successfully Screen
class SignupSuccessFullyPage extends StatelessWidget {
  /// Signup successfully Screen
  const SignupSuccessFullyPage({
    super.key,
    required this.signupSuccessParams,
  });

  final SignupSuccessParams signupSuccessParams;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Scaffold(
      body: AppPadding.symmetric(
        horizontal: AppSize.appPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(
              height: AppSize.h64,
              child: AppAssets.iconsDoneGreenIcon.image(),
            ),
            Gap(AppSize.h15),
            Text(
              signupSuccessParams.isFromLogin
                  ? l10n.loginSuccessfully
                  : l10n.registeredSuccessfully,
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: AppSize.sp20,
                color: AppColors.black,
              ),
            ),
            Gap(AppSize.h10),
            Text(
              signupSuccessParams.isFromLogin
                  ? l10n.yourAccountInReview
                  : l10n
                      .yourAccountIsRegisteredSuccessfullyAndUnderReviewOnceAdminWillApproveThenYouWillAbleToAccessTheApp,
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: AppSize.sp16,
                color: AppColors.ff6C757D,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
