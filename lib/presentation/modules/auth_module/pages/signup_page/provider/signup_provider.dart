// ignore_for_file: public_member_api_docs

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/models/check_otp_params.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';

import 'package:transportmatch_provider/utils/enums.dart';

/// Signup Provider
class SignupProvider extends ChangeNotifier {
  bool isClosed = false;
  final ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> formKeySignup = GlobalKey<FormState>();

  final ValueNotifier<bool> isPasswordShow = ValueNotifier(false);
  final ValueNotifier<bool> isConfirmPasswordShow = ValueNotifier(false);
  CancelToken? registerCancelToken;

  Future<void> registerAPICall({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      registerCancelToken?.cancel();
      registerCancelToken = CancelToken();
      final deviceId = Injector.instance<AppProvider>().deviceId;
      final registrationId = await AppCommonFunctions.getFcmToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: emailController.text.trim().toLowerCase(),
        ApiKeys.firstName: nameController.text.trim(),
        ApiKeys.password: passwordController.text.trim(),
        ApiKeys.deviceId: deviceId,
        ApiKeys.deviceType: Platform.isAndroid
            ? DeviceType.ANDROID.name.toUpperCase()
            : Platform.isIOS
                ? DeviceType.IOS.name.toUpperCase()
                : 'UNKNOWN',
        ApiKeys.registrationId: registrationId,
        ApiKeys.role: 'PROVIDER',
      };
      final request = ApiRequest(
        path: EndPoints.signup,
        data: data,
        cancelToken: registerCancelToken,
      );
      final res = await Injector.instance<AccountRepository>().signUp(request);
      await res.when(
        success: (data) async {
          if (isClosed || (registerCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          Injector.instance<AppDB>().userModel = data;
          context.l10n.accountRegisterSuccessfully.showSuccessAlert();
          await AppNavigationService.pushNamed(
            context,
            AppRoutes.authCheckOtpScreen,
            extra: CheckOtpParams(
              email: emailController.text.trim().toLowerCase(),
              isFromSignup: true,
            ),
            // {
            //   'email': emailController.text.trim().toLowerCase(),
            //   'isFromForgotPassword': false,
            //   'isFromSignup': true,
            // },
          );
        },
        error: (exception) {
          if (isClosed || (registerCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (registerCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  @override
  void dispose() {
    isClosed = true;
    registerCancelToken?.cancel();
    isShowLoader.dispose();
    isPasswordShow.dispose();
    isConfirmPasswordShow.dispose();
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }
}
