// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/add_stripe_web_view_page/models/add_stripe_webview_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';

/// Signup Provider
class SignupProviderInfoProvider extends ChangeNotifier {
  bool isClosed = false;
  final ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  final TextEditingController companyNameController = TextEditingController();
  final TextEditingController commercialNameController =
      TextEditingController();
  final TextEditingController taxIDController = TextEditingController();
  final TextEditingController webPageController = TextEditingController();
  final GlobalKey<FormState> formKeySignupProvider = GlobalKey<FormState>();

  CancelToken? providerInfoCancelToken;
  Future<void> providerInfoApiCall({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      providerInfoCancelToken?.cancel();
      providerInfoCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.commercialName:
            commercialNameController.text.trim().toLowerCase(),
        ApiKeys.companyName: companyNameController.text.trim(),
        ApiKeys.taxId: taxIDController.text.trim(),
        ApiKeys.webPage: webPageController.text.trim().toLowerCase(),
      };
      final request = ApiRequest(
        path:
            '${EndPoints.providerInfo}${Injector.instance<AppDB>().userModel?.user?.userDetailData?.id}/',
        data: data,
        cancelToken: providerInfoCancelToken,
      );
      final res =
          await Injector.instance<AccountRepository>().addProviderInfo(request);
      await res.when(
        success: (data) async {
          if (isClosed || (providerInfoCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          await activateStripeAccount(context);
          Injector.instance<AppDB>().providerInfoModel = data;
          // await AppNavigationService.pushNamed(
          //   context,
          //   const SignupSuccessfullyScreen(),
          // );
        },
        error: (exception) async {
          if (isClosed || (providerInfoCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (providerInfoCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  CancelToken? activateStripeAccountToken;
  Future<void> activateStripeAccount(BuildContext context) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      activateStripeAccountToken?.cancel();
      activateStripeAccountToken = CancelToken();

      final request = ApiRequest(
        path: EndPoints.activateStripeAccount,
        cancelToken: activateStripeAccountToken,
      );

      final res = await Injector.instance<AccountRepository>()
          .activateStripeAccount(request);
      res.when(
        success: (data) {
          if (isClosed || (activateStripeAccountToken?.isCancelled ?? true)) {
            return;
          }
          Injector.instance<AppDB>().token = '';
          Injector.instance<AppDB>().refreshToken = '';
          isShowLoader.value = false;
          final url = data['onboarding_url']['url'] as String?;
          if (url.isNotEmptyAndNotNull) {
            // Replace:
            // AppNavigationService.pushAndRemoveAllScreen(
            //   context,
            //   const BottomBarScreen(),
            // );

            // With:
            AppNavigationService.pushNamed(
              context,
              AppRoutes.authAddStripeWebview,
              extra: AddStripeWebviewParams(url: url ?? ''),
            );
          }
        },
        error: (exception) {
          if (isClosed || (activateStripeAccountToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (activateStripeAccountToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  @override
  void dispose() {
    isClosed = true;
    providerInfoCancelToken?.cancel();
    activateStripeAccountToken?.cancel();
    isShowLoader.dispose();
    companyNameController.dispose();
    commercialNameController.dispose();
    taxIDController.dispose();
    webPageController.dispose();
    super.dispose();
  }
}
