import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/signup_provider_info_page/signup_provider_info_provider/signup_provider_info_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// SignupProviderInfoScreen
class SignupProviderInfoPage extends StatelessWidget {
  /// SignupProviderInfoScreen
  const SignupProviderInfoPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider<SignupProviderInfoProvider>(
      create: (context) => SignupProviderInfoProvider(),
      child: Consumer<SignupProviderInfoProvider>(
        builder: (context, signupProviderInfo, _) {
          return Scaffold(
            appBar: const CustomAppBar(
              title: '',
              canPop: false,
              backgroundColor: AppColors.white,
            ),
            body: ValueListenableBuilder(
              valueListenable: signupProviderInfo.isShowLoader,
              builder: (context, isLoading, child) {
                return AppLoader(
                  isShowLoader: isLoading,
                  child: GestureDetector(
                    onTap: AppCommonFunctions.closeKeyboard,
                    child: AppPadding.symmetric(
                      horizontal: AppSize.appPadding,
                      child: SizedBox(
                        height: context.height,
                        child: Form(
                          key: signupProviderInfo.formKeySignupProvider,
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Gap(AppSize.h30),

                                Text(
                                  l10n.signUp,
                                  style: context.textTheme.headlineLarge
                                      ?.copyWith(fontSize: AppSize.sp24),
                                ),
                                Gap(AppSize.h8),

                                Text(
                                  l10n.pleaseEnterYourDetailsToCreateAnAccount,
                                  style: context.textTheme.bodySmall?.copyWith(
                                    fontSize: AppSize.sp16,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                Gap(AppSize.h40),
                                //* Company Name
                                AppTextFormField(
                                  title: l10n.companyName,
                                  hintText: l10n.enterYourCompanyName,
                                  fillColor: AppColors.ffF8F9FA,
                                  textAction: TextInputAction.next,
                                  controller:
                                      signupProviderInfo.companyNameController,
                                  validator: (p0) =>
                                      companyNameValidator().call(p0),
                                ),
                                Gap(AppSize.h28),
                                //* Commercial Name
                                AppTextFormField(
                                  title: l10n.commercialName,
                                  hintText: l10n.enterCommercialName,
                                  fillColor: AppColors.ffF8F9FA,
                                  textAction: TextInputAction.next,
                                  controller: signupProviderInfo
                                      .commercialNameController,
                                  validator: (p0) =>
                                      commercialNameValidator().call(p0),
                                ),
                                Gap(AppSize.h28),
                                //* Tax ID
                                AppTextFormField(
                                  title: l10n.taxID,
                                  hintText: l10n.enterTaxID,
                                  fillColor: AppColors.ffF8F9FA,
                                  textAction: TextInputAction.next,
                                  controller:
                                      signupProviderInfo.taxIDController,
                                  validator: (p0) => taxIDValidator().call(p0),
                                ),
                                Gap(AppSize.h28),
                                //* Webpage
                                Row(
                                  children: [
                                    Text(
                                      '  ${l10n.webpage}',
                                      textAlign: TextAlign.center,
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                        fontWeight: FontWeight.w500,
                                        fontSize: AppSize.sp14,
                                        color: AppColors.black,
                                      ),
                                    ),
                                    Text(
                                      ' (${l10n.optional})',
                                      textAlign: TextAlign.center,
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                        fontWeight: FontWeight.w500,
                                        fontSize: AppSize.sp14,
                                        color: AppColors.ffADB5BD,
                                      ),
                                    ),
                                  ],
                                ),
                                AppPadding(
                                  top: AppSize.h4,
                                  child: AppTextFormField(
                                    hintText: l10n.enterYourWebpageURL,
                                    fillColor: AppColors.ffF8F9FA,
                                    controller:
                                        signupProviderInfo.webPageController,
                                  ),
                                ),

                                //* Create an account
                                AppPadding(
                                  top: AppSize.h40,
                                  bottom: AppSize.h16,
                                  child: AppButton(
                                    text: l10n.createAnAccount,
                                    onPressed: () {
                                      if (signupProviderInfo
                                          .formKeySignupProvider.currentState!
                                          .validate()) {
                                        signupProviderInfo.providerInfoApiCall(
                                          context: context,
                                        );
                                      }
                                    },
                                  ),
                                ),

                                //* Navigate Login screen
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      l10n.alreadyHaveAnAccount,
                                      textAlign: TextAlign.center,
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                        fontWeight: FontWeight.w500,
                                        fontSize: AppSize.sp14,
                                        color: AppColors.black,
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        AppNavigationService
                                            .pushAndRemoveAllPreviousRoute(
                                          context,
                                          AppRoutes.authBase,
                                          isBaseRoute: true,
                                        );
                                      },
                                      child: Text(
                                        ' ${l10n.logIn}',
                                        textAlign: TextAlign.center,
                                        style: context.textTheme.bodyLarge
                                            ?.copyWith(
                                          fontWeight: FontWeight.w700,
                                          fontSize: AppSize.sp14,
                                          color: AppColors.primaryColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Gap(AppSize.h10),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
