// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/check_otp_page/models/check_otp_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';

class ForgotPasswordProvider extends ChangeNotifier {
  bool isClosed = false;
  final TextEditingController emailController = TextEditingController();
  final GlobalKey<FormState> formKeyForgotPassword = GlobalKey<FormState>();
  final ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? forgotPasswordCancelToken;

  Future<void> forgotPasswordAPIcall({required BuildContext context}) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      forgotPasswordCancelToken?.cancel();
      forgotPasswordCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: emailController.text.trim().toLowerCase(),
      };
      final request = ApiRequest(
        path: EndPoints.sendForgotPasswordOtp,
        data: data,
        cancelToken: forgotPasswordCancelToken,
      );
      final res = await Injector.instance<AccountRepository>()
          .forgotPasswordSendOtp(request);
      await res.when(
        success: (data) async {
          if (isClosed || (forgotPasswordCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          await AppNavigationService.pushNamed(
            context,
            AppRoutes.authCheckOtpScreen,
            extra: CheckOtpParams(
              email: emailController.text.trim().toLowerCase(),
              isFromForgotPassword: true,
            ),
            // {
            //   'email': emailController.text.trim().toLowerCase(),
            //   'isFromForgotPassword': true,
            //   'isFromSignup': false,
            // },
          );
        },
        error: (exception) {
          if (isClosed || (forgotPasswordCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (forgotPasswordCancelToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.toString().showErrorAlert();
    }
  }

  @override
  void dispose() {
    isClosed = true;
    forgotPasswordCancelToken?.cancel();
    isShowLoader.dispose();
    emailController.dispose();
    super.dispose();
  }
}
