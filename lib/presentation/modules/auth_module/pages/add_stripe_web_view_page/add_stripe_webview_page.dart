// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/add_stripe_web_view_page/models/add_stripe_webview_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AddStripeWebViewPage extends StatefulWidget {
  const AddStripeWebViewPage({
    super.key,
    required this.addStripeWebviewParams,
  });
  final AddStripeWebviewParams addStripeWebviewParams;

  @override
  State<AddStripeWebViewPage> createState() => _AddStripeWebViewPageState();
}

class _AddStripeWebViewPageState extends State<AddStripeWebViewPage> {
  WebViewController? controller;
  bool _hasNavigated = false;
  final ValueNotifier<bool> _isShowLoader = ValueNotifier(true);

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      l10n.pleaseWaitWhileWeRedirectingYouToStripPortalForVerification
          .showInfoAlert(duration: const Duration(seconds: 5));

      controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              _isShowLoader.value = true;
              if (_hasNavigated) return;

              if (url ==
                  'https://staging.d171pkw491jul9.amplifyapp.com/providers/') {
                _hasNavigated = true;

                /// Safe delay to avoid _debugLocked error
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Future.delayed(const Duration(milliseconds: 100), () {
                    if (!mounted) return;
                    Injector.instance<AppDB>().logoutUser();
                    AppNavigationService.pushAndRemoveAllPreviousRoute(
                      context,
                      AppRoutes.authBase,
                      isBaseRoute: true,
                    );
                  });
                });
              }
            },
            onPageFinished: (String url) {
              _isShowLoader.value = false;
            },
          ),
        )
        ..loadRequest(Uri.parse(widget.addStripeWebviewParams.url));

      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (result, _) {
        if (_hasNavigated) return;

        _hasNavigated = true;

        /// Safe delay before navigating
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Future.delayed(const Duration(milliseconds: 100), () {
            if (!mounted) return;
            Injector.instance<AppDB>().logoutUser();
            AppNavigationService.pushAndRemoveAllPreviousRoute(
              context,
              AppRoutes.authBase,
              isBaseRoute: true,
            );
          });
        });
      },
      child: Scaffold(
        body: ValueListenableBuilder(
          valueListenable: _isShowLoader,
          builder: (context, isLoading, _) {
            return AppLoader(
              isShowLoader: isLoading,
              child: SafeArea(
                child: controller != null
                    ? WebViewWidget(controller: controller!)
                    : Center(
                        child: Lottie.asset(
                          AppAssets.animationLoaderAppLoader.path,
                          height: AppSize.h100,
                        ),
                      ),
              ),
            );
          },
        ),
      ),
    );
  }
}

// class AddStripeWebViewPage extends StatefulWidget {
//   const AddStripeWebViewPage({
//     super.key,
//     required this.addStripeWebviewParams,
//   });
//   final AddStripeWebviewParams addStripeWebviewParams;

//   @override
//   State<AddStripeWebViewPage> createState() => _AddStripeWebViewPageState();
// }

// class _AddStripeWebViewPageState extends State<AddStripeWebViewPage> {
//   WebViewController? controller;
//   @override
//   void initState() {
//     WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
//       'Please wait while we redirecting you to strip portal for verification..'
//           .showInfoAlert(duration: const Duration(seconds: 5));
//       controller = WebViewController()
//         ..setJavaScriptMode(JavaScriptMode.unrestricted)
//         ..setNavigationDelegate(
//           NavigationDelegate(
//             onProgress: (int progress) {
//               // Update loading bar.
//             },
//             onPageStarted: (String url) {
//               if (url ==
//                   'https://staging.d171pkw491jul9.amplifyapp.com/providers/') {
//                 Future.microtask(() {
//                   if (!mounted) return;
//                   AppNavigationService.pushAndRemoveAllPreviousRoute(
//                     context,
//                     AppRoutes.authBase,
//                     isBaseRoute: true,
//                   );
//                 });
//               }

//               // if (url.contains('tagline')) {
//               // AppNavigationService.replaceScreen(
//               //   context,
//               //   UserInfoPage(controller: widget.controller),
//               // );
//               // }
//             },
//             onPageFinished: (String url) {},
//             onHttpError: (HttpResponseError error) {},
//             onWebResourceError: (WebResourceError error) {},
//             // onNavigationRequest: (NavigationRequest request) {
//             //   if (request.url.startsWith('https://www.youtube.com/')) {
//             //     return NavigationDecision.prevent;
//             //   }
//             //   return NavigationDecision.navigate;
//             // },
//           ),
//         )
//         ..loadRequest(Uri.parse(widget.addStripeWebviewParams.url));
//       setState(() {});
//     });
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return PopScope(
//       canPop: false,
//       onPopInvokedWithResult: (result, _) {
//         AppNavigationService.pushAndRemoveAllPreviousRoute(
//           context,
//           AppRoutes.authBase,
//           isBaseRoute: true,
//         );
//       },
//       child: Scaffold(
//         body: SafeArea(
//           child: controller != null
//               ? WebViewWidget(controller: controller!)
//               : Center(
//                   child: Lottie.asset(
//                     AppAssets.animationLoaderAppLoader.path,
//                     height: AppSize.h100,
//                   ),
//                 ),
//         ),
//       ),
//     );
//   }
// }
