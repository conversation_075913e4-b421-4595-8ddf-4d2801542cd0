// ignore_for_file: public_member_api_docs

class SignUpAndSignInModel {
  SignUpAndSignInModel({
    this.user,
    this.access,
    this.refresh,
  });

  factory SignUpAndSignInModel.fromJson(Map<String, dynamic> json) {
    return SignUpAndSignInModel(
      user: json['user'] == null
          ? null
          : User.fromJson(Map<String, dynamic>.from(json['user'] as Map)),
      access: json['access']?.toString(),
      refresh: json['refresh']?.toString(),
    );
  }

  final User? user;
  final String? access;
  final String? refresh;

  Map<String, dynamic> toJson() => {
        'user': user?.toJson(),
        'access': access,
        'refresh': refresh,
      };
}

class User {
  User({
    this.id,
    this.userDetailData,
    this.profileUrl,
    this.role,
    this.dateJoined,
    this.firstName,
    this.lastName,
    this.email,
    this.country,
    this.lastLogin,
    this.isOnline,
    this.isEmailVerified,
    this.socketSessionId,
    this.isFirstTimeLogin,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] == null ? -1 : int.tryParse(json['id'].toString()) ?? -1,
      userDetailData: json['user_detail_data'] == null
          ? null
          : UserDetailData.fromJson(
              Map<String, dynamic>.from(json['user_detail_data'] as Map),),
      profileUrl:
          json['profile_url'] == null ? '' : json['profile_url'].toString(),
      role: json['role'] == null ? '' : json['role'].toString(),
      dateJoined: json['date_joined'] == null
          ? null
          : DateTime.tryParse(json['date_joined'].toString()),
      firstName:
          json['first_name'] == null ? '' : json['first_name'].toString(),
      lastName: json['last_name'] == null ? '' : json['last_name'].toString(),
      email: json['email'] == null ? '' : json['email'].toString(),
      country: json['country'] == null ? '' : json['country'].toString(),
      lastLogin:
          json['last_login'] == null ? '' : json['last_login'].toString(),
      isOnline: json['is_online'] == null
          ? false
          : bool.tryParse(json['is_online'].toString()),
      isEmailVerified: json['is_email_verified'] == null
          ? false
          : bool.tryParse(json['is_email_verified'].toString()),
      socketSessionId: json['socket_session_id'] == null
          ? ''
          : json['socket_session_id'].toString(),
      isFirstTimeLogin: json['is_first_time_login'] == null
          ? false
          : bool.tryParse(json['is_first_time_login'].toString()),
    );
  }

  final int? id;
  final UserDetailData? userDetailData;
  final String? profileUrl;
  final String? role;
  final DateTime? dateJoined;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? country;
  final String? lastLogin;
  final bool? isOnline;
  final bool? isEmailVerified;
  final String? socketSessionId;
  final bool? isFirstTimeLogin;

  Map<String, dynamic> toJson() => {
        'id': id,
        'user_detail_data': userDetailData?.toJson(),
        'profile_url': profileUrl,
        'role': role,
        'date_joined': dateJoined?.toIso8601String(),
        'first_name': firstName,
        'last_name': lastName,
        'email': email,
        'country': country,
        'last_login': lastLogin,
        'is_online': isOnline,
        'is_email_verified': isEmailVerified,
        'socket_session_id': socketSessionId,
        'is_first_time_login': isFirstTimeLogin,
      };
}

// THIS IS FOR PROVIDER ONLY, NOT FOR USER
class UserDetailData {
  UserDetailData({
    this.id,
    this.stripeAccountId,
    this.isAccountActivated,
    this.companyName,
    this.commercialName,
    this.taxId,
    this.webPage,
    this.accountVerificationStatus,
    this.isProfileCompleted,
  });

  factory UserDetailData.fromJson(Map<String, dynamic> json) {
    return UserDetailData(
      id: json['id'] == null ? -1 : int.tryParse(json['id'].toString()),
      stripeAccountId: json['stripe_account_id'] == null
          ? ''
          : json['stripe_account_id'].toString(),
      isAccountActivated: json['is_account_activated'] == null
          ? false
          : bool.tryParse(json['is_account_activated'].toString()),
      companyName:
          json['company_name'] == null ? '' : json['company_name'].toString(),
      commercialName: json['commercial_name'] == null
          ? ''
          : json['commercial_name'].toString(),
      taxId: json['tax_id'] == null ? '' : json['tax_id'].toString(),
      webPage: json['web_page'] == null ? '' : json['web_page'].toString(),
      accountVerificationStatus: json['account_verification_status'] == null
          ? ''
          : json['account_verification_status'].toString(),
      isProfileCompleted: json['is_profile_completed'] == null
          ? false
          : bool.tryParse(json['is_profile_completed'].toString()),
    );
  }

  final int? id;
  final String? stripeAccountId;
  final bool? isAccountActivated;
  final String? companyName;
  final String? commercialName;
  final String? taxId;
  final String? webPage;
  final String? accountVerificationStatus;
  final bool? isProfileCompleted;

  Map<String, dynamic> toJson() => {
        'id': id,
        'stripe_account_id': stripeAccountId,
        'is_account_activated': isAccountActivated,
        'company_name': companyName,
        'commercial_name': commercialName,
        'tax_id': taxId,
        'web_page': webPage,
        'account_verification_status': accountVerificationStatus,
        'is_profile_completed': isProfileCompleted,
      };
}
