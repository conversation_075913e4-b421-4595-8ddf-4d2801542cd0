import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/shared/shared_api_calls/refresh_token_api.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Custom Dio network interceptor for managing requests and responses globally
final class DioNetworkInterceptor extends Interceptor {
  /// constructor
  DioNetworkInterceptor({
    // required this.encrypt,
    // required this.deviceLocalUtils,
    required this.isSecure,
  });

  /// encrypt/decrypt utils class
  // final EncryptNetworkData encrypt;

  /// is secure network call or not (for token)
  final bool isSecure;

  /// AppDB
  late final appDB = Injector.instance<AppDB>();

  // late final langCubit = Injector.instance<AppLanguageCubit>();

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // options.headers['accept-language'] = appDB.userSettings?.language ?? 'en';
    // final connectivityResult = await Connectivity().checkConnectivity();
    // if (connectivityResult == ConnectivityResult.none) {
    //   return handler.reject(DioException(requestOptions: options, message: 'No internet connection!'));
    // }
    if (!appDB.isInternetConnected) {
      return handler.reject(
        DioException(
          requestOptions: options,
          // message: 'No internet connection! [${options.path}]',
          message: 'No internet connection!',
          type: DioExceptionType.connectionError,
          response: Response(statusCode: 412, requestOptions: options),
        ),
      );
    }
    // options.headers['device-country-code'] = await deviceLocalUtils.getDeviceLocalCode();
    final apiToken =
        // isSecure
        // ?
        (options.headers['Authorization'] as String?) ?? appDB.token;
    // : '';
    if (apiToken.isNotEmptyAndNotNull) {
      options.headers['Authorization'] = 'Bearer $apiToken';
    } else {
      // options.headers['Authorization'] = '';
    }
    options.headers['Accept'] = 'application/json';
    return handler.next(options);
  }

  // @override
  // Future<void> onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) async {
  //
  //   return handler.next(response);
  // }

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    if (err.error?.toString().contains('ERR_INTERNET_DISCONNECTED') ?? false) {
      return handler.reject(
        DioException(
          requestOptions: err.requestOptions,
          // message: 'No internet connection! [${options.path}]',
          message: 'No internet connection!',
          type: DioExceptionType.connectionError,
          response:
              Response(statusCode: 412, requestOptions: err.requestOptions),
        ),
      );
    }
    if (err.response?.statusCode == 401 &&
        !err.requestOptions.uri.path.contains(EndPoints.refreshToken)) {
      var isSuccessful = false;
      await RefreshTokenApi.refreshToken(
        callback: () => isSuccessful = true,
      );
      if (isSuccessful) {
        'Bearer ${appDB.token}'.logFatal;
        final res =
            await Injector.instance<Dio>(instanceName: 'open').request<dynamic>(
          err.requestOptions.path,
          options: Options(
            method: err.requestOptions.method,
            headers: {
              'Authorization': appDB.token,
            },
            contentType: err.requestOptions.contentType,
            responseType: err.requestOptions.responseType,
            validateStatus: err.requestOptions.validateStatus,
            receiveDataWhenStatusError:
                err.requestOptions.receiveDataWhenStatusError,
            followRedirects: err.requestOptions.followRedirects,
            maxRedirects: err.requestOptions.maxRedirects,
            receiveTimeout: err.requestOptions.receiveTimeout,
            sendTimeout: err.requestOptions.sendTimeout,
            extra: err.requestOptions.extra,
            listFormat: err.requestOptions.listFormat,
          ),
          data: err.requestOptions.data,
          queryParameters: err.requestOptions.queryParameters,
          onReceiveProgress: err.requestOptions.onReceiveProgress,
          onSendProgress: err.requestOptions.onSendProgress,
        );
        if (res.statusCode?.clamp(200, 299) == res.statusCode) {
          handler.resolve(res);
          return;
        } else {
          if (err.type != DioExceptionType.cancel) handler.reject(err);
          return;
        }
      }
    }
    if (err.response?.statusCode?.clamp(500, 599) == err.response?.statusCode) {
      // final message = err.response?.statusMessage;
      // if (message.isNotEmptyAndNotNull) Alert.instance.showMessage(message!);
      err.response?.data = <String, dynamic>{};
      err.response?.statusMessage =
          'Oops, something went wrong. Please try again';
      // err.response?.statusMessage = 'Oops, something went wrong. Please try again [${err.requestOptions.path}]';
    }
    if (err.type != DioExceptionType.cancel) handler.next(err);
  }
}
