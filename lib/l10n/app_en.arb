{"@@locale": "en", "just_now": "Just now", "@just_now": {}, "min_ago": "Minute ago", "@min_ago": {}, "hour_ago": "Hour ago", "@hour_ago": {}, "day_ago": "Day ago", "@day_ago": {}, "week_ago": "Week ago", "@week_ago": {}, "year_ago": "Year ago", "@year_ago": {}, "today": "Today", "@today": {}, "language": "Language", "@language": {}, "yesterday": "Yesterday", "@yesterday": {}, "copied": "<PERSON>pied", "@copied": {}, "weHaveSentAVerificationCodeOn": "We've sent a verification code on ", "@weHaveSentAVerificationCodeOn": {}, "verifyOTP": "Verify OTP", "@verifyOTP": {}, "pleaseEnterOTP": "Please enter OTP!", "@pleaseEnterOTP": {}, "pleaseEnterValidOTP": "Please enter valid OTP!", "@pleaseEnterValidOTP": {}, "didnNotReceivedTheEmail": "Didn't received the email? ", "@didnNotReceivedTheEmail": {}, "accountRegisterSuccessfully": "Account registered successfully, Please verify your email.", "@accountRegisterSuccessfully": {}, "weWillSendYouResetPasswordInstructions": "We'll send you reset password instructions", "@weWillSendYouResetPasswordInstructions": {}, "sentAnEmail": "Sent an email", "@sentAnEmail": {}, "theUltimateStressFreeCarTransportExperience": "The ultimate stress-free car\ntransport experience.", "@theUltimateStressFreeCarTransportExperience": {}, "doNotHaveAnAccount": "Don't have an account?", "@doNotHaveAnAccount": {}, "pleaseEnterYourDetailsToCreateAnAccount": "Please enter your details to create an account", "@pleaseEnterYourDetailsToCreateAnAccount": {}, "companyName": "Company Name", "@companyName": {}, "enterYourCompanyName": "Enter your company name", "@enterYourCompanyName": {}, "commercialName": "Commercial Name", "@commercialName": {}, "enterCommercialName": "Enter commercial name", "@enterCommercialName": {}, "taxID": "Tax ID", "@taxID": {}, "enterTaxID": "Enter Tax ID", "@enterTaxID": {}, "webpage": "Webpage", "@webpage": {}, "optional": "Optional", "@optional": {}, "enterYourWebpageURL": "Enter your webpage URL", "@enterYourWebpageURL": {}, "createAnAccount": "Create an account", "@createAnAccount": {}, "alreadyHaveAnAccount": "Already have an account?", "@alreadyHaveAnAccount": {}, "registeredSuccessfully": "Registered Successfully", "@registeredSuccessfully": {}, "yourAccountIsRegisteredSuccessfullyAndUnderReviewOnceAdminWillApproveThenYouWillAbleToAccessTheApp": "Your account is registered successfully and under review, once admin will approve then you will able to access the app", "@yourAccountIsRegisteredSuccessfullyAndUnderReviewOnceAdminWillApproveThenYouWillAbleToAccessTheApp": {}, "next": "Next", "@next": {}, "addDriver": "Add Driver", "@addDriver": {}, "driverInfo": "Driver info", "@driverInfo": {}, "userID": "User ID", "@userID": {}, "enterIDYouWantToAssign": "Enter ID you want to assign", "@enterIDYouWantToAssign": {}, "enterDriversName": "Enter driver's name", "@enterDriversName": {}, "enterDriversEmail": "Enter driver's email", "@enterDriversEmail": {}, "setPasswordForTheDriver": "Set password for the driver", "@setPasswordForTheDriver": {}, "allDrivers": "All Drivers", "@allDrivers": {}, "dateAdded": "Date Added", "@dateAdded": {}, "allEquipments": "All Equipments", "@allEquipments": {}, "type": "Type", "@type": {}, "brand": "Brand", "@brand": {}, "year": "Year", "@year": {}, "editDetails": "Edit Details", "@editDetails": {}, "changePassword": "Change Password", "@changePassword": {}, "transactions": "Transactions", "@transactions": {}, "customerSupport": "Customer Support", "@customerSupport": {}, "signOut": "Sign Out", "@signOut": {}, "deleteAccount": "Delete Account", "@deleteAccount": {}, "oldPassword": "Old Password", "@oldPassword": {}, "newPassword": "New Password", "@newPassword": {}, "from": "From", "@from": {}, "completed": "Completed", "@completed": {}, "unsuccessful": "Unsuccessful", "@unsuccessful": {}, "passwordShouldBeAtLeast8Characters": "Password should be at least 8 characters.", "@passwordShouldBeAtLeast8Characters": {}, "newPasswordAndOldPasswordCannotBeSame": "New password and old password cannot be same", "@newPasswordAndOldPasswordCannotBeSame": {}, "pleaseEnterCompanyName": "Please enter company name", "@pleaseEnterCompanyName": {}, "pleaseEnterCommercialName": "Please enter commercial name", "@pleaseEnterCommercialName": {}, "pleaseEnterTaxID": "Please enter Tax ID", "@pleaseEnterTaxID": {}, "pleaseEnterUserID": "Please enter user ID", "@pleaseEnterUserID": {}, "addEquipment": "Add equipment", "@addEquipment": {}, "equipmentInfo": "Equipment info", "@equipmentInfo": {}, "equipmentName": "Equipment Name", "@equipmentName": {}, "enterEquipmentName": "Enter equipment name", "@enterEquipmentName": {}, "pleaseEnterEquipmentName": "Please enter equipment name", "@pleaseEnterEquipmentName": {}, "saveEquipment": "Save Equipment", "@saveEquipment": {}, "equipmentType": "Equipment Type", "@equipmentType": {}, "equipmentBrand": "Equipment Brand", "@equipmentBrand": {}, "chooseVehicleBrand": "Choose vehicle brand", "@chooseVehicleBrand": {}, "equipmentYear": "Equipment Year", "@equipmentYear": {}, "chooseVehicleYear": "Choose vehicle year", "@chooseVehicleYear": {}, "equipmentVersion": "Equipment Version", "@equipmentVersion": {}, "chooseVehicleVersion": "Choose vehicle version", "@chooseVehicleVersion": {}, "enterOfSlots": "Enter # of slots", "@enterOfSlots": {}, "small": "Small", "@small": {}, "medium": "Medium", "@medium": {}, "pickup": "Pickup", "@pickup": {}, "capacityToLoadInoperableWinch": "Capacity to load inoperable (winch)", "@capacityToLoadInoperableWinch": {}, "plates": "Plates", "@plates": {}, "enterPlateNumber": "Enter plate number", "@enterPlateNumber": {}, "economicNumber": "Economic Number", "@economicNumber": {}, "enterEconomicNumber": "Enter economic number", "@enterEconomicNumber": {}, "insuranceInfo": "Insurance info", "@insuranceInfo": {}, "policyNo": "Policy No.", "@policyNo": {}, "enterInsurancePolicyNo": "Enter insurance policy no.", "@enterInsurancePolicyNo": {}, "photosOptional": "Photos (Optional)", "@photosOptional": {}, "carHauler": "Car Hauler", "@carHauler": {}, "flatBed": "Flat Bed", "@flatBed": {}, "towTruck": "Tow Truck", "@towTruck": {}, "pleaseEnterPlateNumber": "Please enter plate number", "@pleaseEnterPlateNumber": {}, "pleaseEnterEconomicNumber": "Please enter economic number", "@pleaseEnterEconomicNumber": {}, "pleaseEnterInsurancePolicyNo": "Please enter insurance policy no", "@pleaseEnterInsurancePolicyNo": {}, "pleaseSelectEquipmentBrand": "Please select equipment brand", "@pleaseSelectEquipmentBrand": {}, "pleaseSelectEquipmentYear": "Please select equipment year", "@pleaseSelectEquipmentYear": {}, "pleaseSelectEquipmentVersion": "Please select equipment version", "@pleaseSelectEquipmentVersion": {}, "pleaseEnterEmail": "Please enter email", "@pleaseEnterEmail": {}, "pleaseEnterValidEmail": "Please enter valid email", "@pleaseEnterValidEmail": {}, "pleaseEnterCurrentPassword": "Please enter current password", "@pleaseEnterCurrentPassword": {}, "pleaseEnterPassword": "Please enter password", "@pleaseEnterPassword": {}, "passwordMinLength": "Password should be at least 8 characters.", "@passwordMinLength": {}, "otpMinLength": "Please enter valid OTP", "@otpMinLength": {}, "pleaseEnterValidUsername": "Please enter valid username", "@pleaseEnterValidUsername": {}, "usernameMinLength": "Please enter valid username", "@usernameMinLength": {}, "pleaseEnterName": "Please enter name", "@pleaseEnterName": {}, "nameMinLength": "Please enter name", "@nameMinLength": {}, "pleaseEnterFirstName": "Please enter first name", "@pleaseEnterFirstName": {}, "firstNameMinLength": "Please enter first name", "@firstNameMinLength": {}, "pleaseEnterLastName": "Please enter last name", "@pleaseEnterLastName": {}, "lastNameMinLength": "Please enter last name", "@lastNameMinLength": {}, "passwordDoesNotMatch": "Password does not match", "@passwordDoesNotMatch": {}, "checkYourEmail": "Check your email", "@checkYourEmail": {}, "sentVerificationCode": "We've sent a verification code on ", "@sentVerificationCode": {}, "verifyOtp": "Verify OTP", "@verifyOtp": {}, "pleaseEnterOtp": "Please enter OTP!", "@pleaseEnterOtp": {}, "pleaseEnterValidOtp": "Please enter valid OTP!", "@pleaseEnterValidOtp": {}, "didNotReceiveEmail": "Didn't received the email? ", "@didNotReceiveEmail": {}, "clickToResend": "Click to resend", "@clickToResend": {}, "resetPasswordInstructions": "We'll send you reset password instructions", "@resetPasswordInstructions": {}, "sendAnEmail": "Send an email", "@sendAnEmail": {}, "ultimateStressFreeCarTransport": "The ultimate stress-free car\ntransport experience.", "@ultimateStressFreeCarTransport": {}, "email": "Email", "@email": {}, "enterYourEmail": "Enter your email", "@enterYourEmail": {}, "password": "Password", "@password": {}, "enterYourPassword": "Enter your password", "@enterYourPassword": {}, "forgotPassword": "Forgot Password", "@forgotPassword": {}, "forgotPasswordQuestion": "Forgot Password?", "@forgotPasswordQuestion": {}, "logIn": "Log In", "@logIn": {}, "doNotHaveAccount": "Don't have an account?", "@doNotHaveAccount": {}, "signUp": "Sign Up", "@signUp": {}, "setPassword": "Set password", "@setPassword": {}, "goAheadAndSetANewPassword": "Go ahead and set a new password", "@goAheadAndSetANewPassword": {}, "confirmPassword": "Confirm Password", "@confirmPassword": {}, "pleaseEnterConfirmPassword": "Please enter confirm password", "@pleaseEnterConfirmPassword": {}, "pleaseEnterDetails": "Please enter your details to create an account", "@pleaseEnterDetails": {}, "name": "Name", "@name": {}, "enterYourName": "Enter your name", "@enterYourName": {}, "createAccount": "Create an account", "@createAccount": {}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {}, "customer_support": "Customer Support", "@customer_support": {}, "payments": "Payments", "@payments": {}, "past_purchase": "Past Purchase", "@past_purchase": {}, "sign_out": "Sign Out", "@sign_out": {}, "delete_account": "Delete Account", "@delete_account": {}, "edit_details": "Edit Details", "@edit_details": {}, "change_password": "Change Password", "@change_password": {}, "save": "Save", "@save": {}, "enter_your_name": "Enter your name", "@enter_your_name": {}, "enter_your_email": "Enter your email", "@enter_your_email": {}, "enter_your_password": "Enter your password", "@enter_your_password": {}, "set_new_password": "Set new password", "@set_new_password": {}, "go_ahead_and_set_a_new_password": "Go ahead and set a new password", "@go_ahead_and_set_a_new_password": {}, "old_password": "Old Password", "@old_password": {}, "new_password": "New Password", "@new_password": {}, "confirm_password": "Confirm Password", "@confirm_password": {}, "please_enter_confirm_password": "Please enter confirm password", "@please_enter_confirm_password": {}, "transporter": "Transporter", "@transporter": {}, "total_trip_cost": "Total Trip Cost", "@total_trip_cost": {}, "no_of_vehicles": "No. Of Vehicles", "@no_of_vehicles": {}, "equipment_type": "Equipment type", "@equipment_type": {}, "vehicles_info": "Vehicles Info", "@vehicles_info": {}, "details": "Details", "@details": {}, "car_brand": "Car brand", "@car_brand": {}, "car_model": "Car model", "@car_model": {}, "car_serial": "Car serial #", "@car_serial": {}, "car_year": "Car year", "@car_year": {}, "close": "Close", "@close": {}, "view_details": "View Details", "@view_details": {}, "drivers": "Drivers", "@drivers": {}, "signOutTitle": "Sign Out", "@signOutTitle": {}, "signOutContent": "Are you sure you want to Sign Out?", "@signOutContent": {}, "signOutDefaultAction": "Sign Out", "@signOutDefaultAction": {}, "signOutCancelAction": "Cancel", "@signOutCancelAction": {}, "deleteAccountTitle": "Delete Account", "@deleteAccountTitle": {}, "deleteAccountContent": "Are you sure you want to delete this account?", "@deleteAccountContent": {}, "delete": "Delete", "@delete": {}, "cancel": "Cancel", "@cancel": {}, "uploadPicturesTitle": "Upload Pictures of your equipment", "@uploadPicturesTitle": {}, "uploadPicturesDescription": "Tap this and choose the pictures you want\nto upload from your device", "@uploadPicturesDescription": {}, "chooseAnAction": "Choose an action", "@chooseAnAction": {}, "camera": "Camera", "@camera": {}, "gallery": "Gallery", "@gallery": {}, "maxImagesError": "Maximum 7 images can be selected.", "@maxImagesError": {}, "equipmentUpdateSuccess": "Equipment updated successfully!", "@equipmentUpdateSuccess": {}, "equipmentAddSuccess": "Equipment added successfully!", "@equipmentAddSuccess": {}, "updateEquipment": "Update Equipment", "@updateEquipment": {}, "equipmentDeletedSuccess": "Equipment deleted successfully.", "@equipmentDeletedSuccess": {}, "homeTitle": "Home", "@homeTitle": {}, "viewRoutes": "View Routes", "@viewRoutes": {}, "upcomingTrips": "Upcoming Trips", "@upcomingTrips": {}, "requestedTrips": "Requested Trips", "@requestedTrips": {}, "oTPSentSuccessfully": "OTP sent successfully!", "@oTPSentSuccessfully": {}, "driverCreatedSuccessfully": "Driver created successfully.", "@driverCreatedSuccessfully": {}, "driverDeletedSuccessfully": "Driver deleted successfully.", "@driverDeletedSuccessfully": {}, "passwordChangedSuccessfully": "Password changed successfully.", "@passwordChangedSuccessfully": {}, "newRoute": "New Route", "@newRoute": {}, "savedRoute": "Saved Route", "@savedRoute": {}, "searchOriginLocation": "Search Origin location", "@searchOriginLocation": {}, "searchDropLocation": "Search Drop location", "@searchDropLocation": {}, "stockLocations": "Stock Locations", "@stockLocations": {}, "originStockLocation": "Origin Stock Location", "@originStockLocation": {}, "dropStockLocation": "Drop Stock Location", "@dropStockLocation": {}, "tripInfo": "Trip Info", "@tripInfo": {}, "selectEquipment": "Select equipment", "@selectEquipment": {}, "chooseEquipment": "Choose equipment", "@chooseEquipment": {}, "enterAvailableSlots": "Enter # of available slots", "@enterAvailableSlots": {}, "changeOfferPrice": "Change Offer Price", "@changeOfferPrice": {}, "currentMaxPrice": "Current Maximum Price", "@currentMaxPrice": {}, "currentMinPrice": "Current Minimum Price", "@currentMinPrice": {}, "exclusiveTrip": "Exclusive Trip", "@exclusiveTrip": {"description": "Label for exclusive trip type"}, "reportProblem": "Report Problem", "@reportProblem": {}, "totalTripCost": "Total Trip Cost", "@totalTripCost": {}, "perCar": "  {price}/Car", "@perCar": {"placeholders": {"price": {"type": "String"}}}, "allTrips": "All Trips", "@allTrips": {}, "stops": "Stops", "@stops": {}, "companyNameOnTrack": "OnTrack Transportation", "@companyNameOnTrack": {}, "tripCost": "{price}", "@tripCost": {"placeholders": {"price": {"type": "String"}}}, "perCarCost": "  {price}/Car", "@perCarCost": {"placeholders": {"price": {"type": "String"}}}, "availableSlots": "Available Slots", "@availableSlots": {}, "edit": "Edit", "@edit": {}, "driver": "Driver", "@driver": {}, "equipment": "Equipment", "@equipment": {}, "businessDetails": "Business details", "@businessDetails": {}, "loginSuccessfully": "<PERSON><PERSON> successfully", "@loginSuccessfully": {}, "yourAccountInReview": "Your account is in under review, once admin will approve then you will able to access the app", "@yourAccountInReview": {}, "validity": "Validity", "@validity": {}, "enterValidityDate": "Enter validity date", "@enterValidityDate": {}, "pleaseEnterValidityDate": "Please enter validity date", "@pleaseEnterValidityDate": {}, "noEquipmentsAvailable": "No equipments available", "@noEquipmentsAvailable": {}, "deleteEquipment": "Delete equipment", "@deleteEquipment": {}, "deleteEquipmentConfirmation": "Are you sure you want to delete this equipment?", "@deleteEquipmentConfirmation": {}, "yes": "Yes", "@yes": {}, "createTrip": "Create trip", "@createTrip": {}, "editTrip": "Edit trip", "@editTrip": {}, "makeTripLive": "Make Trip Live", "@makeTripLive": {}, "pleaseChooseOriginDropLocation": "Please choose origin and drop location", "@pleaseChooseOriginDropLocation": {}, "chooseOriginStockLocation": "Choose origin stock location", "@chooseOriginStockLocation": {}, "chooseDropStockLocation": "Choose drop stock location", "@chooseDropStockLocation": {}, "pleaseEnterAvailableSlot": "Please enter available slot", "@pleaseEnterAvailableSlot": {}, "pleaseEnterValidSlot": "Please enter valid available slot, Your selected vehicle has only {slot} slot", "@pleaseEnterValidSlot": {"placeholders": {"slot": {"type": "int"}}}, "selectDriver": "Select driver", "@selectDriver": {}, "chooseDriver": "Choose driver", "@chooseDriver": {}, "changeDriverNote": "Changing driver will replace the current assignment", "@changeDriverNote": {}, "pleaseEnterTripStartDate": "Please enter trip start date", "@pleaseEnterTripStartDate": {}, "pleaseEnterTripEndDate": "Please enter trip end date", "@pleaseEnterTripEndDate": {}, "pleaseSelectAtLeastLocation": "You have enabled intermediate pickup, So please select at least one intermediate location", "@pleaseSelectAtLeastLocation": {}, "pleaseEnterKmCharge": "Please enter km charge", "@pleaseEnterKmCharge": {}, "pleaseEnterTotalCost": "Please enter total cost", "@pleaseEnterTotalCost": {}, "tripUpdatedSuccess": "Trip updated successfully", "@tripUpdatedSuccess": {}, "tripCreatedSuccess": "Trip created successfully", "@tripCreatedSuccess": {}, "noSavedRouteYet": "No saved routes yet", "@noSavedRouteYet": {}, "stopsAtMajorCities": "Stops at major cities", "@stopsAtMajorCities": {}, "collectionAndDeliveryDescription": "The collection and delivery of units will take place at established storage facilities to be more time-efficient.", "@collectionAndDeliveryDescription": {}, "tripStartAndEndDates": "Trip start and end dates", "@tripStartAndEndDates": {}, "startDate": "Start date", "@startDate": {}, "endDate": "End date", "@endDate": {}, "intermediateStopsDates": "Intermediate stops and dates", "@intermediateStopsDates": {}, "dateAndTimeFor": "Date and time for", "@dateAndTimeFor": {}, "selectStartEndDate": "Select start and end date", "@selectStartEndDate": {}, "saveThisRoute": "Save this route", "@saveThisRoute": {}, "allowIntermediatePickup": "Allow intermediate pickup", "@allowIntermediatePickup": {}, "enterPerKm": "Enter $ per K.M.", "@enterPerKm": {}, "totalCostOfTrip": "Total cost of trip", "@totalCostOfTrip": {}, "allNotifications": "All notifications", "@allNotifications": {}, "noNotificationsFound": "No notifications found", "@noNotificationsFound": {}, "myProfile": "My profile", "@myProfile": {}, "stripeDashboard": "Stripe dashboard", "@stripeDashboard": {}, "requests": "Requests", "@requests": {}, "accepted": "Accepted", "@accepted": {}, "noTripLiveYet": "No trip live yet", "@noTripLiveYet": {}, "noRoutesSavedYet": "No routes saved yet", "@noRoutesSavedYet": {}, "noTripCompletedYet": "No trip completed yet", "@noTripCompletedYet": {}, "noTripStartedYet": "No trip started yet", "@noTripStartedYet": {}, "currentTripStatus": "Current Trip Status", "@currentTripStatus": {}, "noChecklistPerformedYet": "No check list performed yet", "@noChecklistPerformedYet": {}, "noUpcomingTripAvailableYet": "No Upcoming trip available yet!", "@noUpcomingTripAvailableYet": {}, "noOngoingTripFound": "No ongoing trip found", "@noOngoingTripFound": {}, "noExclusiveTripsFound": "No Exclusive Trips Found", "@noExclusiveTripsFound": {}, "noSentOfferYet": "No sent offer yet", "@noSentOfferYet": {}, "selectLanguage": "Select Language", "@selectLanguage": {}, "allLiveTrips": "All Live Trips", "@allLiveTrips": {}, "deleteDriver": "Delete Driver", "@deleteDriver": {}, "deleteDriverConfirmation": "Are you sure you want to delete the driver?", "@deleteDriverConfirmation": {}, "noDriverAvailableYet": "No driver available yet!", "@noDriverAvailableYet": {}, "upcoming": "Upcoming", "@upcoming": {}, "ongoing": "Ongoing", "@ongoing": {}, "addChecklist": "Add Checklist", "@addChecklist": {}, "checklist": "Checklist", "@checklist": {}, "clientName": "Client Name", "@clientName": {}, "vehiclesInfo": "Vehicles Info", "@vehiclesInfo": {}, "checklistType": "Checklist Type", "@checklistType": {}, "mileageAtPickup": "Mileage at Pickup (In miles/hr)", "@mileageAtPickup": {}, "mileageAtDelivery": "Mileage at Delivery (In miles/hr)", "@mileageAtDelivery": {}, "pickupDatePlace": "Pickup Date & Place", "@pickupDatePlace": {}, "deliveryDatePlace": "Delivery Date & Place", "@deliveryDatePlace": {}, "fuelLevel": "Fuel level", "@fuelLevel": {}, "low": "Low", "@low": {}, "half": "Half", "@half": {}, "high": "High", "@high": {}, "notes": "Notes", "@notes": {}, "confirmShipment": "Confirm Shipment", "@confirmShipment": {}, "fromPickup": "From Pickup", "@fromPickup": {}, "fromDrop": "From Drop", "@fromDrop": {}, "addPickupChecklistFirst": "Please add checklist for pickup first", "@addPickupChecklistFirst": {}, "performedDuring": "Performed During", "@performedDuring": {}, "exterior": "Exterior", "@exterior": {}, "mainLights": "Main Lights", "@mainLights": {}, "mediumLights": "Medium Lights", "@mediumLights": {}, "stopLightOrTurnSignals": "Stop Light/Turn Signals", "@stopLightOrTurnSignals": {}, "radioAntenna": "Radio Antenna", "@radioAntenna": {}, "pairOfWindshieldWipers": "Pair of Windshield Wipers", "@pairOfWindshieldWipers": {}, "rightSideMirror": "Right Side Mirror", "@rightSideMirror": {}, "sideWindows": "Side Windows", "@sideWindows": {}, "windshield": "Windshield", "@windshield": {}, "rearWindow": "Rear Windows", "@rearWindow": {}, "fourWheelCaps": "4 Wheels Caps", "@fourWheelCaps": {}, "bodyWithoutDents": "Body Without Dents", "@bodyWithoutDents": {}, "frontBumper": "Front Bumper", "@frontBumper": {}, "rearBumper": "Rear Bumper", "@rearBumper": {}, "frontLicensePlate": "Front License Plate", "@frontLicensePlate": {}, "rearLicensePlate": "Rear License Plate", "@rearLicensePlate": {}, "interior": "Interior", "@interior": {}, "heating": "Heating", "@heating": {}, "radio": "Radio", "@radio": {}, "speakers": "Speakers", "@speakers": {}, "lighter": "Lighter", "@lighter": {}, "rearViewMirror": "Rear View Mirror", "@rearViewMirror": {}, "ashtrays": "Ashtrays", "@ashtrays": {}, "seatBelts": "Seat Belt", "@seatBelts": {}, "windowHandles": "Window Handles", "@windowHandles": {}, "rubberFloors": "Rubber Floors", "@rubberFloors": {}, "seatCovers": "Seat Covers", "@seatCovers": {}, "doorHandles": "<PERSON> Handles", "@doorHandles": {}, "holders": "Holders", "@holders": {}, "engine": "Engine", "@engine": {}, "floorMats": "Floor Mats", "@floorMats": {}, "accessories": "Accessories", "@accessories": {}, "jack": "<PERSON>", "@jack": {}, "wheelWrench": "Wheel Wrench", "@wheelWrench": {}, "toolKit": "Tool kit", "@toolKit": {}, "triangle": "Triangle", "@triangle": {}, "spareTire": "Spare tire", "@spareTire": {}, "fireExtinguisher": "Fire Extinguisher", "@fireExtinguisher": {}, "damageWear": "Damage / Wear", "@damageWear": {}, "scratchedPaint": "<PERSON><PERSON><PERSON>", "@scratchedPaint": {}, "brokenWindows": "Broken Windows", "@brokenWindows": {}, "dents": "<PERSON><PERSON>", "@dents": {}, "suspension": "Suspension", "@suspension": {}, "description": "Description", "@description": {}, "pleaseEnterDescription": "Please enter description", "@pleaseEnterDescription": {}, "explainInBrief": "Explain in brief", "@explainInBrief": {}, "enterAffectedTime": "Enter affected time", "@enterAffectedTime": {}, "inHours": "in hours", "@inHours": {}, "pleaseEnterAffectedTime": "Please enter affected time", "@pleaseEnterAffectedTime": {}, "submitReport": "Submit Report", "@submitReport": {}, "uploadCarImages": "Upload car images", "@uploadCarImages": {}, "fromLeftSide": "From left side", "@fromLeftSide": {}, "fromRightSide": "From right side", "@fromRightSide": {}, "fromFrontSide": "From front side", "@fromFrontSide": {}, "fromBackSide": "From back side", "@fromBackSide": {}, "fromOtherSide": "Other Images", "@fromOtherSide": {}, "carBrand": "Car brand", "@carBrand": {}, "carSize": "Car Size", "@carSize": {}, "carModel": "Car model", "@carModel": {}, "carYear": "Car year", "@carYear": {}, "carSerial": "Car serial #", "@carSerial": {}, "carCondition": "Car Condition #", "@carCondition": {}, "cancelBooking": "Cancel Booking", "@cancelBooking": {}, "totalVehicle": "Total Vehicle", "@totalVehicle": {}, "stopLocations": "Stop Locations", "@stopLocations": {}, "slot": "Slot", "@slot": {}, "allShipments": "All Shipments", "@allShipments": {}, "completeTrip": "Complete Trip", "@completeTrip": {}, "startTrip": "Start Trip", "@startTrip": {}, "bookingDetail": "Booking Detail", "@bookingDetail": {}, "pickFrom": "Pick from", "@pickFrom": {}, "dropAt": "Drop at", "@dropAt": {}, "cancelTrip": "Cancel Trip", "@cancelTrip": {}, "customer": "Customer", "@customer": {}, "waitingList": "Waiting List", "@waitingList": {}, "sentOffers": "<PERSON><PERSON>", "@sentOffers": {}, "personalizedServiceText": "A personalized service where the entire fleet is dedicated to a single client, offering flexibility in pickup, delivery, and scheduling. While more expensive than shared trips, it ensures exclusivity.", "@personalizedServiceText": {}, "tripStartEndDates": "Trip Start & End Dates", "@tripStartEndDates": {}, "enterCost": "Enter cost", "@enterCost": {}, "perKm": "(km/slot)", "@perKm": {}, "perSlot": "(per slot)", "@perSlot": {}, "totalCost": "Total cost(in $)", "@totalCost": {}, "enterTotalCost": "Enter total cost", "@enterTotalCost": {}, "sendOffer": "Send Offer", "@sendOffer": {}, "days": "Days", "@days": {}, "hr": "hr", "@hr": {}, "carsWantedToMove": "Cars wanted to Move", "@carsWantedToMove": {}, "yourCurrentBid": "Your Current Bid:", "@yourCurrentBid": {}, "yourCurrentSpot": "Your Current Spot:", "@yourCurrentSpot": {}, "listOfCurrentOffers": "List of Current Offers", "@listOfCurrentOffers": {}, "avgPrice": "Avg. <PERSON>", "@avgPrice": {}, "provider": "Provider", "@provider": {}, "you": "You", "@you": {}, "enterYourAmount": "Enter your amount", "@enterYourAmount": {}, "offerAmount": "Offer Amount", "@offerAmount": {}, "deadlineOfferDate": "Deadline offer date", "@deadlineOfferDate": {}, "pleaseSelectEquipment": "Please select equipment", "@pleaseSelectEquipment": {}, "reservationSpotGreaterThanZero": "Spot available for reservation should be greater than 0", "@reservationSpotGreaterThanZero": {}, "pleaseSelectDriver": "Please select driver", "@pleaseSelectDriver": {}, "costPerKmGreaterThanZero": "Cost per kilometer should be greater than 0", "@costPerKmGreaterThanZero": {}, "tripDistanceGreaterThanZero": "Total trip distance should be greater than 0", "@tripDistanceGreaterThanZero": {}, "offerSentSuccessfully": "Offer sent successfully to user", "@offerSentSuccessfully": {}, "exclusiveTripRejectedSuccessfully": "Exclusive trip rejected successfully", "@exclusiveTripRejectedSuccessfully": {}, "offer": "Offer", "@offer": {}, "notInterested": "Not Interested", "@notInterested": {}, "availableSlot": "Available slot", "@availableSlot": {}, "enterAvailableSlot": "Enter available slot", "@enterAvailableSlot": {}, "report": "Report", "@report": {}, "viewLess": "View less", "@viewLess": {}, "viewMore": "View more", "@viewMore": {}, "selectChecklistType": "Please select checklist type", "@selectChecklistType": {}, "enterVehicleMileage": "Please enter vehicle mileage", "@enterVehicleMileage": {}, "enterVehicleFuelLevel": "Please enter vehicle fuel level", "@enterVehicleFuelLevel": {}, "addVehicleImages": "Please add vehicle images", "@addVehicleImages": {}, "deliveredTo": "Delivered to", "@deliveredTo": {}, "youHaveDeliveredThisShipmentOn": "You have delivered this shipment on", "@youHaveDeliveredThisShipmentOn": {}, "vehicleBrand": "Vehicle brand", "@vehicleBrand": {}, "vehicleModel": "Vehicle model", "@vehicleModel": {}, "vehicleYear": "Vehicle year", "@vehicleYear": {}, "vehicleSize": "Vehicle size", "@vehicleSize": {}, "vehicleSerialNumeric": "Vehicle serial number", "@vehicleSerialNumeric": {}, "vehicleDescription": "Vehicle description", "@vehicleDescription": {}, "isWinchRequired": "Is winch required?", "@isWinchRequired": {}, "openMap": "Open Map", "@openMap": {}, "imagesNotUploaded": "Images are not uploaded", "@imagesNotUploaded": {}, "tripDistanceIsZeroPleaseSelectOriginDropLocation": "Trip distance is 0, please select origin and drop location", "@tripDistanceIsZeroPleaseSelectOriginDropLocation": {}, "pleaseDescribeIssue": "Please describe the issue you are facing..", "@pleaseDescribeIssue": {}, "yourComplainSubmittedSuccessfully": "Your complain submitted successfully, We will get back to you soon.", "@yourComplainSubmittedSuccessfully": {}, "submit": "Submit", "@submit": {}, "writeUrMessageHere": "Write your message here", "@writeUrMessageHere": {}, "chat": "Cha<PERSON>", "@chat": {}, "chatIsInactive": "<PERSON><PERSON> is inactive, You can not chat here anymore!", "@chatIsInactive": {}, "locationServicesDisabled": "Location services are disabled. Please enable them to use this feature.", "@locationServicesDisabled": {}, "locationPermissionsDenied": "Location permissions are denied. Please enable them to use this feature.", "@locationPermissionsDenied": {}, "locationPermissionsDeniedForever": "Location permissions are permanently denied. Please enable them in settings.", "@locationPermissionsDeniedForever": {}, "failedToGetLocation": "Failed to get current location. Please try again.", "@failedToGetLocation": {}, "searchAddress": "Search Address", "@searchAddress": {}, "search": "Search", "@search": {}, "selectAnotherAddress": "Something went wrong, please select another address", "@selectAnotherAddress": {}, "somethingWentWrong": "Something went wrong, please try again", "@somethingWentWrong": {}, "selectLocation": "Select Location", "@selectLocation": {}, "selectLocationOnMap": "Please select a location on the map", "@selectLocationOnMap": {}, "failedToGetLocationDetails": "Failed to get location details", "@failedToGetLocationDetails": {}, "select": "Select", "@select": {}, "noMessagesYet": "No messages yet", "@noMessagesYet": {}, "stopLocation": "Stop Location", "@stopLocation": {}, "readMore": "read more", "@readMore": {}, "readLess": "read less", "@readLess": {}, "youHadRejectedThisTrip": "You had rejected this trip,\nWant to send offer?", "@youHadRejectedThisTrip": {}, "pleaseSelectedStartAndEndDate": "Please select start and end date of trip", "@pleaseSelectedStartAndEndDate": {}, "noDriversAvailableOnSelectedDate": "No drivers available on selected date", "@noDriversAvailableOnSelectedDate": {}, "noEquipmentAvailableOnSelectedDate": "No equipment available on selected date", "@noEquipmentAvailableOnSelectedDate": {}, "noStockLocationFoundPleaseChooseAnotherAddress": "No stock location found, please choose another location", "@noStockLocationFoundPleaseChooseAnotherAddress": {}, "contactNumber": "Contact Number", "@contactNumber": {}, "explainProblem": "Explain Problem", "@explainProblem": {}, "noTripDataFound": "No trip data found", "@noTripDataFound": {}, "affectedTime": "Affected time", "@affectedTime": {}, "hour": "hour", "@hour": {}, "reportsFromTransporter": "Reports from transporter", "@reportsFromTransporter": {}, "day": "day", "@day": {}, "readyOfCollect": "Ready to collect", "@readyOfCollect": {}, "outForDelivery": "Out for delivery", "@outForDelivery": {}, "pleaseWaitWhileWeRedirectingYouToStripPortalForVerification": "Please wait while we redirecting you to strip portal for verification..", "@pleaseWaitWhileWeRedirectingYouToStripPortalForVerification": {}, "pleaseSearchOriginLocationToViewStockLocations": "Please search origin location, To view stock locations.", "@pleaseSearchOriginLocationToViewStockLocations": {}, "pleaseSearchDropLocationToViewStockLocations": "Please search drop location, To view stock locations.", "@pleaseSearchDropLocationToViewStockLocations": {}, "pleaseEnterEstimatedDateFor": "Please enter estimated date for", "@pleaseEnterEstimatedDateFor": {}, "pleaseEnterEstimatedTimeFor": "Please enter estimated time for", "@pleaseEnterEstimatedTimeFor": {}, "customiseRoute": "Customise Route", "@customiseRoute": {}, "addStopsWithoutMarkingThemAsIntermediate": "Add stops without marking them as intermediate stop.", "@addStopsWithoutMarkingThemAsIntermediate": {}, "customise": "Customise", "@customise": {}, "thisLocationIsAlreadyAddedAsStop": "This location is already added as a stop.", "@thisLocationIsAlreadyAddedAsStop": {}, "customStopPoints": "Custom stop points", "@customStopPoints": {}, "addStopPoint": "Add stop point", "@addStopPoint": {}, "tripOfferSent": "Trip offer price updated successfully.", "@tripOfferSent": {}, "customerBookingStatus": "Customer booking status", "@customerBookingStatus": {}, "saveChecklist": "Save checklist", "@saveChecklist": {}, "noAnyEquipmentDriver": "No any {equipmentOrDriver} available on this date.", "@noAnyEquipmentDriver": {"placeholders": {"equipmentOrDriver": {"type": "String"}}}, "checklistCreatedSuccessfully": "Checklist created successfully", "@checklistCreatedSuccessfully": {}, "yourEquipmentRoute": "Your Equipment Route", "@yourEquipmentRoute": {}, "vehicleImages": "Vehicle images", "@vehicleImages": {}, "to": "to", "@to": {}, "carOwnerComments": "Car Owner comments", "@carOwnerComments": {}, "full": "Full", "@full": {}, "noCommentsAdded": "No comments added", "@noCommentsAdded": {}, "noImagesAdded": "No images added", "@noImagesAdded": {}, "pendingVerification": "Pending Verification", "@pendingVerification": {}, "noPendingChecklistsFound": "No Pending Checklists Found", "@noPendingChecklistsFound": {}, "allChecklistsHaveBeenVerified": "All checklists have been verified or there are no checklists to review.", "@allChecklistsHaveBeenVerified": {}, "completionProgress": "Completion Progress", "@completionProgress": {}, "mileage": "Mileage", "@mileage": {}, "images": "images", "@images": {}, "image": "image", "@image": {}}