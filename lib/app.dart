import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/l10n/app_localizations.dart';
import 'package:transportmatch_provider/localizations/supported_locales.dart';
import 'package:transportmatch_provider/presentation/provider/initial_app_provider.dart';
import 'package:transportmatch_provider/router/app_router.dart';
import 'package:transportmatch_provider/style/custom_theme.dart';

/// App Initialization
class MyApp extends StatelessWidget {
  /// App Initialization constructor
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => Injector.instance.get<AppProvider>(),
        ),
      ],
      child: ScreenUtilInit(
        minTextAdapt: true,
        splitScreenMode: true,
        designSize: const Size(375, 812),
        builder: (_, child) => Consumer<AppProvider>(
          builder: (context, appProvider, child) {
            return MaterialApp.router(
              title: 'Transport Match Provider',
              debugShowCheckedModeBanner: false,
              theme: CustomTheme().light,
              themeMode: ThemeMode.light,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              locale: appProvider.locale,
              supportedLocales: SupportedLocales.all,
              routerConfig: AppRouter.router,
            );
          },
        ),
      ),
    );
  }
}
