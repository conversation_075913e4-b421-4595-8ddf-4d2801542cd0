// ignore_for_file: lines_longer_than_80_chars

import 'package:flutter/material.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/fonts.gen.dart';

/// Theme Preferences Model
abstract class ThemePreferences {
  /// dark theme type
  ThemeData get dark;

  /// light theme type
  ThemeData get light;
}

/// Theme Customization
class CustomTheme extends ThemePreferences {
  @override
  ThemeData get light => ThemeData(
        fontFamily: AppFontFamily.inter,
        scaffoldBackgroundColor: AppColors.white,
        dialogTheme: const DialogThemeData(
          backgroundColor: Colors.white,
          titleTextStyle: TextStyle(
            color: AppColors.black,
            fontFamily: AppFontFamily.inter,
          ),
          contentTextStyle: TextStyle(
            color: AppColors.black,
            fontFamily: AppFontFamily.inter,
          ),
        ),
        colorScheme: const ColorScheme(
          onPrimary: AppColors.black,
          brightness: Brightness.light,
          onSecondary: AppColors.black,
          surface: AppColors.pageBGColor,
          onError: AppColors.primaryColor,
          onSurface: AppColors.black,
          error: AppColors.errorColor,
          primary: AppColors.primaryColor,
          secondary: AppColors.white,
          surfaceContainerHighest: AppColors.white,
        ),
        datePickerTheme: const DatePickerThemeData(
          backgroundColor: AppColors.pageBGColor,
        ),
        primaryColor: AppColors.primaryColor,
        primaryColorDark: AppColors.primaryColor,
        appBarTheme: const AppBarTheme(
          iconTheme: IconThemeData(color: AppColors.black),
          backgroundColor: AppColors.white,
          surfaceTintColor: Colors.transparent,
        ),
        scrollbarTheme: ScrollbarThemeData(
          interactive: true,
          // thumbVisibility: WidgetStateProperty.all(true),
          radius: Radius.circular(AppSize.r10),
          // thumbColor: WidgetStateProperty.all(Colors.grey),
          // thickness: WidgetStateProperty.all(AppSize.sp4),
          minThumbLength: 100,
        ),
        highlightColor: Colors.transparent,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            minimumSize: Size(
              double.infinity,
              AppSize.h60,
            ),
            backgroundColor: AppColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSize.r14),
            ),
            textStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
              fontFamily: AppFontFamily.inter,
            ),
          ),
        ),
        // dividerColor: AppColors.mediumGrey,
        splashColor: AppColors.transparent,
        textTheme: TextTheme(
          headlineLarge: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp30,
            fontWeight: FontWeight.w700,
            fontFamily: AppFontFamily.inter,
          ),
          headlineMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp30,
            fontFamily: AppFontFamily.inter,
          ),
          displayLarge: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp22,
            fontWeight: FontWeight.w700,
            fontFamily: AppFontFamily.inter,
          ),
          displayMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp22,
            fontFamily: AppFontFamily.inter,
          ),
          titleLarge: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp18,
            fontWeight: FontWeight.w700,
            fontFamily: AppFontFamily.inter,
          ),
          titleMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp18,
            fontFamily: AppFontFamily.inter,
          ),
          bodyMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
        ),
        // shadowColor: AppColors.optionLineColor,
        disabledColor: Colors.grey,
        timePickerTheme: TimePickerThemeData(
          dayPeriodColor: AppColors.primaryColor,
          dayPeriodTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          helpTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          hourMinuteTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp24,
            fontWeight: FontWeight.w600,
            fontFamily: AppFontFamily.inter,
          ),
          timeSelectorSeparatorTextStyle: WidgetStatePropertyAll(
            TextStyle(
              color: AppColors.ff343A40,
              fontFamily: AppFontFamily.inter,
              fontSize: AppSize.sp32,
              height: 1.8,
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            hintStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
            labelStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSize.r14),
          ),
          dialTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
        ),
      );

  @override
  ThemeData get dark => ThemeData(
        fontFamily: AppFontFamily.inter,
        scaffoldBackgroundColor: AppColors.transparent,
        dialogTheme: const DialogThemeData(
          backgroundColor: Colors.white,
          titleTextStyle: TextStyle(color: AppColors.black),
          contentTextStyle: TextStyle(color: AppColors.black),
        ),
        colorScheme: const ColorScheme(
          onPrimary: AppColors.white,
          // background: AppColors.secondaryBGColor,
          surface: Color.fromARGB(255, 0, 0, 0),
          brightness: Brightness.dark,
          onSecondary: AppColors.white,
          onError: AppColors.primaryColor,
          onSurface: AppColors.white,
          // onBackground: AppColors.secondaryBGColor,
          error: AppColors.black,
          primary: AppColors.primaryColor,
          secondary: AppColors.black,
          // surfaceVariant: AppColors.secondaryBGColor,
        ),
        datePickerTheme: const DatePickerThemeData(
          backgroundColor: AppColors.pageBGColor,
        ),
        primaryColor: AppColors.primaryColor,
        primaryColorDark: AppColors.primaryColor,
        appBarTheme: const AppBarTheme(
          iconTheme: IconThemeData(color: AppColors.white),
          backgroundColor: AppColors.black,
        ),
        scrollbarTheme: ScrollbarThemeData(
          interactive: true,
          // thumbVisibility: MaterialStateProperty.all(true),
          radius: Radius.circular(AppSize.r10),
          // thumbColor: MaterialStateProperty.all(Colors.grey),
          // thickness: MaterialStateProperty.all(AppSize.sp4),
          minThumbLength: 100,
        ),
        highlightColor: Colors.transparent,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            minimumSize: Size(
              double.infinity,
              AppSize.h60,
            ),
            backgroundColor: AppColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSize.r14),
            ),
            textStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.white,
            ),
          ),
        ),
        // dividerColor: AppColors.mediumGrey,
        splashColor: AppColors.transparent,
        textTheme: TextTheme(
          headlineLarge: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp30,
            fontWeight: FontWeight.w700,
          ),
          headlineMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp30,
          ),
          displayLarge: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp22,
            fontWeight: FontWeight.w700,
          ),
          displayMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp22,
          ),
          titleLarge: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp18,
            fontWeight: FontWeight.w700,
          ),
          titleMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp18,
          ),
          bodyMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp14,
          ),
        ),
        // shadowColor: AppColors.optionLineColor,
        disabledColor: Colors.grey,
        timePickerTheme: TimePickerThemeData(
          dayPeriodColor: AppColors.primaryColor,
          dayPeriodTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          helpTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          hourMinuteTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp24,
            fontWeight: FontWeight.w600,
            fontFamily: AppFontFamily.inter,
          ),
          timeSelectorSeparatorTextStyle: WidgetStatePropertyAll(
            TextStyle(
              color: AppColors.ff343A40,
              fontFamily: AppFontFamily.inter,
              fontSize: AppSize.sp32,
              height: 1.8,
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            hintStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
            labelStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSize.r14),
          ),
          dialTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
        ),
      );
}
