// ignore_for_file: public_member_api_docs

import 'package:form_field_validator/form_field_validator.dart';
import 'package:transportmatch_provider/l10n/app_localizations.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';

AppLocalizations get l10n => rootNavKey.currentContext!.l10n;

class EmptyValueValidator extends TextFieldValidator {
  EmptyValueValidator({required String errorText}) : super(errorText);

  @override
  bool get ignoreEmptyValues => false;

  @override
  bool isValid(String? value) {
    return value!.trim().isNotEmpty;
  }

  @override
  String? call(String? value) {
    return isValid(value) ? null : errorText;
  }
}

MultiValidator emailValidator() => MultiValidator([
      EmptyValueValidator(errorText: l10n.pleaseEnterEmail),
      RequiredValidator(errorText: l10n.pleaseEnterEmail),
      EmailValidator(errorText: l10n.pleaseEnterValidEmail),
    ]);
MultiValidator currentPasswordValidator() => MultiValidator([
      RequiredValidator(errorText: l10n.pleaseEnterCurrentPassword),
    ]);
MultiValidator passwordValidator() => MultiValidator([
      RequiredValidator(errorText: l10n.pleaseEnterPassword),
      MinLengthValidator(8, errorText: l10n.passwordShouldBeAtLeast8Characters),
    ]);
// MultiValidator passwordValidator() => MultiValidator([
//       RequiredValidator(errorText: l10n.pleaseEnterPassword),
//       MinLengthValidator(
//         8,
//         errorText: 'Password must be at least 8 characters long',
//       ),
//       PatternValidator(
//         r'^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$',
//         errorText: 'Password must include a letter, a number, and a symbol.',
//       ),
//     ]);
MultiValidator otpValidator() => MultiValidator([
      EmptyValueValidator(errorText: l10n.pleaseEnterValidOTP),
      RequiredValidator(errorText: l10n.pleaseEnterValidOTP),
      MinLengthValidator(4, errorText: l10n.pleaseEnterValidOTP),
    ]);
MultiValidator userNameValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterValidUsername),
      RequiredValidator(errorText: l10n.pleaseEnterValidUsername),
      MinLengthValidator(1, errorText: l10n.pleaseEnterValidUsername),
    ]);
MultiValidator nameValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterName),
      RequiredValidator(errorText: l10n.pleaseEnterName),
      MinLengthValidator(1, errorText: l10n.pleaseEnterName),
    ]);
MultiValidator companyNameValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterCompanyName),
      RequiredValidator(errorText: l10n.pleaseEnterCompanyName),
      MinLengthValidator(1, errorText: l10n.pleaseEnterCompanyName),
    ]);
MultiValidator commercialNameValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterCommercialName),
      RequiredValidator(errorText: l10n.pleaseEnterCommercialName),
      MinLengthValidator(1, errorText: l10n.pleaseEnterCommercialName),
    ]);
MultiValidator taxIDValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterTaxID),
      RequiredValidator(errorText: l10n.pleaseEnterTaxID),
      MinLengthValidator(1, errorText: l10n.pleaseEnterTaxID),
    ]);
MultiValidator firstNameValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterFirstName),
      RequiredValidator(errorText: l10n.pleaseEnterFirstName),
      MinLengthValidator(1, errorText: l10n.pleaseEnterFirstName),
    ]);
MultiValidator lastNameValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterLastName),
      RequiredValidator(errorText: l10n.pleaseEnterLastName),
      MinLengthValidator(1, errorText: l10n.pleaseEnterLastName),
    ]);
MultiValidator userIdValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterUserID),
      RequiredValidator(errorText: l10n.pleaseEnterUserID),
      MinLengthValidator(1, errorText: l10n.pleaseEnterUserID),
    ]);
MultiValidator equipmentNameValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterEquipmentName),
      RequiredValidator(errorText: l10n.pleaseEnterEquipmentName),
      MinLengthValidator(1, errorText: l10n.pleaseEnterEquipmentName),
    ]);
MultiValidator equipmentPlateNumberValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterPlateNumber),
      RequiredValidator(errorText: l10n.pleaseEnterPlateNumber),
      MinLengthValidator(1, errorText: l10n.pleaseEnterPlateNumber),
    ]);
MultiValidator equipmentEconomicNumberValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterEconomicNumber),
      RequiredValidator(errorText: l10n.pleaseEnterEconomicNumber),
      MinLengthValidator(1, errorText: l10n.pleaseEnterEconomicNumber),
    ]);
MultiValidator equipmentValidityValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterValidityDate),
      RequiredValidator(errorText: l10n.pleaseEnterValidityDate),
      MinLengthValidator(1, errorText: l10n.pleaseEnterValidityDate),
    ]);
MultiValidator equipmentInsurancePolicyNumberValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterInsurancePolicyNo),
      RequiredValidator(errorText: l10n.pleaseEnterInsurancePolicyNo),
      MinLengthValidator(1, errorText: l10n.pleaseEnterInsurancePolicyNo),
    ]);
MultiValidator descriptionValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterDescription),
      RequiredValidator(errorText: l10n.pleaseEnterDescription),
      MinLengthValidator(1, errorText: l10n.pleaseEnterDescription),
    ]);
MultiValidator equipmentBrandValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseSelectEquipmentBrand),
      RequiredValidator(errorText: l10n.pleaseSelectEquipmentBrand),
    ]);
MultiValidator equipmentYearValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseSelectEquipmentYear),
      RequiredValidator(errorText: l10n.pleaseSelectEquipmentYear),
    ]);
MultiValidator equipmentVersionValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseSelectEquipmentVersion),
      RequiredValidator(errorText: l10n.pleaseSelectEquipmentVersion),
    ]);
MultiValidator equipmentSlotValidator() => MultiValidator([
  EmptyValueValidator(errorText: l10n.pleaseEnterAvailableSlot),
      RequiredValidator(errorText: l10n.pleaseEnterAvailableSlot),
    ]);
