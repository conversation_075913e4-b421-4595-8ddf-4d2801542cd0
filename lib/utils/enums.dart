// App enum will be declared here

// ignore_for_file: constant_identifier_names, public_member_api_docs

/// enum DeviceType :: IOS, ANDROID, WEB
enum DeviceType { IOS, ANDROID }

/// enum demo
enum Demo { step1, step2, step3 }

enum EquipmentType { CAR_HAULER, FLAT_BED, TOW_TRUCK }

/// dummy enum
extension MeetingTypeExtension on Demo {
  String get name {
    switch (this) {
      case Demo.step1:
        return 'step1';
      case Demo.step2:
        return 'step2';
      case Demo.step3:
        return 'step3';
    }
  }
}

enum ImageTypes {
  PROFILE_PICTURES,
  BOOKING_ASSIGNEE_DOCUMENT,
  EQUIPMENT,
  CHECKLIST,
  COMPLAINT
}

enum VehicleStatus {
  CONFIRMED,
  RECEIVED_BY_ORIGIN_STOP_ADMIN,
  HANDED_OVER_TO_DRIVER,
  HANDED_OVER_TO_USER,
}

enum NotificationType {
  BOOKING,
  CONNECT_ACCOUNT,
  TRIP,
  BOOKING_DETAIL,
  EXCLUSIVE
}

enum UserType { Provider, Driver }

enum ChecklistStatus { ACCEPTED, PENDING }

enum TripType { SHARED, EXCLUSIVE }

enum AcceptedTripType {
  ACTIVE,
  ONGOING,
  COMPLETED,
}

enum BookingStatusType {
  IN_PROGRESS,
  CONFIRMED,
  CANCELLED_BY_CUSTOMER,
  CANCELLED_BY_PROVIDER,
  CANCELLED_BY_STOP_ADMIN,
  COMPLETED,
  ONGOING,
  FAILED,
  ALL_UNIT_RECEIVED_BY_DESTINATION_STOP_ADMIN,
  ALL_UNIT_READY_TO_COLLECT,
}
